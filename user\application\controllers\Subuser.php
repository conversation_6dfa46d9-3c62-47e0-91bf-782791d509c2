<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Subuser extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->model('SubUserModel');
    }

    function index()
    {
        return redirectToItSelf('all');
    }
    
    public function export() {
        $where = '1=1';
        $limit='';
        $accountStatus = $this->input->get('accountStatus');
        $accountStatus = jsDropdownSelectedValue($accountStatus);
        $isStatus = $this->input->get('isStatus');
        $isStatus = jsDropdownSelectedValue($isStatus);
        
        if ($accountStatus != '') {
            if ($where != ''):
                $where .= ' AND ( isAccountType = "' . $accountStatus . '")';
            else:
                $where = ' ( isAccountType = "' . $accountStatus . '")';
            endif;
        }
        
        if ($isStatus != '') {
            if ($isStatus == 'active') {
                if ($where != '') {
                    $where .= ' AND ( DATE(expiryDate) >= "' . dbDate(currentDate()) . '")';
                } else {
                    $where = ' ( DATE(expiryDate) >= "' . dbDate(currentDate()) . '")';
                }
                $orderbyField = 'activationDate';
            } else {
                if ($where != '') {
                    $where .= ' AND ( DATE(expiryDate) <= "' . dbDate(currentDate()) . '")';
                } else {
                    $where = ' ( DATE(expiryDate) <= "' . dbDate(currentDate()) . '")';
                }
                $orderbyField = 'expiryDate';
            }
        }
        
        $this->load->library('excel');
        $this->excel->writeAndDownload($this->viewDataBag->queries = $this->userModel->export($where,$limit), "Users-Data");
        return '';
    }

    function add_new()
    {
        $this->load->model('SubUserModel');
        if ($this->input->method() === 'post'):

            $user_id = $this->viewDataBag->userSession->id;
            $where = "userId = ".$user_id;
            $this->load->model('SubUserModel');
            $sub_users_count = $this->SubUserModel->count($where);
            if($sub_users_count>=2) {
                $this->alert->danger('Only 2 sub users are allowed to register');
                return $this->all();
            }

            $this->form_validation->set_rules('firstName', 'first name', 'trim|min_length[2]|max_length[40]|xss_clean|_isHumanName');
            $this->form_validation->set_rules('lastName', 'last name', 'trim|min_length[2]|max_length[40]|xss_clean|_isHumanName');
            $this->form_validation->set_rules('email', 'email', 'trim|valid_email|max_length[100]|_isUniqueExceptItself[tb_sub_user.email|id.id]|xss_clean');
            $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean|numeric|min_length[10]|max_length[16]|is_unique[tb_sub_user.mobile]');
            $this->form_validation->set_rules('newPassword', 'new password', 'required|_isPassword|differs[currentPassword]|xss_clean');
            $this->form_validation->set_rules('confirmNewPassword', 'confirm new password', 'required|_isPassword|matches[newPassword]|xss_clean');
            if ($this->form_validation->run() === true) {
                /* name section */
                $name = ["firstName" => ucwords($this->input->post('firstName')),
                    "lastName" => ucwords($this->input->post('lastName')),];
                $name["displayName"] = $name["firstName"] . " " . $name["lastName"];
                $name["urlName"] = strtolower($name["firstName"]) . "-" . strtolower($name["lastName"]);
                
                $user_id = $this->viewDataBag->userSession->id;
                /* input grab section */
                $userData = [
                    "name" => json_encode($name),
                    'password' => $this->input->makePasswordHash('newPassword'),
                    'userId' => $user_id,
                ];

                /* email or mobile varification section */
                if ($mobile = $this->input->post('mobile')) {
                    $verified["mobile"] = TRUE;
                    $userData["mobile"] = $mobile;
                }
                if ($email = $this->input->post('email')) {
                    $verified["email"] = TRUE;
                    $userData["email"] = $email;
                }
                $userData["verified"] = json_encode($verified);
                if ($this->SubUserModel->attach($userData)):
                    unset($updatedData);
                    $this->alert->success("Sub User has been created successfully.");
                else:
                    $this->alert->danger(getErrorMessage());
                endif;
                return redirectToCurrent();
            }
        endif;
        $this->viewDataBag->jsView = 'subuser/jsView';
        $this->loadView('subuser/addNewView', $this->viewDataBag);
    }

    function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $isStatus = $this->input->get('isStatus');
        $isStatus = jsDropdownSelectedValue($isStatus);
        $user_id = $this->viewDataBag->userSession->id;
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.createdOn';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.mobile like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.createdOn) >=  "' . $from . '" && DATE(t1.createdOn) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.createdOn) >=  "' . $from . '" && DATE(t1.createdOn) <=  "' . $to . '")';
            endif;
        }
        if ($isStatus != '') {
            if ($where != ''):
                $where .= ' AND ( t1.isStatus = "' . $isStatus . '")';
            else:
                $where = ' ( t1.isStatus = "' . $isStatus . '")';
            endif;
        }
        /*if ($isStatus == '' && $searchItem == '' && $from == '' && $to == '') {
            $where = [];
        }*/
        $perPage = 50;
        $this->load->model('SubUserModel');
        $totalRows = $this->SubUserModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $this->viewDataBag->users = $this->SubUserModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        $this->viewDataBag->jsView = 'subuser/jsView';
        $this->loadView('subuser/listView', $this->viewDataBag);
    }

    function detail($slug)
    {
        $id = slugId();
        if ($id) {
            $this->load->model('SubUserModel');
            $user = $this->SubUserModel->findOne(['id' => $id], 'id,name,email,mobile,verified,createdOn,isStatus,createdOn,appLoginHistory');
            if (isObject($user)) {
                $this->viewDataBag->user = $user;
                $this->viewDataBag->jsView = 'subuser/jsView';
                $this->loadView('subuser/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }

    function edit($slug)
    {
        $id = slugId();
        if ($id > 0) {
            $this->load->model('SubUserModel');
            if ($this->input->method() === 'post'):
                /* Validating form inputs */
                $this->form_validation->set_rules('id', 'id', 'trim|required');
                $this->form_validation->set_rules('name', 'name', 'trim');
                $this->form_validation->set_rules('firstName', 'frist name', 'trim|_isHumanName|xss_clean');
                $this->form_validation->set_rules('lastName', 'last name', 'trim|_isHumanName|xss_clean');
                $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|_isMobile|xss_clean|_isUniqueExceptItself[tb_sub_user.mobile|id.id]');
                $this->form_validation->set_rules('email', 'email', 'trim|valid_email|xss_clean|_isUniqueExceptItself[tb_sub_user.email|id.id]');

                if ($this->form_validation->run() === true) :
                    $id = $this->input->post('id');
                    $previousData = (array)dbJsonDecode($this->input->post('previousData'));

                    /* name section */
                    $name = ["firstName" => ucwords($this->input->post('firstName')),
                        "lastName" => ucwords($this->input->post('lastName')),];
                    $name["displayName"] = $name["firstName"] . " " . $name["lastName"];
                    $name["urlName"] = strtolower($name["firstName"]) . "-" . strtolower($name["lastName"]);

                    /* input grab section */
                    /* email or mobile varification section */
                    $verified = json_decode($previousData['verified']);
                    if ($mobile = $this->input->post('mobile')) {
                        if ($mobile != $previousData['mobile']) {
                            $verified->mobile = TRUE;
                        }
                        $updatedData["mobile"] = $mobile;
                    }


                    if ($email = $this->input->post('email')) {
                        if ($email != $previousData['email']) {
                            $verified->email = TRUE;
                        }
                        $updatedData["email"] = $email;
                    }

                    $updatedData["verified"] = json_encode($verified);
                    if ($this->SubUserModel->modify($updatedData, ['id' => $this->input->post('id')])) {
                        $this->alert->success("Sub User details has been updated successfully.");
                    } else {
                        $this->alert->danger(getErrorMessage());
                    }
                    unset($updatedData);
                    return redirectToItSelf(makeSlugURL('edit', $this->input->post("name"), $this->input->post('id')));
                endif;
            endif;

            $subuser = $this->SubUserModel->findOne(['id' => $id], 'id,name,email,mobile,verified');
            if (isObject($subuser)):
                $this->viewDataBag->subuser = $subuser;
                $this->viewDataBag->jsView = 'subuser/jsView';
                $this->loadView('subuser/editView', $this->viewDataBag);
            else :
                return show_404();
            endif;
        } else {
            return show_404();
        }
    }

    function change_password($slug)
    {
        $id = slugId();
        if ($id > 0) {
            $this->load->model('SubUserModel');
            if ($this->input->method() === 'post'):
                /* Validating form inputs */
                $this->form_validation->set_rules('id', 'id', 'trim|required');
                $this->form_validation->set_rules('name', 'name', 'trim');
                $this->form_validation->set_rules('displayName', 'displayName', 'trim');
                $this->form_validation->set_rules('mobile', 'mobile', 'trim|required');
                $this->form_validation->set_rules('newPassword', 'new password', 'required|_isPassword|differs[currentPassword]|xss_clean');
                $this->form_validation->set_rules('confirmNewPassword', 'confirm new password', 'required|_isPassword|matches[newPassword]|xss_clean');
                if ($this->form_validation->run() === true) :
                    /* input grab section */
                    $updatedData = [
                        'password' => $this->input->makePasswordHash('newPassword'),
                    ];
                    if ($this->SubUserModel->modify($updatedData, ['id' => $this->input->post('id')])) {
                        $name = $this->input->post('displayName');
                        $mobile = $this->input->post('mobile');
                        $this->alert->success("Sub User password has been updated successfully.");
                    } else {
                        $this->alert->danger(getErrorMessage());
                    }
                    unset($updatedData);
                    return redirectToItSelf(makeSlugURL('change-password', $this->input->post("name"), $this->input->post('id')));
                endif;
            endif;
            $user = $this->SubUserModel->findOne(['id' => $id], 'id,name,mobile');
            if (isObject($user)):
                $this->viewDataBag->subuser = $user;
                $this->viewDataBag->jsView = 'subuser/jsView';
                $this->loadView('subuser/changePasswordView', $this->viewDataBag);
            else :
                return show_404();
            endif;
        } else {
            return show_404();
        }
    }
}
