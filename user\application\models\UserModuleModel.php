<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class UserModuleModel extends MY_Model {

    protected $primaryTable = 'tb_user_modules';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	function check($module,$user_id) { {
             $this->db->select('*')
                    ->from($this->primaryTable . ' t1')
                    ->where('module',$module)
				 	->where('userId',$user_id)
                    ->where('status','active');                    
            
			//echo "<pre>"; print_r($this->getResult($this->db->get()));die;
            return $this->getResult($this->db->get());
        }
    }
	
	function get_array($where) {
		$this->db->select('module')
          ->from($this->primaryTable . ' t1');
		if ($where != '') {
           $this->db->where($where);
        }
		return $this->getResultArray($this->db->get());
	}
}
