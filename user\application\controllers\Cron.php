<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Cron extends InSecureController
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('truecallerModel');
        $this->load->model("TruecalletokenModel");
        $this->load->model("TruecallerSecondaryTokenModel");
    }

    function truecallerAuthToken($uname,$pwd) {
        $ch = curl_init();
        $param = array(
            'UserName' => $uname,
            'Password' => $pwd
        );
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result);
        $token = (array_key_exists('token',$result)) ? $result->token : '';
        return $token;
    }

    function callapi($authToken, $mobile, $userId){
        $ch = curl_init();
        $param = array(
            'Moblie' => $mobile,
        );
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Tac/GetTrucallerAPI');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Content-Type: application/json';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }

        curl_close($ch);

        writelog($result, 'tc-exc-api-resp');
        
        $truecaller_response = json_decode($result,true);
        $truecaller_data = $truecaller_response['data'];
        $success = $truecaller_response['success'];
        $message = $truecaller_response['message'];
        
        if($success==false ){
            return array("status" => 'false',"msg" => $message);
        }else{
			/*log_message('error',"cron");
			log_message('error', $truecaller_data);*/
            $resData[] = json_decode($truecaller_data);
            /*$returnData =  array(
                "status" =>true,
                "mobile" => $mobile,
                "name" => (property_exists($resData[0], 'name') ? $resData[0]->name : null),
                "gender" => (property_exists($resData[0], 'gender') ? $resData[0]->gender : null),
                "image" => (property_exists($resData[0], 'image') ? $resData[0]->image : null),
                "altName" => (property_exists($resData[0], 'altName') ? $resData[0]->altName : null),
                "about" => (property_exists($resData[0], 'about') ? $resData[0]->about : null),
                "phones" => (property_exists($resData[0], 'phones') ? $resData[0]->phones : []),
                "addresses" => (property_exists($resData[0], 'addresses') ? $resData[0]->addresses : []),
                "internetAddresses" => (property_exists($resData[0], 'internetAddresses') ? $resData[0]->internetAddresses : [])
            );  */   
			if(isset($resData[0][0]->value)){
				//echo "<pre>"; print_r($resData[0][0]->value->name);print_r($resData[0][0]);die;

				$returnData =  array(
					"status" =>'true',
					"mobile" => $mobile,
					"name" => (isset($resData[0][0]->value->name)) ? $resData[0][0]->value->name : null,
					"gender" => (isset($resData[0][0]->value->gender)) ? $resData[0][0]->value->gender : null,
					"image" => (isset($resData[0][0]->value->image)) ? $resData[0][0]->value->image : null,
					"altName" => (isset($resData[0][0]->value->altName)) ? $resData[0][0]->value->altName : null,
					"about" => (isset($resData[0][0]->value->about)) ? $resData[0][0]->value->about : null,
					"phones" => (isset($resData[0][0]->value->phones)) ? $resData[0][0]->value->phones : [],
					"addresses" => (isset($resData[0][0]->value->addresses)) ? $resData[0][0]->value->addresses : [],
					"internetAddresses" => (isset($resData[0][0]->value->internetAddresses)) ? $resData[0][0]->value->internetAddresses : [],
					"spam_description" => (isset($resData[0][0]->value->spamInfo)) ? $resData[0][0]->value->spamInfo : []
				);    
			}else if(isset($resData[0][0]->name)){
				//echo "<pre>"; print_r($resData[0][0]->name);print_r($resData[0][0]);die;
				$returnData =  array(
					"status" =>'true',
					"mobile" => $mobile,
					"name" => (isset($resData[0][0]->name)) ? $resData[0][0]->name : null,
					"gender" => (isset($resData[0][0]->gender)) ? $resData[0][0]->gender : null,
					"image" => (isset($resData[0][0]->image)) ? $resData[0][0]->image : null,
					"altName" => (isset($resData[0][0]->altName)) ? $resData[0][0]->altName : null,
					"about" => (isset($resData[0][0]->about)) ? $resData[0][0]->about : null,
					"phones" => (isset($resData[0][0]->phones)) ? $resData[0][0]->phones : [],
					"addresses" => (isset($resData[0][0]->addresses)) ? $resData[0][0]->addresses : [],
					"internetAddresses" => (isset($resData[0][0]->internetAddresses)) ? $resData[0][0]->internetAddresses : [],
					"spam_description" => (isset($resData[0][0]->spamInfo)) ? $resData[0][0]->spamInfo : []
				);    
			}
			if(isset($returnData['mobile'])){
				$this->truecallerModel->attach(['userId' => $userId,
					 'mobile' => $returnData['mobile'],
					 'name' => $returnData['name'],
					 'gender' => $returnData['gender'],
					 'image' => $returnData['image'],
					 'altName' => $returnData['altName'],
					 'about' => $returnData['about'],
					 'phone' => (string)json_encode($returnData['phones']),
					 'address' => (string)json_encode($returnData['addresses']),
					 'internetAddress' => (string)json_encode($returnData['internetAddresses']),
					'spam_description' => (string)json_encode($returnData['spam_description']),
				  ]);
			}
			if(!isset($returnData)) return [];
            return $returnData;
        }
    }

    function truecaller(){

        require_once APPPATH . "/third_party/Excel2PHP/PHPExcel.php";

        $this->load->model("userModel"); 
        $this->load->model('truecallerModel');
        $this->load->model('truecallerExcelModel');

        $truecallerExcel = $this->truecallerExcelModel->find(['status'=>'processing']);

        $pwd = $this->db->select('value')->get_where('tb_setting', ['name'=>'truecaller_token_password'])->row()->value;

        $authToken = $this->truecallerAuthToken('9461101913','123456');

        //writelog($authToken, 'tc-exc-token');
        
        if($authToken=='') {
            dd('Unauthorized');
        }
        
        foreach($truecallerExcel as $te) {
            $inputFileName = 'uploads/' . $te->filename;
            $userId = $te->userId;
            $truecallerSearch = $this->userModel->findOne(['id' => $userId], "truecallerSearchCredit,truecallerDailySearch");
            $truecallerSearchCredit = $truecallerSearch->truecallerSearchCredit;
            $truecallerDailySearch = $truecallerSearch->truecallerDailySearch;
            try {
                $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
                $objReader = PHPExcel_IOFactory::createReader($inputFileType);
                $objPHPExcel = $objReader->load($inputFileName);
                $excelData = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);

                writelog($te->id, 'tc-exc-id');

                $flag = true;
                $searchRecords = 0;

                $date = new DateTime("now");
                $curr_date = $date->format('Y-m-d ');

                $truecallerDataCount = $this->truecallerModel->count(['userId' => $userId,'DATE(createdate)'=>$curr_date]);

                $limit = $truecallerSearchCredit<$truecallerDailySearch ? $truecallerSearchCredit : $truecallerDailySearch;
                $limit = $limit - $truecallerDataCount;

                $this->truecallerExcelModel->modify(["status" => "processed"], ['id' => $te->id]);

                foreach ($excelData as $value) {

                    if($flag){
                        $flag = false;
                        continue;
                    }
                    
                    //Search only daily search limit and Total limit which is less
                    if($searchRecords == $limit)  { echo 'Daily Limit is over'; break; }

                    echo $value['A'].'<br>';
                    $response = $this->callapi($authToken, $value['A'], $userId);
        			            
                    if(trim($response['status']," ") == 'false') {
                        echo $response['msg'].'<br>';
                        break;
                    } else {
                        $searchRecords = $searchRecords + 1;
                    }
                    echo $searchRecords.'<br>';
                }

                writelog($searchRecords, 'tc-exc-srch');

                $this->truecallerExcelModel->modify(["search_records"=>$searchRecords], ['id' => $te->id]);

                // update credit
                $truecallerSearchCredit = $truecallerSearchCredit - $searchRecords;

                $this->userModel->modify(["truecallerSearchCredit" => $truecallerSearchCredit], ['id' => $userId]);

            } catch (Exception $e) {
                writelog($e->getMessage(), 'tc-exc-error');
                echo $e->getMessage();
            }
        }
    }

}