<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class SubUserReportingModel extends MY_Model {

    protected $primaryTable = 'tb_subuser_reporting_new';
    protected $primaryTablePK = 'id';
    protected $secondaryTable = 'tb_sub_user';
    protected $secondaryTablePK = 'subuserId';
    protected $fkToSecondaryTable = '';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	function totalCount($where = '') {
        $this->db->select('t1.*')
                ->select('t2.userId');
        if ($where) {
            $this->db->where($where);
        }
        return $this->db->from($this->primaryTable . ' t1')->join($this->secondaryTable . ' t2', 't2.' . $this->primaryTablePK . '=t1.' . $this->secondaryTablePK, 'left')->count_all_results();
    }

    function getAll($where = '', $select=null, $extra=NULL) {
        $this->db->select('t1.*')
                ->select('t2.userId')
                ->from($this->primaryTable . ' t1')
                ->join($this->secondaryTable . ' t2', 't2.' . $this->primaryTablePK . '=t1.' . $this->secondaryTablePK, 'left');
        if ($where != '')
            $this->db->where($where);
        $this->db->order_by('t1.id', 'desc');
        if (!is_null($extra) && isArray($extra)) {
            if (array_key_exists('orderBy', $extra) && isArray($extra['orderBy'])) {
                if (isArrayOfArray($extra['orderBy'])) {
                    foreach ($extra['orderBy'] as $key => $value) {
                        $this->db->order_by($value[0], $value[1]);
                    }
                } else {
                    $this->db->order_by($extra['orderBy'][0], $extra['orderBy'][1]);
                }
            }
            if (array_key_exists('limit', $extra) && isArray($extra['limit'])) {
                $this->db->limit($extra['limit']['perPage'], $extra['limit']['offset']);
            }
        }
        $this->db->group_by('t1.id');
        return $this->getResult($this->db->get());
    }

}