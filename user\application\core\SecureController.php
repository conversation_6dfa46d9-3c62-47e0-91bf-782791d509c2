<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class SecureController extends InSecureController {
	
	// List of allowed controllers
    protected $allowedControllers = ['cellexpcgi', 'truecaller', 'me', 'osint', 'link']; // Add controller names in lowercase

    function __construct() {
        parent::__construct();
        if ($this->auth->has() === false) {
            return redirectTo('logout');
        }
        if ($this->auth->has() === true && $this->viewDataBag->userSession->alreadyLogin == "yes" ) {
            return redirectTo('logout');
        }
        if ($this->viewDataBag->user->sessionId != $this->viewDataBag->userSession->sessionId) {
            return redirectTo('logout');
        }
		
		// Controller name check (in lowercase for consistency)
        $currentController = strtolower($this->router->class);
		//dd($currentController);
        if (!in_array($currentController, $this->allowedControllers)) {
            show_error('Access is restricted.', 403, 'Forbidden');
        }

    }

}
