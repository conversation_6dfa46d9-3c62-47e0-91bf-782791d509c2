<?php

class Forgot_password extends InSecureController {

    function __construct() {
        parent::__construct();
        $this->load->model('userModel');
     }

    function index() {
		
		return show_404();
		
        if ($this->input->method() === 'post'):
            $this->form_validation->set_rules('mobile', 'mobile', 'trim|numeric|required|max_length[16]|min_length[10]|xss_clean|_isExist[tb_user.mobile]');
            if ($this->form_validation->run() === true):
                $userData = $this->userModel->findOne(['mobile' => $this->input->post('mobile')], 'id,name,email,mobile,isStatus');
                if (isObject($userData)):
                    if ($userData->isStatus == 'active'):
                        $password = mt_rand(123123123, 999999999);
                        $this->sms->dispatch($userData->mobile, $password . " " . "This is your  Password  for IP Tracking  password reset. DO NOT SHARE THIS WITH ANYBODY.");
                        if ($this->userModel->modify(['password'=>makePasswordHash($password)],["id"=>$userData->id])):
                            $this->alert->success('Your password has been reset and send on your registered mobile number successfully.');
                        else:
                            $this->alert->danger(getErrorMessage());
                        endif;
                        return redirectToCurrent();
                    else:
                        $this->alert->danger("Your account has been deactivated contact to admin for activaction.");
                        return redirectToCurrent();
                    endif;
                else:
                    $this->alert->danger("Your mobile number doesnot match any account.");
                    return redirectToCurrent();
                endif;
               // return redirect('forgot-password');
            endif;
        endif;
        $this->load->View('authentication/forgotPassword/formView', $this->viewDataBag);
    }



}
