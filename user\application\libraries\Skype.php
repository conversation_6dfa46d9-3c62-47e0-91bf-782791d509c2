<?php

class Skype
{

    private $ci;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
    }

    function accessToken() 
    {
        //header
        $headers = array(
            'accept: application/json',
            'accept-encoding: gzip',
            'connection: Keep-Alive',
            'content-type: application/x-www-form-urlencoded',
            'host: login.live.com',
            'user-agent: okhttp/4.9.1'
        );
        $url = 'https://login.live.com/oauth20_token.srf';

        $postData = 'client_id=00000000480BC46C&nopa=1&coa=1&scope=service::lw.skype.com::MBI_SSL&refresh_token=M.C107_BAY.-CSGyWNTNuj4i13kREQcWFDNa!8REKc4nTitF80Q8!Je8o*wkRt8i*YgAOqtJ2qvZljSGNS3Nf3zg5UV1hV4NZxkanUk3*e*s6CxQKSfjHObijg!EQgrvHwFZMkfMAc!Crj2DzZz2tZvGxPhLge90k*4VEZgeVkIVSm31KMSheFneLvYVzKlb4KL9PZ*rRVV69BURs9WvOuswdVEj9MvE6gBzsbJ9x5oRG*emRTXXL3LOfhFXyHwVNW1h5PN4ojN0eQDYAgnwMyE0u31eVUYWNFM$&grant_type=refresh_token&skmpb=0';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        $result = curl_exec($ch);        
        $result = json_decode($result);
        curl_close($ch);
        return $result;
    }

    function skypeToken() {
        $tokenData = $this->accessToken();
		dd($tokenData);
        $accessToken = $tokenData->access_token;
        
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://edge.skype.com/rps/v1/rps/skypetoken',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{"scopes":"client","clientVersion":"1419/8.88.0.404","access_token":"'.$accessToken.'","site_name":"lw"}',
          CURLOPT_HTTPHEADER => array(
            'accept: application/json',
            'accept-encoding: gzip',
            'connection: Keep-Alive',
            'content-type: application/json',
            'host: edge.skype.com',
            'user-agent: okhttp/4.9.1',
            'x-skype-caller: 572381',
            'x-skype-request-id: 0988418d-ac43-4b21-aca0-d0435e703716'
          ),
        ));

        $result = curl_exec($curl);
        $result = json_decode($result);
        $skypeToken = $result->skypetoken;
        curl_close($curl);
        return $skypeToken;
    }

    function account($userName) {
		$token_expiry =  setting('skype_token')->createdOn;
		$token_value = setting('skype_token')->value;
		$current_date = date('Y-m-d H:i:s');
		if($current_date>$token_expiry || $token_value=='') {
			$this->ci->load->model('settingModel');
			$created_date =  date('Y-m-d H:i:s', strtotime("+20 hour"));
			$updatedData = [
				'value' => $this->skypeToken(),
				'createdOn' => $created_date 
			];
			$this->ci->settingModel->modify($updatedData, ['name' => 'skype_token' ]);
		}
		dd(2);
		$skypeToken = setting('skype_token')->value;
		dd($skypeToken);
		
        //get skypetoken every 10 hours
        /*if(!$ci->input->cookie('skypeToken',TRUE)) {
			echo 11;die;
            setcookie("skypeToken", $ci->skypeToken(), time() + 3600*10);
        }*/
        //$skypeToken = $ci->input->cookie('skypeToken',TRUE);

        //header
        $headers = array(
            'accept: application/json',
            'host: skypegraph.skype.com',    
            'x-ecs-etag: g9DsoMY3hyWQT9Nm8KcU517fudj9IRGvALi7K4cIjuc=',
            'x-skype-client: 1419/8.88.0.404',
            'x-skypegraphservicesettings: {"experiment":"MinimumFriendsForAnnotationsEnabled","geoProximity":"disabled","minimumFriendsForAnnotationsEnabled":"true","minimumFriendsForAnnotations":2,"demotionScoreEnabled":"true"}',
            'x-skypetoken: '.$skypeToken,
        );
        $url = 'https://skypegraph.skype.com/v2.0/search?searchString='.$userName.'&requestId=Query21';
        $check = get_headers($url, 1);
        //dd($check);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $result = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $response['status'] = 404;
        $response['data'] = '';
        
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
        } else {
			$result = json_decode($result);
        	$response['status'] = property_exists($result, 'demotionIndex') ? $result->demotionIndex : 500;
        	$response['data'] = property_exists($result, 'results') ? $result->results : [];
		}
        
        curl_close($ch);
        return $response;
    }

    function error()
    {
        return $this->responseError;
    }

}