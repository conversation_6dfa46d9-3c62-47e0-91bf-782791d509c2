<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Nodalofficer extends SecureController {
     
    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->model('NodalOfficerModel','NodalOfficer');
    }

    public function all($offset = 0){ 
        $name = $this->input->get('name');
        //$curlPost = "Company=".$name;
		$curlPost = json_encode(["Company"=>$name]);
        $authToken = $this->viewDataBag->userSession->auth_token;
        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetNodalByCompanyName');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Cache-Control: no-cache';
		$headers[] = 'Content-Type: application/json';
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($cURLConnection);
        if (curl_errno($cURLConnection)) {
            echo 'Error:' . curl_error($cURLConnection);
        }
        curl_close($cURLConnection);
        $nodalData = json_decode($result);
        $this->viewDataBag->response = isset($nodalData->Data) ? $nodalData->Data : [];
        $this->viewDataBag->jsView = 'nodalOfficer/jsView';
        $this->loadView('nodalOfficer/listView', $this->viewDataBag);        
    }   
}