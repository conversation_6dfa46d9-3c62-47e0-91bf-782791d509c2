<?php

Class Api {

    protected $ci;
    protected $authToken = "KmSJLPX+NicgqDtQpXk1YURiC63Og012ZBBrY7l0Duz427AmuBU9j67EvQh4zOoqtM8g/aU1ti1OOsWcPEfCkg==";
    protected $headers = ["X-Auth-Token:KmSJLPX+NicgqDtQpXk1YURiC63Og012ZBBrY7l0Duz427AmuBU9j67EvQh4zOoqtM8g/aU1ti1OOsWcPEfCkg=="];
    protected $baseUrl = 'http://project.avengingsecurity.com/mkr/api/';
    protected $SECRET_KEY = "qaz125mlp{;.7j8&";
    protected $urls = [
        "userRegistration" => "v1/user/registration",
        "userLogin" => 'v1/user/login',
        "facebookLogin" => 'v1/user/social-login-facebook',
        "googleLogin" => 'v1/user/social-login-google',
    ];
    protected $debugError = "";

    function __construct() {
        $this->ci = & get_instance();
        if ($this->ci->input->server('SERVER_NAME') == "localhost") {
            $this->baseUrl = "http://localhost/mkr/api/";
        }
    }

    function debugInfo() {
        d($this->debugError);
    }

    function getHeaders() {
        return (object) $this->headers;
    }

    function getAuthToken() {
        return (string) $this->authToken;
    }

    function getUrl($key) {
        return (string) (array_key_exists($key, $this->urls)) ? $this->baseUrl . $this->urls[$key] : "";
    }

    private function _handleResponse($response) {
        if (isArray($response) && array_key_exists('debug_info', $response)) {
            $this->debugError = $response;
            return (object) ["status" => (bool) FALSE,
                        "error" => "Oops! Something went wrong.",
                        "errorCode" => "SE"];
        }
        return (object) json_decode($response);
    }

    function get($url, $params = []) {
        $this->ci->curl->setHeader($this->headers);
        $response = $this->ci->curl->get($this->baseUrl . $this->urls[$url], (isArray($params) > 0) ? ["key" => $this->encrypt($params)] : []);
        return $this->_handleResponse($response);
    }

    function post($url, $params) {
        $this->ci->curl->setHeader($this->headers);
        $response = $this->ci->curl->post($this->baseUrl . $this->urls[$url], ["post" => $this->encrypt($params)]);
        return $this->_handleResponse($response);
    }

    /**
     * @param string $str
     * @param bool $isBinary whether to encrypt as binary or not. Default is: false
     * @return string Encrypted data
     */
    public function encrypt($str1, $isBinary = false) {
        $size = 32; //mcrypt_get_block_size('aes', 'cbc');
        $str = $this->pkcs5_pad(json_encode($str1));

        $iv = $this->SECRET_KEY;
        $str = $isBinary ? $str : utf8_decode($str);

        $td = mcrypt_module_open('rijndael-128', ' ', 'cbc', $iv);

        mcrypt_generic_init($td, $this->SECRET_KEY, $iv);
        $encrypted = mcrypt_generic($td, $str);

        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);

        return $isBinary ? $encrypted : base64_encode($encrypted);
    }

    /**
     * @param string $code
     * @param bool $isBinary whether to decrypt as binary or not. Default is: false
     * @return string Decrypted data
     */
    public function decrypt($code, $isBinary = false) {
        $code = $isBinary ? $code : base64_decode($code);
        $iv = $this->SECRET_KEY;
        $td = mcrypt_module_open('rijndael-128', ' ', 'cbc', $iv);
        mcrypt_generic_init($td, $this->SECRET_KEY, $iv);
        $decrypted = mdecrypt_generic($td, $code);

        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);

        return $isBinary ? trim($decrypted) : utf8_encode(trim($this->pkcs5_unpad($decrypted)));
    }

    private function pkcs5_pad($text) {
        $size = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
        $pad = $size - (strlen($text) % $size);
        return $text . str_repeat(chr($pad), $pad);
    }

    private function pkcs5_unpad($text) {
        $pad = ord($text{strlen($text) - 1});
        if ($pad > strlen($text))
            return false;
        if (strspn($text, chr($pad), strlen($text) - $pad) != $pad)
            return false;
        return substr($text, 0, -1 * $pad);
    }

}
