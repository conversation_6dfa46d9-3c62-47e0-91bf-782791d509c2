<?php

class OsintServices
{

    private $ci;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
    }

    private function authToken($uname,$pwd) {
        $ch = curl_init();
        $param = array(
            'UserName' => $uname,
            'Password' => $pwd
        );
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result);
		
        $token = (array_key_exists('token',$result)) ? $result->token : '';

        return $token;
    }

    public function fetchData($input) {

        $services = [
            'bulk' => 'fetchBulk',
        ];

        if (preg_match('/^[6-9]\d{9}$/', $input['uname']) === 1) {
            $services['fetch_whatsapp'] = 'fetchWhatsapp';
			$services['fetch_telegram'] = 'fetchTelegram';
            $services['fetch_payment_details'] = 'fetchPaymentDetails';
            $services['fetch_fb_details'] = 'fetchFBDetails';
        }

        $auth_token = $this->authToken($input['uname'],$input['password']);

        if(!$auth_token) {
            log_message('error', "Osint Module Error response from Token Generation: Invalid credentials");
            return [
                'status' => 'error',
                'message' => 'Invalid credentials',
                'code' => 401
            ];
        } 
		
        $input['auth_token'] = $auth_token;

        $results = [];
        foreach ($services as $service_name => $method) {
            try {
                if (method_exists($this, $method)) {
                    $results[$service_name] = $this->$method($input);
                } else {

                    log_message('error', "Osint Module Error response from {$service_name}: Service not implemented");

                    $results[$service_name] = [
                        'status' => 'error',
                        'message' => 'Service not implemented',
                        'code' => 501
                    ];
                }
            } catch (Exception $e) {
                log_message('error', "Osint Module Error response from {$service_name}: {$e->getMessage()}");
                $results[$service_name] = [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'code' => 500
                ];
            }
        }

        return $results;
    }

    function fetchBulk($input)
    {
        $ch = curl_init();
        $param = json_encode([
            'MobileNumber' => $input['mobile'],
            'EmailId' => $input['email']
        ]);

        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Tac/GetOSIntAPICommon');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);

        $headers = array();
        $headers[] = 'Authorization: Bearer '.$input['auth_token'];
        $headers[] = 'Content-Type: application/json';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		
        if (curl_errno($ch)) {
			return [
                'status' => "error",
                'message' => curl_error($ch),
                'code' => 500				
			];
        }

        curl_close($ch);

        if ($http_code >= 200 && $http_code < 300) {
            return [
                "status" => "success",
                "data" => json_decode($result, true),
            ];
        } else {
            return [
                "status" => "error",
                "message" => "HTTP error code $http_code",
                "code" => $http_code,
            ];
        }
		
    }

    function fetchWhatsapp($input)
    {
        $post = [
            "mobile" => $input['mobile'],
            "userId"     => $input['userId']
        ];
        
        $ch = curl_init();

        curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://msg.ccas.in/api/whatsap/find',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('userId' => $input['userId'],'mobile' => $input['mobile']),
        CURLOPT_HTTPHEADER => array(
            //'Content-Type: application/json',
            'Authorization: ••••••'
        ),
        ));

        $response = curl_exec($ch);
        //writelog($response, 'wh');
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            return [
                'status' => "error",
                'message' => curl_error($ch),
                'code' => 500				
			];
        }

        curl_close($ch);
        $responseData = json_decode($response, true);

        //dd($responseData);

        if(isset($responseData) && isset($responseData['data'])) {

            $result[] = [
                'Status' => 'is exist',
                'PayloadData' => json_encode($responseData['data']),
                'Data' => null,
                'APIName' => 'Whatsapp'
            ];
    
            return [
                "status" => "success",
                "data" => $result,
            ];
        }

        return [
            "status" => "error",
            "message" => "HTTP error code $http_code",
            "code" => $http_code,
        ];
    }
	
	function fetchTelegram($input)
    {
        $post = [
            "mobile" => $input['mobile'],
            "userId"     => $input['userId']
        ];
        
        $ch = curl_init();

        curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://msg.ccas.in/api/telegram/find',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => array('userId' => $input['userId'],'mobile' => $input['mobile']),
        CURLOPT_HTTPHEADER => array(
            //'Content-Type: application/json',
            'Authorization: ••••••'
        ),
        ));

        $response = curl_exec($ch);

        //writelog($response, 'tg');

        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            return [
                'status' => "error",
                'message' => curl_error($ch),
                'code' => 500				
			];
        }

        curl_close($ch);
        $responseData = json_decode($response, true);

        //dd($responseData);

        if(isset($responseData) && isset($responseData['data'])) {

            $result[] = [
                'Status' => 'is exist',
                'PayloadData' => json_encode($responseData['data']),
                'Data' => null,
                'APIName' => 'Telegram'
            ];
    
            return [
                "status" => "success",
                "data" => $result,
            ];
        }

        return [
            "status" => "error",
            "message" => "HTTP error code $http_code",
            "code" => $http_code,
        ];
    }

    function fetchPaymentDetails($input)
    {
        $ch = curl_init();

        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://api.eitem.in/api/v1/mobile-info/lookup',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('mobile_number' => $input['mobile']),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Client-ID: client_xxjIHtXeRniH6CWK',
                'X-Client-Secret: wA1i9U8izau8IfybfIeWW5hNoFY7rUtk'
            ),
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            return [
                'status' => "error",
                'message' => curl_error($ch),
                'code' => 500
            ];
        }

        curl_close($ch);

        $responseData = json_decode($response, true);

        writelog($responseData, 'payment_methods');

        if ($http_code >= 200 && $http_code < 300 && isset($responseData['status']) && $responseData['status'] === true) {
            $data = $responseData['data'];

            $result[] = [
                'Status' => 'is exist',
                'PayloadData' => json_encode($data),
                'Data' => [
                    'PhonePe' => $data['phonepe_data'] ? $data['phonepe_data'] : null,
                    'GPay'    => $data['gpay_data'] ? $data['gpay_data'] : null,
                ],
                'APIName' => 'PaymentMethod'
            ];

            return [
                "status" => "success",
                "data" => $result,
            ];
        }

        return [
            "status" => "error",
            "message" => "HTTP error code $http_code",
            "code" => $http_code,
            "raw" => $response
        ];
    }

    function fetchFBDetails($input)
    {
        $ch = curl_init();

        // Remove any non-digit characters first (optional, for safety)
        $mobile = preg_replace('/\D/', '', $input['mobile']);

        if (substr($mobile, 0, 2) !== "91") {
            $mobile = "91" . $mobile;
        }

        curl_setopt_array($ch, array(
            CURLOPT_URL => 'https://msg.ccas.in/api/caller/search',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('userId' => $input['userId'], 'phone' => $mobile),
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'Authorization: Bearer VWN4ZWREWUkxWklHbEZJTFRTWStwYk5YcE54MWpiVXBtdXUxaFZKRWFFWUtqeFl4dm9jTkgyOThxQXhqOXJmKw=='
            ),
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            return [
                'status' => "error",
                'message' => curl_error($ch),
                'code' => 500
            ];
        }

        curl_close($ch);

        $responseData = json_decode($response, true);

        writelog($responseData, 'fb_caller');

        if ($http_code >= 200 && $http_code < 300 && !empty($responseData['data'])) {
            $data = $responseData['data'];

            $result[] = [
                'Status' => 'is exist',
                'PayloadData' => json_encode($data),
                'Data' => null,
                'APIName' => 'Facebook'
            ];

            return [
                "status" => "success",
                "data" => $result,
            ];
        }

        return [
            "status" => "error",
            "message" => "HTTP error code $http_code",
            "code" => $http_code,
            "raw" => $response
        ];
    }

    function error()
    {
        return $this->responseError;
    }

}