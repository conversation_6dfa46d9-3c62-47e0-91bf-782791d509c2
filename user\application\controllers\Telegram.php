<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Telegram extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('TelegramBot');
		$this->load->library('TelegramService');
        $this->load->model('TelegramModel');
    }
	
	public function index()
	{
		/*$commands = [
			['command' => 'start', 'description' => 'Start the bot'],
			['command' => 'menu', 'description' => 'Show main menu'],
			['command' => 'help', 'description' => 'Get help']
		];

		file_get_contents("https://api.telegram.org/bot7651277303:AAECGnZ7D_3CBTgzO9nJ4OYfCMe6nygb9AE/setMyCommands?" . http_build_query([
			'commands' => json_encode($commands)
		]));*/
		
		/*$response = file_get_contents("https://api.telegram.org/bot7651277303:AAECGnZ7D_3CBTgzO9nJ4OYfCMe6nygb9AE/getMyCommands");
		$commands = json_decode($response, true);

		echo '<pre>';
		print_r($commands);
		echo '</pre>';*/
		echo 'telegram bot';
	}

    public function webhook() {
        $input = file_get_contents("php://input");
        $update = json_decode($input, true);
		
        //writelog($update, 'webhook');

        if (isset($update["message"])) {

			$message = $update['message'];				
            $chat_id = $update["message"]["chat"]["id"];
            $telegram_id = $update["message"]["from"]["id"];

			if (isset($message['contact'])) {
                $this->handleContact($message);
                return;
            }

			if (isset($message['text'])) {
                $text = trim($message['text']);

                if ($text == '/start') {
                    if( !$this->checkUser($chat_id,$telegram_id)) {
                        return;
                    } else {
                        $this->sendFeaturesList($chat_id, $telegram_id, 'Main Menu');
                    }
				} else if ($text == '/menu') {
                    $this->sendFeaturesList($chat_id, $telegram_id, 'Main Menu');
                } else {
					$this->sendFeaturesList($chat_id, $telegram_id, $text);
				}
            }

        }

        if (isset($update['callback_query'])) {
            $callbackData = $update['callback_query']['data'];
            $chat_id = $update['callback_query']['message']['chat']['id'];
            $user_id = $update['callback_query']["message"]["from"]["id"];
            $telegram_id = $update['callback_query']['from']['id'];

            // Check if user is registered
            if( !$this->checkUser($chat_id,$telegram_id)) {
                return;
            }

            $user = $this->TelegramModel->findOne(['telegramId' => $telegram_id]);

            $this->load->model('truecallerModel');
            $this->load->model('osintModel');
            $this->load->model('settingModel');
            
            $date = new DateTime("now");
            $curr_date = $date->format('Y-m-d ');

            /* Get Truecaller Search Credit */
            $truecallerSearchCredit = $user->truecallerSearchCredit;
            $truecallerDailySearch = $user->truecallerDailySearch;

            $truecallerDailyCount = $this->truecallerModel->count(['userId' => $user->id, 'userType' => 'telegram', 'DATE(createdate)'=>$curr_date]);

            $dailySearchRem = $truecallerSearchCredit<$truecallerDailySearch ? $truecallerSearchCredit : $truecallerDailySearch;
            $dailySearchRem =  $dailySearchRem - $truecallerDailyCount;
            /* Get Truecaller Search Credit */

            /* Get Osint Search Credit */
            $osintSearchCredit = $user->osintSearchCredit;
            $osintDailySearch = $user->osintDailySearch;

            $osintDailyCount = $this->osintModel->count(['userId' => $user->id,'userType' => 'telegram','DATE(createdOn)'=>$curr_date]);

            $osintDailySearchRem = $osintSearchCredit<$osintDailySearch ? $osintSearchCredit : $osintDailySearch;
            $osintDailySearchRem =  $osintDailySearchRem - $osintDailyCount;
            /* Get Osint Search Credit */

            $settings = $this->settingModel->find(['type'=>'telegram_bot'], '*');

            foreach($settings as $setting) {                
                $vehicleCredit = ($user->accountType == 'demo' && $setting->name == 'vehicle_demo_daily_credit')
                                    ? $setting->value
                                    : (($user->accountType == 'paid' && $setting->name == 'vehicle_paid_daily_credit')
                                        ? $setting->value
                                        : 100);

            }            
        
            // Respond to the selected button
            switch ($callbackData) {
                case 'pincode':
                    $message = "Please enter the pincode.\nFor example: pin 123456";
                    break;
                case 'location':
                    $message = "Please enter the Latitude & Longitude.\nFor example: location 28.6139,77.2090";
                    break;
                case 'bank_details':
                    $message = "🏦 *Bank Account Verification*\n";
                    $message .= "Please enter the Bank Details.\n";
                    $message .= "_Use format:_ `bank AccountNumber IFSC`\n";
                    $message .= "_For example:_ `bank ************** BARB0RAMAJM`\n\n";
                    $message .= "🔄 *Your Bank Search Credit:* {$vehicleCredit}\n";
                    break;
				case 'ifsc':
					$message  = "🏦 *IFSC Code Lookup*\n";
					$message .= "Please enter the IFSC code to search bank details.\n\n";
					$message .= "🔹 _Format:_ `ifsc IFSC_CODE`\n";
					$message .= "🔹 _Example:_ `ifsc BARB0RAMAJM`\n";
					break;
                case 'isd':
                    $message = "Please enter ISD.\nFor example: isd 91";
                    break;
                case 'std':
                    $message = "Please enter STD.\nFor example: std 0141";
                    break;
                case 'toll_free':
                    $message = "Please enter the Toll Free Details.\nFor example: toll ***********";
                    break;
                case 'msp':
                    $message = "Please enter the Mobile Number.\nFor example: msp **********";
                    break;
                case 'short_code':
                    $message = "Please enter the Short Code Details.\nFor example: short 56767";
                    break;
                case 'shorten':
                    $message = "Please enter the URL.\nFor example: shorten https://example.com/your-long-link";
                    break;
                case 'virtual_number':
                    $message = "Please enter the Virtual Number Details.\nFor example: virtual 91********** 919876543211";
                    break;
                case 'sms':
                    $message = "Please enter the SMS Details.\nFor example: smsid VK-OTPSMS";
                    break;
                case 'cin':
                    $message = "Please enter Company Details.\nFor example: cin L24230DL1975PLC007908";
                    break;
                case 'cname':
					$message  = "🏢 *Search Company by Name*\n\n";
                    $message .= "🔹 *Format:* `cname CompanyType|CompanyName`\n";
                    $message .= "🔹 *Example:* `cname Private|Tata Motors`\n\n";
                    $message .= "✅ *Valid CompanyType values:*\n";
                    $message .= "• `LLP`\n";
                    $message .= "• `Private`\n";
                    break;
                case 'din':
                    $message = "Please enter DIN Number.\nFor example: din 01234567";
                    break;
                case 'pan':
                    $message = "Please enter PAN Number.\nFor example: pan 0123456789";
                    break;
                case 'gst':
                    $message = "Please enter GST Number.\nFor example: gst 08AADFT3025B1ZP";
                    break;
                case 'nodal':
                    $message = "Please enter Nodal Number.\nFor example: nodal companyname";
                    break;
                case 'track_cellid':
                    $message = "Please enter Cell ID.\nFor example: cellid ***************";
                    break;
                case 'aadhar':
                    $message = "🆔 *Aadhaar Number Search*\n";
                    $message .= "Please enter Aadhaar Number.\n";
                    $message .= "_For example:_ `aadhaar 123456789012`\n\n";
                    $message .= "🔄 *Your Aadhaar Search Credit:* {$vehicleCredit}\n";
                    break;
                case 'dl':
                    $message = "🪪 *Driving License Search*\n";
                    $message .= "Please enter Driving License Details.\n";
                    $message .= "_Use format:_ `dl DLNumber|DateOfBirth`\n";
                    $message .= "_For example:_ `dl AN0120130003278 1987-12-20`\n\n";
                    $message .= "🔄 *Your DL Search Credit:* {$vehicleCredit}\n";
                    break;
                case 'mnp':
                    $message = "Please enter one or more MNP numbers, separated by commas.\nFor example: mnp **********,**********";
                    break;
                case 'gmail':
                    $message = "Please enter Gmail ID.\nFor example: gmail <EMAIL>";
                    break;
				case 'vehicle':
                    $message = "🚗 *Vehicle Search*\n";
                    $message .= "Please enter Vehicle Number.\n";
                    $message .= "_For example:_ `vehicle DL1ABC1234`\n\n";
                    $message .= "🔄 *Your Vehicle Search Credit:* {$vehicleCredit}\n";
                    break;
                case 'whatsapp':
                    $message = "💬 *WhatsApp Number Search*\n";
                    $message .= "Please enter WhatsApp Number.\n";
                    $message .= "_For example:_ `whatsapp **********`\n\n";
                    $message .= "🔄 *Your WhatsApp Search Credit:* {$vehicleCredit}\n";
                    break;
                case 'proxy':
                    $message = "Please enter Proxy Details.\nFor example: proxy *************";
                    break;
                case 'ip':
                    $message = "Please enter IP Address.\nFor example: ip *************";
                    break;
                case 'truecaller':
					$message  = "🔍 *Truecaller Lookup Help:*\n";
					$message .= "You can search *multiple mobile numbers* separated by *space* or *comma*.\n\n";
					$message .= "📌 *Format:* `true **********,**********` or `true ********** **********`\n";
					$message .= "📈 *Truecaller Search Credit:* {$truecallerSearchCredit}\n";
                    $message .= "📅 *Today’s Search Credit:* {$truecallerDailySearch}\n";
					$message .= "📅 *Today’s Search Remaining:* {$dailySearchRem}\n";
					break;
                case 'imei':
                    $message = "Please enter IMEI Number.\nFor example: imei 867799041329938";//358240051111110
                    break;
                case 'second_imei':
                    $message = "Please enter IMEI Number.\nFor example: secondimei 358240051111110";
                    break;
				case 'mobile_sdr':
                    $message = "📱 *Mobile SDR Search*\n";
                    $message .= "Please enter Mobile SDR Details.\n";
                    $message .= "_For example:_ `sdr **********`\n\n";
                    $message .= "🔄 *Your SDR Search Credit:* {$vehicleCredit}\n";
                    break;
                case 'osint':
                    $message = "📡 *OSINT Lookup*\nPlease enter a mobile number or email address.\n\n";
                    $message .= "📌 *Example:* \n`osint **********`\n`osint <EMAIL>`\n";
                    $message .= "📈 *Search Credit:* {$osintSearchCredit}\n";
                    $message .= "📅 *Today’s Search Credit:* {$osintDailySearch}\n";
					$message .= "📅 *Today’s Search Remaining:* {$osintDailySearchRem}\n";
                    break;
                case 'link_create':
                    $message = "Please enter the following format to create a case:\n\n";
                    $message .= "`Case:FIR/Case Name | Redirect:https://example.com | Cam=yes | Location=yes | Paste=No` \n";
                    break;
                default:
                    $this->telegramservice->handleUserInput($chat_id, $telegram_id, $callbackData);
			        return ;
                    break;

            }
            
            $this->telegrambot->sendMessage($chat_id, $message);
			return ;
        }

    }

	private function start($chat_id)
    {
        $text = "👋 Welcome! Please verify your number to continue.";
        $this->askForContact($chat_id);
    }

	private function askForContact($chat_id)
    {
        $keyboard = [
            'keyboard' => [[[
                'text' => '📱 Share My Phone Number',
                'request_contact' => true
            ]]],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];
        $this->telegrambot->sendMessageWithKeyboard($chat_id, "🚨 Please share your contact to use our services:", $keyboard);
    }

	private function handleContact($message)
    {
        $contact = $message['contact'];
        $phone = $contact['phone_number'];
        $name = $contact['first_name'] ? $contact['first_name'] : 'Unknown';
        $telegram_id = $message['from']['id'];
		$chat_id = $message['chat']['id'];

		$phone = preg_replace('/^\+?91/', '', $phone);

        $res = saveUser($name, $phone, $telegram_id);

        if($res) {

            $user = $this->TelegramModel->findOne(['mobile' => $phone]);

            if (!empty((array)$user)) {

                if ($user->status !== 'active') {
                    $this->telegrambot->sendMessage($chat_id, "Your account has been deactivated. Please contact the administrator to activate your account and continue using our services. 📞 Contact:  +91 97842 68547");
                    return;
                }

                if($user->expiryDate && strtotime($user->expiryDate) < strtotime(currentDateTime())) {
                    $this->telegrambot->sendMessage($chat_id, "Your account has expired as of {$user->expiryDate}. Please contact the administrator to renew your account and continue using our services. 📞 Contact:  +91 97842 68547");
                    return;
                }

                if($user->accountType == 'fresh') {
                    $this->telegrambot->sendMessage($chat_id, "You are not registered for the Cligence Bot,  Please contact the administrator to activate your account 📞 Contact: +************");
                    return;
                }
                
                $registered_on = longDateTime($user->createdOn);

                $response = "*👤 `{$user->name}` Details:*\n";
                $response .= "*Name:* `{$user->name}`\n";
                $response .= "*Phone:* `{$user->mobile}`\n";
                $response .= "*Registered On:* `$registered_on`\n";
                
                $this->telegrambot->sendMessage($chat_id, $response);
                
                // ✨ After sending user details, send available features
                $this->sendFeaturesList($chat_id, $telegram_id, 'Main Menu');

                return ;

            } else {
                return ;
            }

        } else  {
            $this->telegrambot->sendMessage($chat_id, "Thank you for sharing your contact information! We will now verify your account.");
            return;
        }
        
    }
	
	private function sendFeaturesList($chat_id, $telegram_id, $text)
	{

        // Check if user is access or not
        if( !$this->checkUser($chat_id,$telegram_id)) {
            return;
        }

        $user = $this->TelegramModel->findOne(['telegramId' => $telegram_id]);

		if ($text == '/menu' || $text == 'Main Menu') {
			$keyboard = [
				'keyboard' => [
					[ 
                        ['text' => '👤 Account Info'], 
                        ['text' => '🌐 IP Grabber'] 
                    ],
					[ 
                        ['text' => '🔍 Search Individual'], 
                        ['text' => '✅ Verification'] 
                    ],
					[
                        ['text' => '📡 Cell ID’s'], 
                        ['text' => '🏢 Company'] 
                    ],
					[ 
                        ['text' => '🧰 Misc'] 
                    ]
				],
				'resize_keyboard' => true,
				'one_time_keyboard' => false
			];

			$message = "📲 *Please choose a feature:*";

		} else if($text == "👤 Account Info") {

            $registered_on = longDateTime($user->createdOn);
            if ($user->expiryDate) {
                $expiryDate = longDateTime($user->expiryDate);
            } else {
                $expiryDate = "Not Set";
            }

            $response = "*👤 Account Details:*\n";
            $response .= "*Name:* `{$user->name}`\n";
            $response .= "*Phone:* `{$user->mobile}`\n";
            $response .= "*Registered On:* `{$registered_on}`\n";
            $response .= "*Valid Till:* `{$expiryDate}`\n";
            $response .= "*Status:* `{$user->status}`\n";

            $this->telegrambot->sendMessage($chat_id, $response);
            return;
        } else if ($text == "🧰 Misc") {	
            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '🌍 ISD Code', 'callback_data' => 'isd'],
                        ['text' => '🏙️ STD Code', 'callback_data' => 'std']
                    ],
                    [
                        ['text' => '📦 Pincode Search', 'callback_data' => 'pincode'],
                        ['text' => '🏦 IFSC', 'callback_data' => 'ifsc']
                    ],
                    [
                        ['text' => '☎ Toll-Free Search', 'callback_data' => 'toll_free'],
                        ['text' => '📱 Short Code Search', 'callback_data' => 'short_code']
                    ],
                    [
                        ['text' => '📱 MSP Search', 'callback_data' => 'msp'],
                        ['text' => '📤 BULK SMS Finder', 'callback_data' => 'sms']
                    ],
					[
						['text' => '📍 Latitude & Longitude Search', 'callback_data' => 'location'],
                    ],
                    [
						['text' => '🔗 URL Shortener', 'callback_data' => 'shorten'],
                        ['text' => '📱 Virtual Number', 'callback_data' => 'virtual_number'],
					]
                ]
            ];
            
			$message = "🚀 *Misc Features:*\n\n";

		} else if($text == "🏢 Company") {
            $keyboard = [
				'inline_keyboard' => [
					[
						['text' => '🏢 Company Search', 'callback_data' => 'cin'],
					],
					[
						['text' => '🏢 Company Search By Name', 'callback_data' => 'cname'],
					],
                    [
						['text' => '🆔 DIN Search', 'callback_data' => 'din'],
                        ['text' => '🔎 PAN Search', 'callback_data' => 'pan']
					],
					[
						['text' => '💼 GST Info', 'callback_data' => 'gst'],
						['text' => '📮 Nodal Officer', 'callback_data' => 'nodal']
					]
				]
			];

			$message = "🚀 *Company Features:*\n\nPlease select a service below:";
        } else if($text == "📡 Cell ID’s") {
            $keyboard = [
				'inline_keyboard' => [
					[
						['text' => '📡 Track Cell Id', 'callback_data' => 'track_cellid'],
					],
				]
			];

			$message = "📡 *Cell ID Features:*\n\nPlease select a service below:";
        } else if($text == "✅ Verification") {
            $keyboard = [
				'inline_keyboard' => [
					[
						['text' => '🆔 Aadhar', 'callback_data' => 'aadhar'],
                        ['text' => '🚘 Driving License', 'callback_data' => 'dl'],
					],
					[
						['text' => '🏦 Bank Account', 'callback_data' => 'bank_details'],
					],
				]
			];

			$message = "📡 *✅ Verification Features:*\n\nPlease select a service below:";
        } else if($text == "🔍 Search Individual") {
            $keyboard = [
				'inline_keyboard' => [
					[
						['text' => '🪪 PAN to Company', 'callback_data' => 'pan'],
                        ['text' => '📧 Gmail ID', 'callback_data' => 'gmail'],
					],
					[
						['text' => '🔁 MNP Check', 'callback_data' => 'mnp'],
						['text' => '🚗 Vehicle Info', 'callback_data' => 'vehicle'],
					],
					[
						['text' => '🕵️‍♂️ OSINT / Leak Check', 'callback_data' => 'osint'],
						['text' => '📞 Truecaller Lookup', 'callback_data' => 'truecaller'],
					],
					[
						['text' => '💬 WhatsApp Info', 'callback_data' => 'whatsapp'],
						['text' => '📡 Mobile SDR', 'callback_data' => 'mobile_sdr'],
					],
					[
						['text' => '🌐 IP Details', 'callback_data' => 'ip'],
						['text' => '🛡 Proxy Check', 'callback_data' => 'proxy'],
					],
					[
                        ['text' => '📱 IMEI Info', 'callback_data' => 'imei'],
						['text' => '📲 2nd IMEI Info', 'callback_data' => 'second_imei'],
					],
				]
			];

			$message = "📡 *🔍 Search Individual Features:*\n\nPlease select an option below:";
        } else if($text == "🌐 IP Grabber") {
            $keyboard = [
				'inline_keyboard' => [
					[
						['text' => 'Create Case', 'callback_data' => 'link_create'],
                        ['text' => 'View Report', 'callback_data' => 'view_report'],
					],
				]
			];

			$message = "🌐 *IP Grabber Features:*\n\nPlease select a service below:";
        } else {
			$this->telegramservice->handleUserInput($chat_id, $telegram_id, $text);
			return ;
		}

		$this->telegrambot->sendMessageWithKeyboard($chat_id, $message, $keyboard, 'Markdown');
		
		return ;
	}

    private function checkUser($chat_id,$telegram_id) {

        $user = $this->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if (empty((array)$user)) {
            $this->start($chat_id);
			return;
        } else {
            if ($user->status !== 'active') {
                $this->telegrambot->sendMessage($chat_id, "Your account has been deactivated. Please contact the administrator to activate your account and continue using our services. 📞 Contact:  +91 97842 68547");
                return;
            }
			
            if($user->expiryDate && strtotime($user->expiryDate) < strtotime(currentDateTime())) {
                $this->telegrambot->sendMessage($chat_id, "Your account has expired as of {$user->expiryDate}. Please contact the administrator to renew your account and continue using our services. 📞 Contact:  +91 97842 68547");
                return;
            }

            if($user->accountType == 'fresh') {
                $this->telegrambot->sendMessage($chat_id, "You are not registered for the Cligence Bot,  Please contact the administrator to activate your account 📞 Contact: +************");
                return;
            }

            return true;
            
        }
    }

	
}
