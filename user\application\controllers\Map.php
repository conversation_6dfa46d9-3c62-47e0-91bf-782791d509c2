<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Map extends InSecureController {

    function __construct() {
        parent::__construct();		
        $this->load->library('curl');
    }
	
	function view($cellId=''){
		if($cellId == ''){
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
		$this->load->model('cellIdExcelModel');
		$cellIdMapDetails = $this->cellIdExcelModel->fetchMapData(['name' => trim($cellId)]);
		if($cellIdMapDetails && count($cellIdMapDetails) > 0){
			foreach($cellIdMapDetails as $cellIdMapDetail){
				$lon = $cellIdMapDetail->lon;
				$lat = $cellIdMapDetail->lat;
				$address 	= $cellIdMapDetail->address;
				$radius 	= $cellIdMapDetail->radius;
				$azimooth 	= $cellIdMapDetail->azimooth;
			}			
		}else{
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
        $this->viewDataBag->lon = $lon;
        $this->viewDataBag->lat = $lat;
		$this->viewDataBag->radius = $radius;
		$this->viewDataBag->address = $address;
		$this->viewDataBag->azimooth = $azimooth;
        $this->load->view('CellExpCGI/map', $this->viewDataBag);
    }
	
	function viewsingle($cellId=''){
		if($cellId == ''){
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
		$this->load->model('cellIdExcelModel');
		$cellIdMapDetails = $this->cellIdExcelModel->fetchMapData(['common_name' => trim($cellId)]);
		//echo "<pre>";print_r($cellIdMapDetails);die;
		if($cellIdMapDetails && count($cellIdMapDetails) > 0){
			foreach($cellIdMapDetails as $cellIdMapDetail){
				$lon[] = $cellIdMapDetail->lon;
				$lat[] = $cellIdMapDetail->lat;
				$address[] 	= $cellIdMapDetail->address;
				$radius 	= $cellIdMapDetail->radius;
				$azimooth[] 	= $cellIdMapDetail->azimooth;
			}			
		}else{
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
        $this->viewDataBag->lon = $lon;
        $this->viewDataBag->lat = $lat;
		$this->viewDataBag->radius = $radius;
		$this->viewDataBag->address = $address;
		$this->viewDataBag->azimooth = $azimooth;
        $this->load->view('CellExpCGI/mapsingle', $this->viewDataBag);
    }

	function triangularization($cellId='')
	{
		if($cellId == ''){
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
		$this->load->model('cellIdExcelModel');
		$cellIdMapDetails = $this->cellIdExcelModel->fetchMapData(['common_name' => trim($cellId)]);
		
		if($cellIdMapDetails && count($cellIdMapDetails) > 0){
			foreach($cellIdMapDetails as $cellIdMapDetail){
				
				$lon[] = $cellIdMapDetail->lon;
				$lat[] = $cellIdMapDetail->lat;
				$address[] 	= $cellIdMapDetail->address;
				$radius 	= $cellIdMapDetail->radius;
				$azimooth[] 	= $cellIdMapDetail->azimooth;

				$lbsLatitude[] 	= isset($cellIdMapDetail->lbsLatitude) ? $cellIdMapDetail->lbsLatitude : 0;
				$lbsLongitude[] 	= $cellIdMapDetail->lbsLongitude ? $cellIdMapDetail->lbsLongitude : 0;
				$lbsRadius[] 	= $cellIdMapDetail->lbsRadius;
				$cgi[] 	= $cellIdMapDetail->cgi;
				$lbsColor[] 	= $cellIdMapDetail->lbsColor ? $cellIdMapDetail->lbsColor : '#00008B';
			}			
		}else{
			$this->alert->danger("Please check link again or login");
			return redirect('login');
		}
		
        $this->viewDataBag->lon = $lon;
        $this->viewDataBag->lat = $lat;
		$this->viewDataBag->radius = $radius;
		$this->viewDataBag->address = $address;
		$this->viewDataBag->azimooth = $azimooth;
		$this->viewDataBag->lbsLatitude = $lbsLatitude;
		$this->viewDataBag->lbsLongitude = $lbsLongitude;
		$this->viewDataBag->lbsRadius = $lbsRadius;
		$this->viewDataBag->lbsColor = $lbsColor;
		$this->viewDataBag->cgi = $cgi;
		//dd($this->viewDataBag);
        $this->load->view('CellExpCGI/mapTriangularization', $this->viewDataBag);
    }
	
	function callapi($mobile){		 
		$param = array(
			"CGI" => $mobile
		);

		$param = json_encode($param, JSON_PRETTY_PRINT);
		log_message('error', 'dd$param'.$param);
		$authToken = $this->viewDataBag->userSession->auth_token;
		//log_message('error', '$authToken'.$authToken);
		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => $this->config->item('CCASApiUrl','env').'GetCellExpByCGI',
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		  CURLOPT_POSTFIELDS =>$param,
		  CURLOPT_HTTPHEADER => array(
			'Content-Type: application/json',
			'Authorization: Bearer '.$authToken
		  ),
		));

		$response = curl_exec($curl);
		//log_message('error', '$response'.$response);
		curl_close($curl);
		//echo "<pre>";print_r($response);die;
		return $response;        
    }  
}