<?php

defined('BASEPATH') OR exit('No direct script access allowed');
header("Pragma: no-cache");
header("Cache-Control: no-cache");
header("Expires: 0");
require_once APPPATH . 'third_party/paytmLib/encdec_paytm.php';

class Paytm
{
    private $ci;
    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
        define('PAYTM_ENVIRONMENT', $this->ci->config->item('PAYTM_ENVIRONMENT', 'env')); // PROD
        define('PAYTM_MERCHANT_KEY', $this->ci->config->item('PAYTM_MERCHANT_KEY', 'env')); //Change this constant's value with Merchant key received from Paytm.
        define('PAYTM_MERCHANT_MID', $this->ci->config->item('PAYTM_MERCHANT_MID', 'env')); //Change this constant's value with MID (Merchant ID) received from Paytm.
        define('PAYTM_MERCHANT_WEBSITE', $this->ci->config->item('PAYTM_MERCHANT_WEBSITE', 'env'));//Change this constant's value with Website name received from Paytm.
        $PAYTM_STATUS_QUERY_NEW_URL = $this->ci->config->item('PAYTM_STATUS_QUERY_NEW_URL', 'env');
        $PAYTM_TXN_URL = $this->ci->config->item('PAYTM_TXN_URL', 'env');
        define('PAYTM_REFUND_URL', '');
        define('PAYTM_STATUS_QUERY_URL', $PAYTM_STATUS_QUERY_NEW_URL);
        define('PAYTM_STATUS_QUERY_NEW_URL', $PAYTM_STATUS_QUERY_NEW_URL);
        define('PAYTM_TXN_URL', $PAYTM_TXN_URL);
        define('CALLBACK_URL', base_url("payment/response"));
    }
    function makePayment($params)
    {
         $paramList = array();
        $ORDER_ID = $params->orderId;
        $CUST_ID = $params->customerId;
        $TXN_AMOUNT = $params->amount;

        //Create an array having all required parameters for creating checksum.
        $paramList["MID"] = PAYTM_MERCHANT_MID;
        $paramList["ORDER_ID"] = $ORDER_ID;
        $paramList["CUST_ID"] = $CUST_ID;
        $paramList["INDUSTRY_TYPE_ID"] = 'Retail';
        $paramList["CHANNEL_ID"] = 'WEB';
        $paramList["TXN_AMOUNT"] = $TXN_AMOUNT;
        $paramList["WEBSITE"] = PAYTM_MERCHANT_WEBSITE;
        $paramList["CALLBACK_URL"] = CALLBACK_URL;
        /*
          $paramList["MSISDN"] = $MSISDN; //Mobile number of customer
          $paramList["EMAIL"] = $EMAIL; //Email ID of customer
          $paramList["VERIFIED_BY"] = "EMAIL"; //
          $paramList["IS_USER_VERIFIED"] = "YES"; //
         */
        //Here checksum string will return by getChecksumFromArray() function.
        $checkSum = getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        echo '<html><head><title>Merchant Check Out Page</title></head>';
        echo '<body><center><h1>Please do not refresh this page...</h1></center>';
        echo '<form method="post" action="' . PAYTM_TXN_URL . '" name="f1">';
        echo '<table border="1"><tbody>';
        foreach ($paramList as $name => $value) {
            echo '<input type="hidden" name="' . $name . '" value="' . $value . '">';
        }
        echo '<input type="hidden" name="CHECKSUMHASH" value="' . $checkSum . '"></tbody></table>';
        echo '<script type="text/javascript">document.f1.submit();</script>';
        echo '</form></body></html>';
    }
    function getPaymentResponse()
    {
        $paytmChecksum = "";
        $paramList = array();
        $isValidChecksum = "FALSE";

        $paramList = $_POST;
        $paytmChecksum = isset($_POST["CHECKSUMHASH"]) ? $_POST["CHECKSUMHASH"] : ""; //Sent by Paytm pg
        $isValidChecksum = verifychecksum_e($paramList, PAYTM_MERCHANT_KEY, $paytmChecksum); //will return TRUE or FALSE string.

        $responseData = [];
        $responseMessage = '';
        if ($isValidChecksum == "TRUE") {
            $responseMessage = "Checksum matched and following are the transaction details";
            if ($_POST["STATUS"] == "TXN_SUCCESS") {
                $responseMessage = "Transaction status is success";
                } else {
                $responseMessage = "Transaction status is failure";
            }

            if (isset($_POST) && count($_POST) > 0) {
                foreach ($_POST as $paramName => $paramValue) {
                    $responseData[$paramName] = $paramValue;
                }
            }
        } else {
            $responseMessage = "Checksum mismatched";
        }
        $responseData['message'] = $responseMessage;
        return $responseData;



    }
}

?>
