<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class SubUserReporting extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct(); 
        $this->load->model('SubUserReportingModel','SubUserReporting');
    }

    function takeAction()
    {
        $status = $this->input->post('status');
        $execute = false;
        if ($status == "delete") {
            $image = $this->SubUserReporting->findOne(['id' => $this->input->post('id')]);
            $this->SubUserReporting->detach(['id' => $this->input->post('id')]);
            $execute = true;
        } else {
            $execute = false;
        }
        if ($execute) {
            unset($where);
            unset($updateData);
            $this->exitSuccess();
        } else {
            unset($where);
            unset($updateData);
            $this->exitDanger();
        }
    }
    public function allDeleteAction($value='')
    {
         $status = $this->input->post('status');
        $execute = false;
        if ($status == "allDelete") {              
            $this->SubUserReporting->detachMultiple(['id' => $this->input->post('id')]);
            $execute = true;
        } else {
            $execute = false;
        }
        if ($execute) {
            unset($where);
            unset($updateData);
            $this->exitSuccess();
        } else {
            unset($where);
            unset($updateData);
            $this->exitDanger();
        }
    }
}
