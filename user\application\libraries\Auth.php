<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Auth {

    private $ci;
    private $sessionName;
    private $loginTable;
    private $loginTablePrimaryKey;
    private $isSession;
    function __construct() {
        $this->ci = & get_instance();
        $this->ci->config->load('env', true);
        $projectName = str_replace(' ', '', $this->ci->config->item('projectName', 'env'));
        $this->loginTable = $this->ci->config->item('loginTable', 'env');
        $this->loginTablePrimaryKey = $this->ci->config->item('loginTablePrimaryKey', 'env');
        $this->sessionName = $projectName . ucfirst('user');
        $this->isSession = $this->sessionName . 'IsSession';
    }
    function welcome($loginData, $extra = '') {
        $password = $loginData['password'];
        unset($loginData['password']);
        $tablePrimaryKey = $this->loginTablePrimaryKey;
        $query = $this->ci->db->limit(1)->where($loginData)->get($this->loginTable);
        if ($query->num_rows() === 0) {
            return false;
        } else {
            $dbResult = $query->row();
            if (!verifyPasswordHash($password, $dbResult->password)) {
                unset($dbResult);
                return false;
            } else {
                $dbResult->name = json_decode((string) $dbResult->name);
                $dbResult->profileImage = $dbResult->profileImage;
                $dbResult->verified = json_decode((string) $dbResult->verified);

                unset($dbResult->updateByOwn);
                unset($dbResult->updateByAdmin);
                unset($dbResult->password);
                $dbResult->imageBaseUrl = IMAGE_BASE_URL;
                if (isArray($extra)) {
                    foreach ($extra as $key => $value) {
                        $dbResult->$key = $value;
                    }
                }

                if ($dbResult->sessionId == NULL) {
                    $dbResult->sessionId = date("YmdHis") . uniqid();
                    $dbResult->alreadyLogin = "no";
                }else{
                    $dbResult->alreadyLogin = "yes";
                }

                $this->ci->db->where(["id" => $dbResult->id])->update($this->loginTable, [
                    "sessionId" => $dbResult->sessionId,
                    "loginHistory" => userAgent($dbResult->loginHistory, $dbResult->id, 'login')]);
                $dbResult->isRobot = false;
                $dbResult->isMobile = false;
                if ($this->ci->agent->is_robot()) {
                    $dbResult->isRobot = true;
                } elseif ($this->ci->agent->is_mobile()) {
                    $dbResult->isMobile = true;
                }
                $isSession = $this->isSession;
                $dbResult->$isSession = TRUE;
				
				//Insert data of login History
                $historyData = [
                    'userId'=> $dbResult->id,
                    'type'=> 'web',
                    'content'=> userInfo()
                ];
                $this->ci->db->insert('tb_user_login_history',$historyData);

				$ch = curl_init();
				$param = array(
					'UserName' => $dbResult->mobile,
					'Password' => $password
				);
				$param = json_encode($param);
				curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
				$headers = array();
				$headers[] = 'Content-Type: application/json';
				curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
				$result = curl_exec($ch);
				if (curl_errno($ch)) {
					echo 'Error:' . curl_error($ch);
				}
				curl_close($ch);
				$result = json_decode($result);
				$token = (array_key_exists('token',$result)) ? $result->token : '';
				
			
                $dbResult->auth_token = $token;
                $_SESSION[$this->sessionName] = (array) $dbResult;
                unset($dbResult);
                return true;
            }
        }
    }

    function has() {
        if (isset($_SESSION[$this->sessionName]) && isArray($_SESSION[$this->sessionName]) && array_key_exists($this->isSession, $_SESSION[$this->sessionName]) && array_key_exists('sessionId', $_SESSION[$this->sessionName]) && array_key_exists('id', $_SESSION[$this->sessionName]) && $_SESSION[$this->sessionName]['sessionId'] != NULL && $_SESSION[$this->sessionName]['id'] > 0
        ) {
            return true;
        }
        return false;
    }

    function user() {
        return (object) $_SESSION[$this->sessionName];
    }

    function goodbye() {
        if(isset($_SESSION[$this->sessionName])) {
            $this->ci->db->where(["id" => $_SESSION[$this->sessionName]['id']])->update($this->loginTable, ["sessionId" => NULL]);
        }

        $_SESSION[$this->sessionName] = "";
        unset($_SESSION[$this->sessionName]);
        return true;
    }

    function reset($dbResult) {
        $this->ci->session->push($dbResult, $this->sessionName);
        return true;
    }
	
	function userlimit($user_id){
		
		$this->ci->load->model('userModel');
		$users = $this->ci->userModel->findOne(['id' => $user_id]);
		if(!$users){
			$data = ["status"=>false,"error" => "User not exist!"];
		}else{			
			if(is_null($users->expiryDate) || $users->expiryDate >= date("Y-m-d h:i:s")){
				$userAccountType = $users->isAccountType;
				if($userAccountType == 'demo'){
					$userlimit=2;
					$data = ["status"=>true,"error" => $userlimit];
				}elseif($userAccountType == 'paid'){
					$userlimit=10;
					$data = ["status"=>true,"error" => $userlimit];
				}else{
					$data =  ["status"=>false,"error" => "User not unauthorized to access!"];
				}
			}
			else{
				$data =  ["status"=>false,"error" => "User not exist or expired. Please contact admin!"];
			}
		}
		return $data;
	}

}
