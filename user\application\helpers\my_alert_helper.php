<?php

if (!function_exists('getErrorMessage')) {

    function getErrorMessage() {
        return "We're sorry! An unexpected error has occurred. Our technical staff has been automatically notified and will be looking into this with utmost urgency.";
    }

}

if (!function_exists('emoji')) {

    function emoji($emojiType, $emojiCount) {
        $emoji = ['cry' => '&#x1F62D;',
            'happy' => '&#x1F60A;',
            'warning' => '&#x1F914;',
            'okay' => '&#x1F60F;',
        ];
        $returnEmoji = '';
        if (array_key_exists($emojiType, $emoji)) {
            for ($i = 1; $i <= $emojiCount; $i++) {
                $returnEmoji .= $emoji[$emojiType];
            }
        }
        return $returnEmoji;
    }

}
if (!function_exists('happyEmoji')) {

    function happyEmoji($emojiCount = 1) {
        return emoji('happy', $emojiCount);
    }

}
if (!function_exists('cryEmoji')) {

    function cryEmoji($emojiCount = 1) {
        return emoji('cry', $emojiCount);
    }

}
if (!function_exists('warningEmoji')) {

    function warningEmoji($emojiCount = 1) {
        return emoji('warning', $emojiCount);
    }

}
if (!function_exists('okayEmoji')) {

    function okayEmoji($emojiCount = 1) {
        return emoji('okay', $emojiCount);
    }

}
