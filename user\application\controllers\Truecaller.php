<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>caller extends SecureController
	
{

    function __construct() {
		
        parent::__construct();
		
        $this->load->model('truecallerModel');
        $this->load->model('UserModuleModel');
        $this->load->model("TruecalletokenModel");
        $this->load->model("TruecallerSecondaryTokenModel");
        $user_id = $this->viewDataBag->userSession->id;
        //$res = $this->UserModuleModel->check('truecaller',$user_id);
		$res = accessModule('truecaller');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
        
        /*if (strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime()) || empty($res) ) {
            return redirectTo("me/dashboard");
        }*/
    }

    function callapi($mobile){
        $ch = curl_init();
        $param = array(
            'Moblie' => $mobile,
        );
        $authToken = $this->viewDataBag->userSession->auth_token;
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Tac/GetTrucallerAPI');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $truecaller_response = json_decode($result,true);

        $truecaller_data = $truecaller_response['data'];
        $success = $truecaller_response['success'];
        $message = $truecaller_response['message'];
        
        if($success==false || $truecaller_data==null){
            return array("status" => 'false',"msg" => $message);
        }else{
			//log_message('error', $truecaller_data);
            $resData[] = json_decode($truecaller_data);            
			
			if(isset($resData[0][0]->value)){
				//echo "<pre>"; print_r($resData[0][0]->value->name);print_r($resData[0][0]);die;

				$returnData =  array(
					"status" =>'true',
					"mobile" => $mobile,
					"name" => (isset($resData[0][0]->value->name)) ? $resData[0][0]->value->name : null,
					"gender" => (isset($resData[0][0]->value->gender)) ? $resData[0][0]->value->gender : null,
					"image" => (isset($resData[0][0]->value->image)) ? $resData[0][0]->value->image : null,
					"altName" => (isset($resData[0][0]->value->altName)) ? $resData[0][0]->value->altName : null,
					"about" => (isset($resData[0][0]->value->about)) ? $resData[0][0]->value->about : null,
					"phones" => (isset($resData[0][0]->value->phones)) ? $resData[0][0]->value->phones : [],
					"addresses" => (isset($resData[0][0]->value->addresses)) ? $resData[0][0]->value->addresses : [],
					"internetAddresses" => (isset($resData[0][0]->value->internetAddresses)) ? $resData[0][0]->value->internetAddresses : [],
					"spam_description" => (isset($resData[0][0]->value->spamInfo)) ? $resData[0][0]->value->spamInfo : []
				);    
			}else if(isset($resData[0][0]->name)){
				//echo "<pre>"; print_r($resData[0][0]->name);print_r($resData[0][0]);die;
				$returnData =  array(
					"status" =>'true',
					"mobile" => $mobile,
					"name" => (isset($resData[0][0]->name)) ? $resData[0][0]->name : null,
					"gender" => (isset($resData[0][0]->gender)) ? $resData[0][0]->gender : null,
					"image" => (isset($resData[0][0]->image)) ? $resData[0][0]->image : null,
					"altName" => (isset($resData[0][0]->altName)) ? $resData[0][0]->altName : null,
					"about" => (isset($resData[0][0]->about)) ? $resData[0][0]->about : null,
					"phones" => (isset($resData[0][0]->phones)) ? $resData[0][0]->phones : [],
					"addresses" => (isset($resData[0][0]->addresses)) ? $resData[0][0]->addresses : [],
					"internetAddresses" => (isset($resData[0][0]->internetAddresses)) ? $resData[0][0]->internetAddresses : [],
					"spam_description" => (isset($resData[0][0]->spamInfo)) ? $resData[0][0]->spamInfo : []
				);    
			}

            if(isset($returnData['mobile'])){
				$this->truecallerModel->attach(['userId' => $this->viewDataBag->userSession->id,
					'mobile' => $returnData['mobile'],
					'name' => $returnData['name'],
					'gender' => $returnData['gender'],
					'image' => $returnData['image'],
					'altName' => $returnData['altName'],
					'about' => $returnData['about'],
					'phone' => (string)json_encode($returnData['phones']),
					'address' => (string)json_encode($returnData['addresses']),
					'internetAddress' => (string)json_encode($returnData['internetAddresses']),
					'spam_description' => (string)json_encode($returnData['spam_description']),
				]);
			}
			if(!isset($returnData)) return [];
            return $returnData;
        }
    }

    function search(){
        
         $this->load->model("userModel"); 
         $this->load->model('truecallerModel');
         $user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate");
         $truecallerSearchCredit = $user->truecallerSearchCredit;
         $truecallerDailySearch = $user->truecallerDailySearch;
         $response = "";
         $exportMobiles = [];
         if ($this->input->method() === 'post'){
             $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');
             if ($this->form_validation->run() === true) {
                $mobiles = $this->input->post("mobile");
                $mobiles = trim(preg_replace('/\s\s+/', ' ', $mobiles));
                $mobiles = (explode(" ", $mobiles ));
                $SearchCreaditCount = 0;

                //Daily search by user
                $date = new DateTime("now");
                $curr_date = $date->format('Y-m-d ');
                $truecallerDataCount = $this->truecallerModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(createdate)'=>$curr_date]);

                $dailySearchRem = $truecallerSearchCredit<$truecallerDailySearch ? $truecallerSearchCredit : $truecallerDailySearch;
                $dailySearchRem =  $dailySearchRem - $truecallerDataCount;

                //$trueCalrDlSrch = $truecallerDataCount+count($mobiles);
                $mobileCount = count($mobiles);
                
                //check user daily limit is over
                if ($mobileCount > $dailySearchRem) {
                    $this->alert->danger("Your Daily Search Credit Over.");
                    return redirectToItSelf('search/');
                }

                if (count($mobiles) <= 50) { 
                    if ($truecallerSearchCredit >= count($mobiles)) {
                        $trueCallerResponses = [];
						$res = 0;
                        foreach ($mobiles as $mobile) {
                            $exportMobiles[] = $mobile;
                            $TruecallerApis = $this->callapi($mobile);
                            
                            if(!isset($TruecallerApis['status'])){
								$msg = "No Records Found!";//$TruecallerApis['msg'];
                                break;
							}elseif($TruecallerApis['status'] == 'true'){
                                $trueCallerResponses[$mobile] = $TruecallerApis;
								$res = 1;
                            }else{
                                // incase token is got expired
                                $msg = "API Token is expired, Contact To Admin To Update Token";//$TruecallerApis['msg'];
                                break;
                            }
                            $SearchCreaditCount = $SearchCreaditCount + 1;
                        }
                        // update credit
                        $truecallerSearchCredit = $truecallerSearchCredit - $SearchCreaditCount;
                        $this->userModel->modify(["truecallerSearchCredit" => $truecallerSearchCredit], ['id' => $this->viewDataBag->userSession->id]);
						
                        if($res==0 ){
                            $this->alert->danger($msg);
                            return redirectToItSelf('search/');
                        }
						
                        $response = $trueCallerResponses;
                        
                    }else{
                        $this->alert->danger("Your Search Credit Limit Reachead. Contact To Admin To Extend Your Limit.");
                        return redirectToItSelf('search/');
                    }
                 }else{
                    $this->alert->danger("You Can Search Only 50 Number At Time");
                    return redirectToItSelf('search/');
                 }
             }
         }
        
        $this->viewDataBag->userDetail = $user;
        $this->viewDataBag->response = $response;
        $this->viewDataBag->truecallerSearchCredit = $truecallerSearchCredit;
        $this->viewDataBag->exportMobiles = $exportMobiles;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/truecaller/search.js")];
        $this->viewDataBag->jsView = 'truecaller/jsView';
        $this->loadView('truecaller/searchView', $this->viewDataBag);
    }

    function all($offset = 0)
    {
        $searchItem = $this->input->get('searchItem');
        $userId = $this->viewDataBag->userSession->id;
        $where = 'FIND_IN_SET('.$userId.', userId)';
        if ($searchItem != '') {
            $where .= 'AND ( name like "%' . $searchItem . '%" || mobile like "%' . $searchItem . '%")';
        }

        $perPage = 50;
        $this->load->model('truecallerModel');
        $totalRows = $this->truecallerModel->count($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;

        $this->viewDataBag->response = $this->truecallerModel->find($where, '*', ['orderBy' => ['id', "DESC"],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
		
    	$user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate");
		
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
		$this->viewDataBag->userDetail = $user;
        $this->viewDataBag->jsView = 'truecaller/jsView';
        $this->loadView('truecaller/listView', $this->viewDataBag);
    }

    function bulksearch(){

        $this->load->model("userModel"); 
        $this->load->model('truecallerModel');
        $this->load->model('truecallerExcelModel');
        $user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate");
        $date = new DateTime("now");
        $curr_date = $date->format('Y-m-d ');

        $whereExcel = 'userId ='. $this->viewDataBag->userSession->id;
        $truecallerExcel = $this->truecallerExcelModel->find($whereExcel,'*', ['orderBy' => ['id', 'desc']]);

        $truecallerDataCount = $this->truecallerModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(createdate)'=>$curr_date]);

        $truecallerSearchCredit = $user->truecallerSearchCredit;
        $truecallerDailySearch = $user->truecallerDailySearch;

        $dailySearchRem = $truecallerSearchCredit<$truecallerDailySearch ? $truecallerSearchCredit : $truecallerDailySearch;
        $dailySearchRem =  $dailySearchRem - $truecallerDataCount;
        $response = "";
        $exportMobiles = [];

        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('mobile_excel', 'mobile excel', 'required');

            if ($this->form_validation->run() === true) {

                //check user daily limit is over
                if ($dailySearchRem == 0 || $dailySearchRem < 0 || $truecallerSearchCredit==0) {
                    $this->alert->danger("Your Search Credit Over.");
                    return redirectToItSelf('bulksearch/');
                }

                //If one file is processing
                $excelProcessing = $this->truecallerExcelModel->count(
                                    ['userId' => $this->viewDataBag->userSession->id,'status'=>'processing']
                                );

                if ($excelProcessing>0) {
                    $this->alert->danger("You can upload one excel at a time.");
                    return redirectToItSelf('bulksearch/');
                }

                $path = 'uploads/';
                $SearchCreaditCount = 0;
                $file = uploadFile('mobile_excel',$path);
                $mobiles = $file['data'];
                $filename = $file['filename'];
                

                //Daily search by user
                $mobileCount = count($mobiles) - 1;

                if ($mobileCount > 500) {
                    $this->alert->danger("You Can Search Only 500 Number At Time");
                    return redirectToItSelf('bulksearch/');
                }

                //Insert into truecaller excel
                $inserData = [];
                $inserData = [
                    'userId' => $this->viewDataBag->userSession->id,
                    'filename' => $filename
                ];

                if($this->truecallerExcelModel->attach($inserData)) {
                    $msg = 'Imported';
                    $this->alert->success($msg);
                    return redirectToItSelf('bulksearch/');
                }

            }
        }
        
        //print_r($response);die;
        $this->viewDataBag->response = $truecallerExcel;
        $this->viewDataBag->truecallerSearch = $user;
        $this->viewDataBag->dailySearchRem = $dailySearchRem;
        $this->viewDataBag->exportMobiles = $exportMobiles;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/truecaller/search.js")];
        $this->viewDataBag->jsView = 'truecaller/jsView';
        $this->loadView('truecaller/bulksearchView', $this->viewDataBag);
    }
    
    public function export() {
        $userId = $this->viewDataBag->userSession->id;
        //$where = 'FIND_IN_SET('.$userId.', userId)';
		$where = 'userId ="'.$userId.'"';
        $limit = '';
		$from = $this->input->get('from');
		if(isset($from) && $from!=''){
			$from = date("Y-m-d",strtotime($from));
			$fromstart = $from." 00:00:00";
			$fromend = $from." 23:59:59";
			$where .= ' AND ( createdate between "'.$fromstart.'" and "'.$fromend.'" ) ';
		}
        
        if($this->input->get('exportMobiles')){
            $exportMobiles = $this->input->get('exportMobiles');
            //$exportMobiles = implode(",",$exportMobiles);
			$exportMobiles = implode("','",$exportMobiles);
			$exportMobiles	 = "'".$exportMobiles."'";
            if($where != '') {
                $where .= ' AND ( mobile IN ('.$exportMobiles.') )' ;
            } else {
                $where = '( mobile IN ('.$exportMobiles.') )' ;
            }
        }
        
        $this->load->library('excel');
		//$this->viewDataBag->queries = $this->truecallerModel->export('all',$where,$limit);
		$this->viewDataBag->queries = $this->truecallerModel->export('excel',$where,$limit);
		//echo "<pre>";print_r($this->viewDataBag->queries);die;
        $this->excel->writeAndDownload($this->viewDataBag->queries, "Truecallers-Data");
        return '';
    }
    
    public function export_search() {
        $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');

        if ($this->form_validation->run() === true) {
            $mobiles = $this->input->post("mobile");
            $mobiles = trim(preg_replace('/\s\s+/', ' ', $mobiles));
            $mobiles = (explode(" ", $mobiles ));
            if (count($mobiles) <= 50) {
                $response = [];
                $break = 'false';
                foreach ($mobiles as $mobile) {
                    $TruecallerApis = $this->callapi($mobile);

                    if($TruecallerApis['status'] == 'true'){
                        $trueCallerResponses[$mobile] = $TruecallerApis;
                    }else{
                        // incase token is got expired
                        $this->alert->danger($TruecallerApis['msg']);
                        $msg = $TruecallerApis['msg'];//"API Token is expired, Contact To Admin To Update Token"
                        $break = 'true';
                        break;
                    }
                }
                if(trim($break," ") == 'true'){
                    $this->alert->danger($msg);
                    return redirectToItSelf('search/');
                }   
            }else{
                $this->alert->danger("You Can Search Only 50 Number At Time");
                return redirectToItSelf('search/');
            }
        }
        $this->load->library('excel');
        $this->excel->writeAndDownload($this->viewDataBag->queries = $response, "Truecallers-Data");
        return '';
    }

    public function excelDownload() {

        if($this->input->get('excelId')){
            $this->load->library('excel');
            $excelId = $this->input->get('excelId');
            $userId = $this->viewDataBag->userSession->id;
            $this->load->model('truecallerExcelModel');
            $truecallerExcel = $this->truecallerExcelModel->findOne(['id' => $excelId]);
            $search_records = $truecallerExcel->search_records;

            $FileName = 'uploads/' . $truecallerExcel->filename;

            $excelData = $this->excel->getData($FileName,1,['mobile']);
            $newExcelData = array_slice($excelData['data'], 0, $search_records);
            $mobiles = implode("','",$newExcelData);
			$mobiles	 = "'".$mobiles."'";
			$where = 'userId ="'.$userId.'"';
            $limit = $search_records;
            //$where = '( mobile IN ('.$mobiles.') )' ;
           	$where .= ' AND ( mobile IN ('.$mobiles.') )' ;
			$query = $this->truecallerModel->export('excel',$where,$limit);
        	$this->excel->customDownload($query, "Truecallers-Data");
        }
        return '';
    }

    function array_map_assoc( $callback , $array ){
      $r = array();
      foreach ($array as $key=>$value)
        $r[$key] = $callback($key,$value);
      return $r;
    }
    
    function spamDetail($slug) {
        $id = $slug;
        if ($id) {
            $this->load->model('truecallerModel');
            $truecaller = $this->truecallerModel->find(['mobile' => $id], 'id,spam_description', ['orderBy' => ['id', "DESC"], "limit" => ['perPage' => 1, 'offset' => 0] ]);
            if ($truecaller) {
                $this->viewDataBag->truecaller = $truecaller[0];
                $this->viewDataBag->jsView = 'truecaller/jsView';
                $this->loadView('truecaller/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }

}