<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class CountryDetail extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->library('curl');
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('country_code', 'Country Code', 'trim|required|xss_clean|min_length[2]|max_length[2]');
            if ($this->form_validation->run() === true) {
                $country_code = $this->input->post("country_code");
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCountryInformationByCountryCode?CountryCode='.$country_code);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $countryDetails = json_decode($result);
				//dd($countryDetails);
                $response = $countryDetails->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('countryDetail/listView', $this->viewDataBag);
    }

    public function allByISD($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('isd_code', 'ISD Code', 'trim|required|xss_clean|min_length[2]|max_length[6]');
            if ($this->form_validation->run() === true) {
                $isd_code = $this->input->post("isd_code");
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCountryInformationByISD?ISDCode='.$isd_code);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $countryDetails = json_decode($result);
                $response = $countryDetails->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('countryDetail/listViewISD', $this->viewDataBag);
    }

    public function allByName($offset = 0){
        $response = [];
        $authToken = $this->viewDataBag->userSession->auth_token;
        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetAllCountyList');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, []);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Cache-Control: no-cache';
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($cURLConnection);
        if (curl_errno($cURLConnection)) {
            echo 'Error:' . curl_error($cURLConnection);
        }
        curl_close($cURLConnection);
        $countryList = json_decode($result);
		//dd($countryList);
        $countries = $countryList->Data;
		
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('country_name', 'Country Name', 'trim|required|xss_clean');
            if ($this->form_validation->run() === true) {
                $country_name = jsDropdownSelectedValue($this->input->post("country_name"));
                $curlPost = "SortCode=AA-00test";
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCountryInformationByCountryName?CountryName='.$country_name);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $countryDetails = json_decode($result);
                $response = ($countryDetails) ? $countryDetails->Data : [];
            }
        }
        $this->viewDataBag->countries = $countries;
        $this->viewDataBag->response = $response;
        $this->loadView('countryDetail/listViewName', $this->viewDataBag);
    }

}