<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class GstModel extends MY_Model {

    protected $primaryTable = 'tb_gst_data';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	public function select_sum($where){
		return $truecallerDataSum = $this->db->count('id')
			->where($where)
			->get($this->primaryTable)
			->row()
			;
	}
	
	function totalCount($where = '') {
        $this->db->select('t1.*');
        if ($where) {
            $this->db->where($where);
        }
        return $this->db->from($this->primaryTable . ' t1')->count_all_results();
    }

    function getAll($where = '', $select=null, $extra=NULL) {
        $this->db->select('t1.*')
                ->from($this->primaryTable . ' t1');
        if ($where != '')
            $this->db->where($where);
        $this->db->order_by('t1.id', 'desc');
        if (!is_null($extra) && isArray($extra)) {
            if (array_key_exists('orderBy', $extra) && isArray($extra['orderBy'])) {
                if (isArrayOfArray($extra['orderBy'])) {
                    foreach ($extra['orderBy'] as $key => $value) {
                        $this->db->order_by($value[0], $value[1]);
                    }
                } else {
                    $this->db->order_by($extra['orderBy'][0], $extra['orderBy'][1]);
                }
            }
            if (array_key_exists('limit', $extra) && isArray($extra['limit'])) {
                $this->db->limit($extra['limit']['perPage'], $extra['limit']['offset']);
            }
        }
        $this->db->group_by('t1.id');
        return $this->getResult($this->db->get());
    }
}
