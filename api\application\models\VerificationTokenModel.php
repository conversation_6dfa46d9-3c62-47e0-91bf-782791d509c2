<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class VerificationTokenModel extends CI_Model
{
    protected $primaryTable = 'tb_verification_api_tokens';
    protected $primaryTablePK = 'id';
    protected $secondaryTable = '';
    protected $secondaryTablePK = '';
    protected $fkToSecondaryTable = '';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }

    // Get an active token for a service
    public function getActiveToken($serviceName)
    {
        $this->db->where('serviceName', $serviceName);
        $this->db->where('status', 'active');
        $this->db->order_by('lastUsedAt', 'ASC'); // Least recently used first
        $query = $this->db->get('tb_verification_api_tokens', 1);
        return $query->row();
    }

    // Update usage counts and last used timestamp
    public function incrementTokenUsage($id)
    {
        $this->db->set('dailyCount', 'dailyCount+1', FALSE);
        $this->db->set('overallCount', 'overallCount+1', FALSE);
        $this->db->set('lastUsedAt', 'NOW()', FALSE);
        $this->db->where('id', $id);
        $this->db->update('tb_verification_api_tokens');
    }

    // Mark token as expired/inactive
    public function setTokenStatus($id, $status = 'expired')
    {
        $this->db->set('status', $status);
        if ($status == 'expired') {
            $this->db->set('expiredAt', 'NOW()', FALSE);
        }
        $this->db->where('id', $id);
        $this->db->update('tb_verification_api_tokens');
    }

    public function resetAllTokens() {
		return $this->db
					->whereIn('status', ['expired', 'inactive'])
					->update('tb_verification_api_tokens', ['status' => 'active', 'dailyCount'=>0]);
	}

    public function getAllActiveTokens($service)
    {
        return $this->db->where('serviceName', $service)
            ->where('status', 'active')
            ->get('tb_verification_api_tokens')
            ->result();
    }
}
