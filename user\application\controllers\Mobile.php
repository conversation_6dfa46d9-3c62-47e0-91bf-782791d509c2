<?php

defined('BASEPATH') OR exit('No direct script access allowed');
set_time_limit(0);
class Mobile extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$user_id = $this->viewDataBag->userSession->id;
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
		/*$this->load->model("userModel"); 
		$user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "registrationType,isAccountType,expiryDate");
		if ($user->registrationType != NULL && $user->isAccountType !="fresh" ) {
			if (strtotime($user->expiryDate) > strtotime(currentDateTime())) {
				
			}else{
				return show_404();
			}
		}else{
			return show_404();
		}*/

    }

    function search(){
		$emailData = [];
		$this->viewDataBag->mobiles = $emailData;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'mobile/jsView';
        $this->loadView('mobile/searchView', $this->viewDataBag);
    }
	function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $user_id = $this->viewDataBag->userSession->id;
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.mobile like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;
        $this->load->model('mobileModel');
        $totalRows = $this->mobileModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $this->viewDataBag->users = $this->mobileModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        $this->viewDataBag->jsView = 'mobile/jsView';
        $this->loadView('mobile/listView', $this->viewDataBag);
    }
	function detail($slug)
    {
        $id = slugId();
        if ($id) {
            $this->load->model('mobileModel');
            $value = $this->mobileModel->findOne(['id' => $id], '*');
            if (isObject($value)) {
				$result = $result_vi = $result_id = '';
				if(isset($value->response) && $value->status){
					// Array to hold extracted data
						$data = json_decode($value->response, true);
						
						$networkName = isset($data[0]['result']['source_output']['current_service_provider']['network_name']) ? $data[0]['result']['source_output']['current_service_provider']['network_name'] : 'N/A';
						$msisdn = isset($data[0]['result']['source_output']['msisdn_details']['msisdn']) ? $data[0]['result']['source_output']['msisdn_details']['msisdn'] : 'N/A';
						$imsi = isset($data[0]['result']['source_output']['msisdn_details']['imsi']) ? $data[0]['result']['source_output']['msisdn_details']['imsi'] : 'N/A';
						$portingHistory = isset($data[0]['result']['source_output']['mobile_number_details']['porting_history']) ? $data[0]['result']['source_output']['mobile_number_details']['porting_history'] : 'N/A';
						$name = isset($data[0]['result']['source_output']['mobile_number_details']['name']) ? $data[0]['result']['source_output']['mobile_number_details']['name'] : 'N/A';
						$mobile_number_status = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_number_status']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_number_status'] : 'N/A';
						$mobile_connection_type = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type'] : 'N/A';
						$mobile_connection_type = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type'] : 'N/A';
						$alternate_number = isset($data[0]['result']['source_output']['mobile_number_details']['alternate_number']) ? $data[0]['result']['source_output']['mobile_number_details']['alternate_number'] : 'N/A';
						$is_ported = isset($data[0]['result']['source_output']['mobile_number_details']['is_ported']) ? $data[0]['result']['source_output']['mobile_number_details']['is_ported'] : 'N/A';
						$network = isset($data[0]['result']['source_output']['current_service_provider']['network_name']) ? $data[0]['result']['source_output']['current_service_provider']['network_name'] : 'N/A';
						$network_region = isset($data[0]['result']['source_output']['current_service_provider']['network_region']) ? $data[0]['result']['source_output']['current_service_provider']['network_region'] : 'N/A';
						$status_code = isset($data[0]['result']['source_output']['connection_status']['status_code']) ? $data[0]['result']['source_output']['connection_status']['status_code'] : 'N/A';

						// Define regex patterns for each field
						$patterns = [
							'Network Name' => $networkName,
							'Msisdn' => $msisdn,
							'Imsi' => $imsi,
							'Name' => $name,
							'Porting history' => $portingHistory,
							'Mobile Number Status' => $mobile_number_status,
							'Mobile Connection Type' => $mobile_connection_type,
							'Alternate Number' => $alternate_number,
							'Is Ported' => $is_ported,
							'Mobile Connection Type' => $mobile_connection_type,
							'Network Name' => $network,
							'Network Region' => $network_region,
							'Status Code' => $status_code
						];
						
						
						$result = "<p></p><h3>Mobile Information</h3><p>";						
						$result = $this->getDetails($patterns,$value->response,$result);
						$result .= "</p>";
						$data = [];
						$data = [ "pi" =>$result];
				}else{
					$this->viewDataBag->response = $value->response;
				}
				 
				$this->viewDataBag->pi = $result;
				$this->viewDataBag->res_status = $value->status;
				$this->viewDataBag->mobiles = $value->mobile;
                $this->viewDataBag->jsView = 'mobile/jsView';
                $this->loadView('mobile/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			$result .= "<b>".$key."</b>: ".$pattern."<br>";
		}
		return $result;
	}
}