<?php

class Login extends InSecureController
{

    function __construct()
    {
        parent::__construct();
        if ($this->auth->has() === true && $this->viewDataBag->userSession->alreadyLogin == "no" ) {
            return redirectTo('me/dashboard');
        }

        if ($this->viewDataBag->userSession->id > 0 && $this->viewDataBag->userSession->redirectTo != 'dashboard') {
            return redirectTo('me/dashboard');
        }
    }
    function index()
    {
        if ($this->input->method() === 'post'):

            $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|_isMobile|xss_clean|max_length[10]|regex_match[/^[0-9]{10}$/]');
            $this->form_validation->set_rules('password', 'password', 'required|_isPassword|xss_clean');

            if ($this->form_validation->run() === true):

                if ($this->auth->welcome($this->input->all()) === true):

                    $userSession = $this->auth->user();                    

                    if ($userSession->alreadyLogin == "yes") {
						$this->auth->goodbye();
						
						$this->auth->welcome($this->input->all());
                        //return redirectTo('login-session/reset');
                        // Reset the old session (assuming same user logging in again)
                        //$this->session->sess_destroy(); // Clear old session data completely
                        //session_start(); // Restart session for new login flow
                        //$this->auth->forceLogin($userSession); // Custom method to re-login user manually
                    }

                    if ($userSession->isStatus !== "active") {
                        $this->alert->danger("Your account has been deactivated. Please contact the administrator to reactivate your account.");
                    } else {
                        if(in_array(ipaddress(), user_ipaddress())) {
                            return redirectTo('truecaller/search');
                        } else {
                            return redirectTo('me/dashboard/');
                        }
                    }

                    $this->auth->goodbye();
                    return redirectToCurrent();
                else:
                    $this->alert->danger("The mobile and password that you've entered doesn't match any account.", url("registration"), "Sign up for an account");
                    return redirectToCurrent();
                endif;

            endif;

        endif;
        $this->load->view('authentication/login/formView', $this->viewDataBag);
    }

}
