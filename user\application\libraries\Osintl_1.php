<?php

class Osintl
{

    private $ci;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
    }

    function flipkartAccountStatus($uname) {

        if(filter_var($uname, FILTER_VALIDATE_EMAIL)) {
            $ch = curl_init();

        curl_setopt_array($ch, array(
          CURLOPT_URL => 'https://2.rome.api.flipkart.com/api/6/user/signup/status?Accept=*%2F*&Accept-Encoding=gzip%2C%20deflate%2C%20br&Accept-Language=en-GB%2Cen-US%3Bq%3D0.9%2Cen%3Bq%3D0.8&Connection=keep-alive&Content-Length=63&Content-Type=application%2Fjson&Cookie=&T%3DTI167013402374800189945490208679631478892963784789500693138440976215%3B%20at%3DeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjFkOTYzYzUwLTM0YjctNDA1OC1iMTNmLWY2NDhiODFjYTBkYSJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8YWIVSqd8OzQARHBQ-63XhWMabnw_8X3oiVJdb9XJcU%3B%20K-ACTION%3Dnull%3B%20vh%3D896%3B%20vw%3D1792%3B%20dpr%3D2%3B%20Network-Type%3D4g%3B%20AMCVS_17EB401053DAF4840A490D4C%40AdobeOrg%3D1%3B%20AMCV_17EB401053DAF4840A490D4C%40AdobeOrg%3D-227196251%7CMCIDTS%7C19683%7CMCMID%7C62334212218307586113708857726599870671%7CMCAAMLH-1701165040%7C12%7CMCAAMB-1701165040%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1700567440s%7CNONE%7CMCAID%7CNONE%3B%20pxcts%3D6e745d71-8853-11ee-ac06-ada452dc5d96%3B%20_pxvid%3D6e744f97-8853-11ee-ac06-96e5a902a9d6%3B%20gpv_pn%3DHomePage%3B%20gpv_pn_t%3DFLIPKART%3AHomePage%3B%20SN%3DVID65B62DA843F44EBAE69635650DB0B55.TOK3B45D27082E64123A0F1DEC19E5C8806.1700560253.LO%3B%20S%3Dd1t19NglxKhs%2FQTQ%2FPz8%2FPxY%2FP%2F0H0WsjeE4ZleDhbdTlxDsteZ5a2Qx8QJkJKYOX7FjHPs3FKMZ9DXl98AsjLm015w%3D%3D%3B%20_px3%3D99a01e13be2afb5a03c6700c255bf95e016566b77ce6eb4a704c77e2ca33b017=GvcTguM%2FYrbWYmXokZXabL8cB47NXS4puxedjOE%2FjcKj0APLyFvkjE%2B5CRzpAbqw0KpcZ70X3H6%2Fk9vxRilu7A%3D%3D%3A1000%3AtlWFZT1NhWdBclXLdrPJkomNT1CRPuzEhQgcD8WbMcC%2F4dO1Z8vazEGf%2FsbMl9uPa8kaPmZBfWk0hHLAHxeUOB9%2BYAu3Rr4Rnba%2FY%2F4Ddh8ECI7krIijNNA5vz6zkcavlt48KO%2FCBfQgPVF%2FU6XU8fwieIco0T3OwL2lRgsUI9YAAhmm4SyJv9kIC75dR1C2QYt89S3FmGboxRAh0vrPr5ZfUgFt1p13h7u2H7qPJsY%3D%3B%20s_sq%3Dflipkart-prd%3D%2526pid%253Dwww.flipkart.com%25253Aaccount%25253Alogin%2526pidt%253D1%2526oid%253DfunctionSr%252528%252529%25257B%25257D%2526oidt%253D2%2526ot%253DSUBMIT&Host=2.rome.api.flipkart.com&Origin=https%3A%2F%2Fwww.flipkart.com&Referer=https%3A%2F%2Fwww.flipkart.com%2F&sec-ch-ua=%22Google%20Chrome%22%3Bv%3D%22119%22%2C%20%22Chromium%22%3Bv%3D%22119%22%2C%20%22Not%3FA_Brand%22%3Bv%3D%2224%22&sec-ch-ua-mobile=%3F0&sec-ch-ua-platform=%22macOS%22&Sec-Fetch-Dest=empty&Sec-Fetch-Mode=cors&Sec-Fetch-Site=same-site&User-Agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F119.0.0.0%20Safari%2F537.36&X-User-Agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F119.0.0.0%20Safari%2F537.36%20FKUA%2Fwebsite%2F42%2Fwebsite%2FDesktop',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{"loginId":["'.$uname.'"],"supportAllStates":true}',
          CURLOPT_HTTPHEADER => array(
            'accept: */*',
            'accept-encoding: gzip, deflate, br',
            'accept-language: en-GB,en-US;q=0.9,en;q=0.8',
            'connection: keep-alive',
            'content-type: application/json',
            'cookie: T=TI167013402374800189945490208679631478892963784789500693138440976215; SN=VI6F224FB14E324306B690F220AF6D7AA5.TOK5B5979DCBFD842D895C0398850FB2EC1.1670134023.LO; Network-Type=4g; AMCVS_17EB401053DAF4840A490D4C%40AdobeOrg=1; AMCV_17EB401053DAF4840A490D4C%40AdobeOrg=-227196251%7CMCIDTS%7C19331%7CMCMID%7C62334212218307586113708857726599870671%7CMCAAMLH-1670738826%7C12%7CMCAAMB-1670738826%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1670141226s%7CNONE%7CMCAID%7CNONE; S=d1t13LQ0/DD8/Cj8/ID8XPz8cP8prLGk9a4FYOtTZi38iVW0z2wjEAtXdZXa2Tt84MpnWbnOORgOWnUHdRw31dTh2aA==; s_sq=flipkart-prd%3D%2526pid%253Dwww.flipkart.com%25253A%2526pidt%253D1%2526oid%253DLogin%2526oidt%253D3%2526ot%253DSUBMIT; S=d1t12Pz8/GGI/Pz8/Hz8HPz8/P8vpfbsRvm1cVsybI06Z1q/FSlZpeZw7EtNoR/XKg2vfPfQUBqBFVSFpeBqlzjx2Dg==; SN=VI8178D60E89CC4A72BF5849085FCFD4D4.TOK5CD343EF404C4D2BABE1E68C22A07CAB.1700659885.LO; at=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjFkOTYzYzUwLTM0YjctNDA1OC1iMTNmLWY2NDhiODFjYTBkYSJ9.eyJleHAiOjE3MDIzODU0MDUsImlhdCI6MTcwMDY1NzQwNSwiaXNzIjoia2V2bGFyIiwianRpIjoiYmY2MTBiOTktYjU1ZS00Yjg4LWJiMTgtNTQ0YjYxYTg0NjkwIiwidHlwZSI6IkFUIiwiZElkIjoiVEkxNjYzMTc2NjQwOTI3MDAwNzQ1NTMwNzk4MjcwNDM4Mjc0NzE3Nzg1NzAzODA0NDgxNzU1NjM2NTM0MTU0NjgwMDAiLCJrZXZJZCI6IlZJODE3OEQ2MEU4OUNDNEE3MkJGNTg0OTA4NUZDRkQ0RDQiLCJ0SWQiOiJtYXBpIiwidnMiOiJMTyIsInoiOiJIWUQiLCJtIjp0cnVlLCJnZW4iOjR9.9iROPGQMYayGxuHi5ZycvEZ8I3qLnairT8ZQ23Dn8Sw',
            'host: 2.rome.api.flipkart.com',
            'origin: https://www.flipkart.com',
            'referer: https://www.flipkart.com/',
            'sec-ch-ua: "Not?A_Brand";v="8", "Chromium";v="108", "Google Chrome";v="108"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "macOS"',
            'sec-fetch-dest: empty',
            'sec-fetch-mode: cors',
            'sec-fetch-site: same-site',
            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 FKUA/website/42/website/Desktop'
          ),
        ));

        $result = curl_exec($ch);
        } else {
            $headers = array(
                'accept: */*',
                'accept-encoding: gzip, deflate',
                'accept-language: en-GB,en;q=0.9',
                'connection: keep-alive',
                'content-length: 53',
                'content-type: application/json',
                'cookie: T=TI166317664092700074553079827043827471778570380448175563653415468000; Network-Type=4g; pxcts=f55e9439-3452-11ed-a24f-46784b437558; _pxvid=f55e8858-3452-11ed-a24f-46784b437558; AMCVS_17EB401053DAF4840A490D4C%40AdobeOrg=1; AMCV_17EB401053DAF4840A490D4C%40AdobeOrg=-227196251%7CMCIDTS%7C19250%7CMCMID%7C68070055222956299347679550210400884410%7CMCAAMLH-1663781443%7C12%7CMCAAMB-1663781443%7C6G1ynYcLPuiQxYZrsz_pkqfLG9yMXBpb2zX5dvJdYQJzPXImdj0y%7CMCOPTOUT-1663183843s%7CNONE%7CMCAID%7CNONE; s_cc=true; SN=VI2E5025530AB64026B48AD76AECD2076A.TOKEC14933A180040EA93308163CDA7A144.1663177186.LO; gpv_pn=HomePage; gpv_pn_t=FLIPKART%3AHomePage; _px3=7c308e06739981d4338c808d1356f44551c7a8ac46310a83f7899accb63c71f7:WZ0Ils6Pb8EtphT+68dmUvPYem1+zrlJkTKxUaFoQPb/Mpc3RPa5rEWacg9DS0YWGbh4ks/87XMYcAFxv2eQZA==:1000:hOcBIEUZrcoHUetNSQfhm9CTl7ebl2Y+95odKZo+fhF28IOLC4tYXG65qjIrXpBd8vwCZRc8jlF21bqrLHysIPdecmS7jifFmVOgJMegKP8Nc3t8pZ2l+NGzuiktTaZOkoMz9867h03Rf7FYPVtkLfotNvY5pTxUPg1toGbu2HYirOvhEj4KO6pp5Era0gzrPThgQZHd8vc1WFK3nRFLRw==; S=d1t17PxU/GD8/UnUlP0g/Nz8/Pztw+Yi5wKEqLePWdi1naro6jYvVkscaJ+Vv6ugwqJ58KJgVM+OHSwqbsVLaF8MUHw==; s_sq=flipkart-prd%3D%2526pid%253DHomePage%2526pidt%253D1%2526oid%253DfunctionEr%252528%252529%25257B%25257D%2526oidt%253D2%2526ot%253DA; S=d1t17Pz8/A30/Pz9+UiY/Pz9jP1PkiR5PK7h/NOQHYNTWU6aVrrrRWj0ndu1MSZg2mAmZ7Vk4aCccl3GxGwDZ20gQtA==; SN=VI2E5025530AB64026B48AD76AECD2076A.TOKEC14933A180040EA93308163CDA7A144.1664536984.LO',
                'host: 2.rome.api.flipkart.com',
                'origin: https://www.flipkart.com',
                'referer: https://www.flipkart.com/',
                'sec-ch-ua: "Google Chrome";v="105", "Not)A;Brand";v="8", "Chromium";v="105"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "macOS"',
                'sec-fetch-dest: empty',
                'sec-fetch-mode: cors',
                'sec-fetch-site: same-site',
                'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 FKUA/website/42/website/Desktop'
            );
            $url = 'https://2.rome.api.flipkart.com/api/6/user/signup/status';
            $postData = [
                'loginId' => ['+91'.$uname],
                'supportAllStates' => true
            ];
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 0);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
        }
        
        
        $response['data'] = '';
        
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
            $response['status'] = 404;
        } else {
            $result = json_decode($result);
            $response['status'] = property_exists($result, 'STATUS_CODE') ? $result->STATUS_CODE : 500;            
            if(!empty($result->RESPONSE) && $result->STATUS_CODE==200) {
                
                if(filter_var($uname, FILTER_VALIDATE_EMAIL)) {
                    if (property_exists($result->RESPONSE->userDetails, $uname)) {
                        $response['data'] = $result->RESPONSE->userDetails->$uname;
                    }
                } else {
					$mob = '+91'.$uname;
                    if (property_exists($result->RESPONSE->userDetails, $mob)) {
                        $response['data'] = $result->RESPONSE->userDetails->$mob;
                    }
                }
                
            }
        }
        curl_close($ch);
        return $response;
    }

    function amazonAccountStatus($uname) {

        $headers = array(
            'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-encoding: gzip, deflate, br',
            'accept-language: en-GB,en-US;q=0.9,en;q=0.8',
            'cache-control: max-age=0',
            'connection: keep-alive',
            'content-type: application/x-www-form-urlencoded',
            'cookie: session-id=257-9317989-0903359; i18n-prefs=INR; ubid-acbin=257-8978641-0463560; lc-acbin=en_IN; session-token="/R4Fm0lN2qrDWS8WWqx2q+E2ztkxqyhNj3ULFq/ARlfceYRMoSCYcqWVfUb1hFcFjEDXM47802AG7o/QBPRLnLQR8QEvU3qZS/waPYW5bmXLJiIIVzUEtn+FgCFGkDl7Qg3Udl7mdK/f+zZ0TmfKthyZ4R/SeUdJdTJq5geJQr283CUqPA9EE23rKSjCwIzYbL63P7MjQFQ8fAdUmBLIEtUhgSP3zzCw6YlriUtPjKc="; session-id-time=2082758401l; csm-hit=tb:SFZ09R83YVPCSCKRD2E7+s-0XPG92SX4KVH9RYWPV5Z|*************&t:*************&adb:adblk_no; session-id=257-9317989-0903359; session-id-time=2300966363l; session-token="z7jetHFAZs6NhPl74B6KE9DaRB4FFCaODgbo7dzUhULib/Rd6Vxp3r50qoSr17KbdExc3o2OcO/iW/mCchU/F42MrKIO5YGB/xiGlE/5aekJUx/mEF7UfEDb5cYXXai9XwBHO71Yb3Ghd9OhnUJx0KS/nt7T/5u7gmBhuiEtaSM+v3mItCsbhQsROAfM3lA1+AIyNBfBPmO3WCgOMAY5I642pPJhRV8f9KawkQRiJWU="; ubid-acbin=257-8978641-0463560',
            'device-memory: 8',
            'downlink: 10',
            'dpr: 2',
            'ect: 4g',
            'host: www.amazon.in',
            'origin: https://www.amazon.in',
            'referer: https://www.amazon.in/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.in%2F%3Fref_%3Dnav_custrec_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=inflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&',
            'rtt: 0',
            'sec-ch-device-memory: 8',
            'sec-ch-dpr: 2',
            'sec-ch-ua: "Not?A_Brand";v="8", "Chromium";v="108", "Google Chrome";v="108"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "macOS"',
            'sec-ch-ua-platform-version: "13.0.0"',
            'sec-ch-viewport-width: 472',
            'sec-fetch-dest: document',
            'sec-fetch-mode: navigate',
            'sec-fetch-site: same-origin',
            'sec-fetch-user: ?1',
            'upgrade-insecure-requests: 1',
            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'viewport-width: 472'
        );
        $url = 'https://www.amazon.in/ap/signin';
        
        $postData = 'appActionToken=747X4PQnlj2Bvj2B4lePF9NImavfbPkj3D&appAction=SIGNIN_PWD_COLLECT&subPageType=SignInClaimCollect&openid.return_to=ape%3AaHR0cHM6Ly93d3cuYW1hem9uLmluLz9yZWZfPW5hdl9jdXN0cmVjX3NpZ25pbg%3D%3D&prevRID=ape%3AMFhQRzkyU1g0S1ZIOVJZV1BWNVo%3D&workflowState=eyJ6aXAiOiJERUYiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiQTI1NktXIn0.ve_KiR7lRyW1ExJtDsegGLvGaQZ8r46ndWXTHMiaRuIiDaouabCQCw.9kxY9H5O9zyT7g-r.TNb3esj5yvX1QXlWLh-eV45uhRsH8a_FMszZOTqDJu94WUiXtOJw5eapm-Bu-ZbZX47cv3vyIzRDvIDwOnptRW_uFO4F20Aj1z71cftyW6qaSIBEUdMt-Kydk2b9_67S8BarwhqqQGJa6GjeT_WGZPTGR5ay58WYTe2xGBmjywZo9pd3sfXaUUOrxOKSGlibJLiuPnKYtqPulgMK1OrSoDGlemWElD5dw61KB5-c5GUbWnn_aKk6h33zBY-Y1FAq8HWOhFOEVCpDtd6CDq9AF5hWzIlWelcT7BDoICRl3AzjG4NktaJ8tOZK0k4RpJ4JPfMNTUH222UHbSnyspABeupOHa9b2V1JdbVL4PPliW1Peg.MiNbJZS2hc6eFA-roWjJuQ&email='.$uname.'&password=&create=0&metadata1=ECdITeCs%3Aa7bWhR2iep%2FvxSe0KtKGQK6dnySoo55SRVn8Wp1Dbe%2FaxUHm9OD53WUo105jW3gptHdcAPbEJ6lvGTUHHYKa4HhLGE%2BQSGeqoKUiY1Qj3JyYPTfoE%2FfkE6KRYvzfX8qKQT2j6vSneMNOghcETegwmAvDhdYykm19WjivfEWrI2XC%2F1Z9FxmkdEL%2Fv2zRPN8HerGsUfTac0jN9oQhMWzfvF2Gku2OKSFi%2B14CDSn3p9iSTCWPtxGd1USGF4Nfghb%2FDi4oPwjDGKf5xMqwnSiGI7DVt9hhpoESb48uvhuCbW2vNerqTtKUeJLaqhAghGk5oCLlQUJJHKGJJgu1azBIXeEkn6DHGhFtujRwIklRvqmAYwu4exgReYfuVQIqIx7ZPlqbp2Xodj398zuUl2nQa4aZ%2BOZ9Wh48jFVlKZ2G%2ByHoTYOpGIpQvjPYV%2BdN1R4WYFtld6aZqfWOWiAhyIMkvSkrrL0DaeY8JLuV9yZe1gvcF38wzDUfMEGlfjznUXUc3R40gqFH7KbhtIo2FQJQYtj7SHCKYJ2uhoHGgTzzvYFntVOJPqeI7OeJ8a1hAwKiBaDC3Z%2FVrMc3UPRPvZU983hNz3NyfbUcrA2qqI52OJ%2FuuMW5XjFYgUyB7uQJeLWJgMSOWwWJ%2B0e%2BHQs1BIrRMwAX3PIRHYbagN57GLbD9C5Bye23kRYDdWrB%2B6SjWtT%2B%2F1BTz5ensPVuN16dMQqoRk0tatxovRV9aXoNER54ZcnY1n6B1Op0ulnuwe7RmNtL2PxdycHEFhaHqV445B3yFZA%2FxlHU2jBkdqujAUbPMVbAbbUaAjXBWBpWk3XRzLvypjZDcJlAMCzPLOyWpm2UFMlkigO0TXdwNjlxvrdV7HIbmDcgqunteRt22p6GhnGfwtDw4L38A6r8aZbNLsool9BIfYihTiCFfMXE7IDKD%2FPx34MLSxL3m41kX9uDwvun7VMzGgPEa8oQUn9%2BmwmCSftXVgdvQiXMnJZIyyh06Lm01SOGiogh%2FJO5%2Fbu6Vbdn412zSBo7TK1y8N0bx3MP9Zh7hPqTCo%2Bc2EamkjedBd8q%2BemnnW7qXNktXSD6YqPtpET%2BEk%2F%2FPaylLCtmISfhvZiB4PlJtqtLX7ej%2BkVW0%2B3HJrq%2F%2F%2BEx8hgtA6zd5CrVMhXiLnCzrg6OkrhviWch1VgmcqzESYf54ieS6aak4fNqQ8x3Z66aaqH%2BhrSGQhW0GqbcDzhth4yUD00JNVtNkK4owoS5Tz%2BOam6tPRzrAXYtjSuXxUt5DD2GsmHNsocEameqc5013dRs3Gh5VDfzBQ1p41GgQgjgDkNVlW5Qq3Fp97SrSVLDBfTf6pIcuyR2oKXFzsx4umpRwpAC1FvskPIIE2pA3G1c7BeAKTyH3L0MCR2DrsB9Bz0Op3k4dMMQnQQQtH2yb71P5HRYN19O4qgK%2BTBUbo6Y7nDT4iSCAkchOnSHmilB2xKmUOSDxerloEBhvoYsy0%2B4pEqWkUo449pcpR2o4n%2FHd4ey1ahlE2AaCJCLglGIz7LMJnpgAnSCqudiosMJi3J1jhRgxiNEu6yYLY1TOXlFETBUUez29X9rhl7v4uxh8RqgDIG%2FK0J3FI1dw3Z6U88MiAy0jsHt9OUzDEKX2nhfEs9EIVbhdBEsNX%2F7UFilByrkN%2BJ4Vq2JVCyXeeCstHh0SkinkfWK9z56ZTtWNh8%2F1SWvrTGXlKuxjhqmeVSFB9N0VIesgCfl9agRuNq%2BNFxxRT8GjVdawULHH6ygtaImVzSqVWd2LdRxv2xgjuaDml6LIq2UG346tEeIOqq5twcmLreaZxjVUD8JcK%2Fq6tiOuGU1h2Y85cNnMu4CyJh165Qk1clRBpqUwkAe%2FgPOkM87ImLuF9UOSCYC8Nd7MWJIm3NiLYKa1YppkzAr2CoriwxpZISNaZvEfRws4p6u2xISHN0SFgoLGmJUl5QzD5J0XUwWrSrFEXyMFWmqKJpxmhMPKr0TXnxdm1qXAIIPTKhMqXzQs3nn7W4cMHiGHIzg9h9oTThc0O1ogRH3qIdi%2Fr%2FV9dzsRDGf4xh8cgM6padhBpCnXH%2BxyapakpH5KlvtJU1jV4t6h4Df6nEyI7BP8pWvpM91JM6loLNE6FaVpAQJK1MsylZtPZ5myZGpCUOlZoJCMJzyRNfBxjf3uTcXwpH1OPTwCGiZCog93z6jhROAlPerKkV1Jkt4ef8kCrL32FeLMTwIRFI3pkBxuy%2FWi8opc5ix5CnupG1tNQ22wHB%2Bc93ZUQidHanysGWn1mXMfdaW8Bi9tM1RtcltE4OTYqlLCF3%2BKC1Ts8jwKHZfZQl7w44SnoeAOF%2FMk8rUvX%2F1CyItUU2UT0aeyHyrNlaoDPndMm9ah1CyL03RrSdneM6P7p%2F36WJI1sv3BBrNhm8DHaX9fr0%2Bf7X218K9WfuzXbvdz56%2FQWS%2BgfzezUY%2BCXzilznHvDag4b1grRFDmiFMSBRyzuqN9ycJsC4AJarO1JACtiKmvRRTOeLI0Y5qR%2FRr0Tf9M9l8fJzkhcfuxcU5PhlLJirJokqBal3Cw85c1pbtmWXnmJt%2Fhkt2RwhSmRq%2BesLMpjGHXwKn1mb9dhMbB1NVU%2B09e1i8%2FjPYxUM1K2pZF7F3Qcj71kt4BPO9Dzredr70U89BYTzWRXfiNF7NsFiIICvUXSfcxQvVjHmILBOHQ1G6KX3lMX7Qoly0GLvmTztBQmR1dWWE%2BwRU8%2F0mken5t4UVyJvkT4ANcFAsGZ2fL%2FOwzBQWpcjXoWsAD2HG%2FSNBsr%2BIxAlZdcU4HZwjLwmZyY2UfG2F9AFDMeVt9jjlr2QM%2F4cBifzqzFhiDSqIKPmOmpF0kN5iPMjNe%2FNW8OoUNUP0212f8phDEkkZJbvB9t%2FZHKuwJH3Jwje3gi3rVgWr9l20xtO%2BNfLHUuYUGKQZcQOh6L%2BjHJDVLcXLVRriB3P9CnVzFxu0KonwTeBKtiArtAuQ5zhodYfWMlb%2B3vc2yUH32AtDFQx8oYjdgI6ym308LMdZO5bw5kLtLYspOQPh%2BTPCotCbg8lqKJW76PgmM5ennL82l0RUGU8sH1cLPgoZwrOXwDT3MnYs0GO8HUR0w764G2eGRn2gHGFXLoLSJh3CxRyU2MuQBrmuZBcXCF2m3Z0cYW3gRlrb9TupEaxVWpFxn19x1nsmMI3O3omixt%2FM3EJ%2F7m05HuMXjWMX4fi3BlqvKO7WJ5GhmQjAJh8Odc3cgCt0q1PXL599XVIhusB2FIxtnY2c9BewcRSwDjqPryf7my2TsMjYqyFJEMIBHi2FY4xKi%2Fr%2FdpYNBjLSdGC%2FfPU1D82Xs2vxDhmGL2o6r3wCz1cDFY24N2b4dkqrqXrzePVi1sR9ONth4mYofDlBEjEwh2rEpLKYS5xpxk4Fvisfu0TpVGXI5wdaYy3RBfD5FqBHgg9iUDg%2FJo2Lew%2BVj0y8NXdBGjJOE7e3RC4HcGbYSiSTnpjhuMtWwL7FRhWjoC1fVSEv%2Btx9LGmmmWtWkfEYxPh7SVCilJBxnuDtkjFyIJIPB3JvMV%2F6L9nvgfK2DSUPSNX8MAb%2FlMBO1%2FMfUznipdd5mypsIjITPPCuPqUD%2FkuxmgEn8wH4GxSo%2F9D6nyQDi3yXHX4yP582LtvAtMZlPZTl%2F5RJCr0LBSdFGZZR0Ou0k8oHsnddOFxbs4RfZH3WjB7ClJVdd%2BOiJax3JPtzvYGiTdh4hyytCCZ5VLvrO3PPOCRDU2ibJRJwt%2BGDYwC%2FPhZLTQe%2BVjhfd8G82qbEP7lV%2BIbRAMYvU3%2FXv75KNoDbnVEc%2FRacoxhhr%2FFJz4Ikoo%2BVdSuuXp1C%2BNMkrFMq2wyj4%2F672a90K0uLuH6KiTLPNPUYtcnVp4JE9xJp4nPrAUplbZT%2FFBLukHjEVmuxWzSLQ3yWOqJSuLPop314bNQ%2BMP4nsV0DqTz67Uq7KTkzhiKgZqYP29RKTX3IATpoU9YeazlXjwoDI9l8GCeyl7t0ZHlAMqpD7xjV%2BEO71roBIEInkDfoZsnFQFa0dJn4pYQvchQsTspWklO7m2efRIohItjsCg3Ek5pMNGw4Fd9vNxK7KzwgkUt8%2BEY3Wr8QYNKQaTYNXZKsoVazfVBUEiSFdPyVcOKcoqG6%2FjzWCCuIq1l7%2FkVli2rDZppV5eZCeH%2FyDA%2F9XdiRz59bZQhVmnLWO94JiIUrL234JMKxUKhYqYNtzXpRvXkutUHtanyfnqhJqbKeL0uU4o72gYACaKLygByKhhNyv%2FdDMvQt%2BSLa8Iv7HYKZ%2BawTMJC%2BBs88oOjvXj3xqVkqTRIF1WzrP2Xx1DFr4m24Gjgn9AHJ3H8l3uUhQyLeUtaCPYvXhsEIpJQppkr9AzDEcDAMMkv5atJtULW9U8ktGdxCZxkCNXMYaXfpmw0CpsA5d5EVO%2FbdtP5v6eGCcJ8sabkNw7ZTEcljI%2BrHGkJNSryJzLSO%2FMxLzLdooQp3epTcQ%2BA0iGM5gZEOM8Z9yz80lbjB%2BOdiZ4MLmRZ9zHr8p9IXQoz5H5af%2FyVfUySkaXYdpY%2FH709D6BvvnaJ03AkL5SrfA9auloBjzo43e%2FDUQp%2BnQCTkhYPiSkRJS8aJYZ2iqNeNGbIAaYu%2FOY%2FNH3HaTHoAZvI1OnkpfOuUYQ56re0JrAkNLdlT72KHtcy3b%2Bf434fN7H4AGSZ6Uhqexi4m%2FRY8SW0ygk2DBQY6Ci5JRdCOVHjlT4VbFYQVywUj9YaKiUgo7cFh5MI7lbk8BJ7iJStip%2FUUweMf9pwkki8ci9rxPX8DEeg1erx7EGw%2BKiK8%2FGIGTHnZgUqRcBSTIqW5Y5Rlm%2BwRh3mO1TWGWSYHgbIhHLc8XOGzp%2Fi%2BBjrHsC7vk6O65l%2FXytAZUdefeYeuIg3fCcjrou76ep8KgPIcww8pE25MCkZT3CempANp2uu%2BTAc2TXRelkJZcbDhBTbwH6QetG9g%2F45CdG5SKSP4aMa5xvAp3XoaO1BiYvU0Wu4pEwqGqAJjBmfdp313XwUtiuFs%2BMcGR7bi5Sw%2Fc0pmJZMD8v4m0BGMzwFSg3SzzetufQZsS%2FSsnPNy%2B2jgqTKiW%2FmKxIba0d5CspUnL4d1ChGt1FTWHcDM%2BHECR%2BijvJRfoPMxJtR%2F7GN%2FOa9LZJRpE%2B1%2BvRmONuFOMN2Qy54hhlpMIhwUoZmL7oesms2k4atJ7MBeXwdW9YI0hjqKshtdkYwTgCJvffxs5eAAVQhYBY9XXEs8LSCAotkWqr13i3dCSSpuHCXifjZkHbig1pBWdt%2B8shNhK6wJJhezjKxG%2FfN7dRhbQWk2%2FAkzYhNBTbMPMW9vYenCjobiNyZSfeg1mW%2B%2FpNC6PpSEZrelcVrAuY%2BRQSBbpmdg0F%2BVjFROoDWYqQwdilCwbCpb4hV6zKQBoeLTmlJ%2BG%2FM%2F%2B0n5%2Bfduh8huqhqVC1ytZdvzcD5CS7ous25xCsnLwg7vAcmFSSWZnT2Zxvstx2eYr9Wdi1kgEgXg6dqU1WSJKCmF9%2F00kszlXZd3aAYF89F5RZ00Lx5KI4axE2uY8kcKy%2B3sy8ZxbI7zlST6n3CKTtJLX14k5INQlgjjKJF%2FFI%2F66UgeyswmaPC6goXFgRNgRrFGQHaF1E3eZsvwVzT4ksXI5%2BOUdpNtEucSJOnMqXH1oNq7QNfbM5hcEXoAGGCBt1FWXPVrTbMzWUrIomLrpgLHYV7XfEcqzPdPLLmqX0L5hQMgyjgtD8VV1eIagNvGoGOAPbOIEzQtGM61laCL%2BQCVQwZxB9%2FJvDJblTbYP5%2BPVNR1hWB3Si6J2LarLo%2FFe1bZDpOfitb5qpBVak3ZLOdHaYfCzeEDrV8wRqKoZxWcZFksPtwk6ekptPD6%2Fb8ikWpqgr2Zex9TzfBbDkr2OflYLJjCS%2BuhZQb1VtEyRdBboPpQHqcMMuOjn4NaCyaT6ZUUFNE9BxBvGO3FFXGPcPq%2BCMN2yKFdKWLa8nEmcTEJTtfrsp4lfy%2BqB8VYSOgAihc7vqNaCJUKodgoQ2MRpJbMr6iQak0HMMA7MY4vRJ6Qh67MAOxb6bHkMrhsMXWzGBTaVLcMAlL%2FvcxT%2FuuBHM8ZRHYju3E4gSv1xOfL0dk03BNJ9Rai26Qa98xh0pu0oiVRiDLZzQWh%2BGI800Nl7fYsE%2BCywuFCbdbsJuARDRD4sXTat4GQ%2BUeN5%2FmjHZ50FYPHT4JMZNVnoQ1VWOiq3l8gXZl89jwI4cje2nFUjb2Ou97YucTITum4WfJEy7jUpcqOK8x1HATB1j6N3QquZfF8NIeYkvA%2FrDYQYgk6zF%2BC4TkbGnr5BVmk3eTyjmcJNJZqe7J%2FExSWuoyh11NEaLK2rYZ3u4ho6SM4WVm5UWhSoHqpo73OEIA%2F2G8zUBhMUw4Pvj4gI3HSS6RyM0QaFH%2Bw4NFrH%2Bs4Zo2WYCVO%2F2Qro5V55kQc35OTUd1plwtgrJA0AyPXGUyPqvlTgoNRdDk14KNKxTp8BvWq9ZEfIxPGC7fSs42gh9%2Fc9wtZR4NKKECnr4aTfmYIDXaHWbYQpsRoBzWbr1ZPRO0mJHEIJGofCsPCEX%2Boqns6jLWMTX%2BTpXKhV6YQgQRXzLeWDCS%2BcHg0MXNuVfuUjfUeAgwwG1dq85T9zgyIezKhode%2BN0oOeCcfG%2BnFGVhZHNylqFkSZPBcs3vSlKoHlvQUJMX1hcYJCchnqkwVIOzBb5cMkKAlCNHFM%2FMPOMbhpudAdMgGJBh96LqfgSNJov5s7Vne7SeKqludjDZFO1GU6lkxlE5zfwgkxPg05furqDfKIU9q2JeCUJGsogqVthNYrCcQ%2FeyoxiAmWcEmCZ6Nt9%2BFor7vs2LQ2NlwERMklQESEIdvtQ8e5jzT%2FCRPqmunGnYUKJHVcJKPTRiHsgHqTJWUk7xF6hBDw6NublMs9tchn3YZtE8wqQ3ZTP%2FvYUdU%2Bdzlo8NSoh6ZKiFZ4ngP%2BeG9F8C20VQETRg0Mekap8Yfg36dlBg6pQW%2BAec1B7dXKdpf8YL8Fqvp70INyc4rd6gyjoCWEjP12%2B00dyarJEfAsjvEuMhazxm4HgZzUDwd4Nl4kTRQTo4xJ27wr3dCUWpa19z33D0ZfABetBt%2Ffwystlf9HUOwMW8WoFAsKrgZdnRecMeSAdVeTk2LP7iglpeqhR8JfbW17OlwzDj8DSXd%2FZT6Yv6clQlZ99uVP0m8TlRV2pBBzGB8djZ9BmzTMqNWj4WlaryJrVU1fnKZK4S%2BVR21Hb%2FRdTvXVto%2FIbq%2BTcRPQkdhWyOF6koy%2B2pZIrp6V9FxBIIu76v9EwzrsppsZKVYO41SK30G8kEBq7INwXvDvB7XUrv%2Bp%2BrAIg2zHYp3KnukU8KzTtLymky236j92boxi5A%2By3aY3Vryc%2FARMm7poq7hrnltm8CmtKangfK6I%2FIgA0HHFthzUlJHOagdAsUNfX1i6Z3QwY4v60YUcEjOA%2BwUj84xuiiLNs39REV3hVy%2BmIVTBLHhwVNmzDVOILnpU9q6d9VOmaHN9CRi2jY4GPqOXUEYjTi1CrPhIH4n08ceSCyXWQt7dAGsJLvJVY6OsoC7FkIuE%2Bu0JNmu6n6NdF7seDnLgeuHJnk%2BxTOz4PDpsrF%2F7Q0NB4DqE7BU1MV%2Fb8JXPEc1O22vxgRPQxyy8LR%2BUGn5VBqHzhRDgDlNB1Jt2m%2BC7tNUpI3Oe6lrT%2FOEwlIcMeNzBb7uo%2F5VhSe3IhCjioVzkxrC8LECKikoV6Al6GarE1ItK0WuYmMkQ6OyZqSvGz7tpq1N8UkJn1rV1f8iHvkf%2FMw2i888DrGEGsWkW0MVqzMEZHEIfVfITwJ8%2FXNcQIR8iDviFOfNUKWd9vOEs6skEo3AtYWU1nrexgktoPEO56v%2FL%2BPJZRz5%2Bxv5EQsVzq6Cj0Dj3IZj4WjyynZhC6qxH0nfogPjZUSb7oRqlJzlvj0L7daaNpzRoSkuLsyn0cJq9VGhVPdXSHlBGCzL%2FXTbZVrxuZg%2BxJ%2BZZVjI9QfQDCPDuFGdGGqA0jC%2BX4vH0tpG4tIZRi3AKGOMNS%2FkB5tSEgnP1E4v%2BFCc1JiHPkI4OfBoiKSnY75RHsfVLe7STI2lT8zWGpxo%2FlMlJNaR7E%2BMZ1PB67%2BcPTNGI15hziYPFPWlFBhhg5isM%2Bqq%2BFPoevfm%2FLCq9tLQsgbqmuyj7PNBMSNSAKd56HYB60zTwocz3Jp3FkeiPHD2rF17V9PCGvHGSAtFFBeAXn04%2FYnouHCKSUHqQhjleK01GcB%2BSXP30%2FI8TRdnHMSMiK3fvigHDI25LvX2Z2g08McwXLHsop7vXp4pPhKRZvAL951hL3AEEHyDY4pm%2B4yy3JuFyhQ%2BE652wxs7qzDzeWQvn35tRNGxVBEHm84cGz0YPj3YdjfYiSgrksdK%2BWdShs1pQdGl5XlQhgg6S6L7Aj%2Fws720xr5lmtG0gRWodUYzH4qwoURAXDisTIRZK38%2BHUhWa0JWxroNK8JTMS3SJIjxoRRjueDXGt%2FvlYtWaibsnK9xBEHeN2yZ%2BcMR5Wnx18%2FHOt8H1SHKfWRW4olNWlnTAsZt5eAtuRxHFmr3GyGtceqDuJCqgUC93lOYxAz%2FGI0Mt1icSAdMM6fNpGZTRxMNs%2FVzAPEuXu20%2FF57%2BbOj7H7dZyCiZVbmMrq5ZKUGNks%2BjMCI6W1SgKOKtcyVRhNAkjddwBchEBiQ%2FXpwg0yhe3KDUEca2iLBpN7TsycsKmUP8%2BhZtDJtokrwBCgzV5309P3iab0KnYXDlylLdjxquv3D2gDSlUirnGB3o0cQ6xgA%2BSnCJRXwOjUkKdsSz3dz97PntUP4c9kBhsmA91c4k4zh1JK%2BVYtexYX5MV4pdUuOjuDH%2FPDrEtexGqb7qjaGop%2FtF5dKVWWJ9Z%2Fo749BpI3QOLDJnS1SKF6972BSdx6cABY%3D&aaToken=%7B%22uniqueValidationId%22%3A%2270719ecc-2348-4124-b3fc-5b5269db573b%22%7D';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        $result = curl_exec($ch);
        //echo htmlspecialchars($result);die;
        
        $response['status'] = 404;
        $response['data'] = '';
        
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
            $response['data'] = '';
        }

        if (strpos(htmlspecialchars($result), "mobile number +91".$uname) !== false || strpos(htmlspecialchars($result), "Verification required") !== false) {
            $response['status'] = 200;
            $response['data'] = 'Found';
        }
        if (strpos(htmlspecialchars($result), "Keep me signed in") !== false) {
            $response['status'] = 200;
            $response['data'] = 'Found';
        }        
        curl_close($ch);
        return $response;
    }

    function error()
    {
        return $this->responseError;
    }

}