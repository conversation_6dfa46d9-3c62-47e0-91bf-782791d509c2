<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>ncode extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->library('curl');
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('pincode', 'Pincode', 'trim|required|xss_clean|min_length[6]|max_length[8]');
            if ($this->form_validation->run() === true) {
                $pincode = $this->input->post("pincode");
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $CCASApiUrl = $this->config->item('CCASApiUrl','env');
                $cURLConnection = curl_init($CCASApiUrl.'GetPincodeData?Pincode='.$pincode);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $pincodeDetails = json_decode($result);
                $response = $pincodeDetails->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('pincode/listView', $this->viewDataBag);
    }

    

}