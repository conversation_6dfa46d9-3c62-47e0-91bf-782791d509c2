[2025-08-14 01:05:20] Array
(
    [status] => 1
    [message] => User information retrieved successfully
    [data] => Array
        (
            [mobile_number] => 7708705652
            [phonepe_data] => Array
                (
                    [is_registered] => 1
                    [name] => JANANI M
                    [profile_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/element_7708705652_1755113672.png
                            [relative_path] => phonepe_screenshots/element_7708705652_1755113672.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/element_7708705652_1755113672.png
                            [file_exists] => 1
                            [file_size] => 3727
                        )

                    [cropped_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/contact_7708705652_1755113672.png
                            [relative_path] => phonepe_screenshots/contact_7708705652_1755113672.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/contact_7708705652_1755113672.png
                            [file_exists] => 1
                            [file_size] => 3466
                        )

                    [full_screenshot] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/full_7708705652_1755113672.png
                            [relative_path] => phonepe_screenshots/full_7708705652_1755113672.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/full_7708705652_1755113672.png
                            [file_exists] => 1
                            [file_size] => 126645
                        )

                    [execution_time] => 29.71 seconds
                    [success] => 1
                )

            [gpay_data] => Array
                (
                    [is_registered] => 1
                    [name] => 
                    [banking_name] => JANANI M
                    [upi_id] => jennyjenny67814@oksbi
                    [joining_date] => December 2018
                    [profile_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/gpay_screenshots/element_7708705652_1755113714.png
                            [relative_path] => gpay_screenshots/element_7708705652_1755113714.png
                            [public_url] => https://api.eitem.in/storage/gpay_screenshots/element_7708705652_1755113714.png
                            [file_exists] => 1
                            [file_size] => 29429
                        )

                    [full_screenshot] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/gpay_screenshots/full_7708705652_1755113718.png
                            [relative_path] => gpay_screenshots/full_7708705652_1755113718.png
                            [public_url] => https://api.eitem.in/storage/gpay_screenshots/full_7708705652_1755113718.png
                            [file_exists] => 1
                            [file_size] => 134876
                        )

                    [execution_time] => 45.66 seconds
                    [success] => 1
                )

            [phonepe_error] => 
            [gpay_error] => 
            [status] => completed
            [execution_time] => 75.37 seconds
            [timestamp] => 2025-08-13T19:34:04.738094Z
            [success] => 1
            [partial_success] => 
            [complete_failure] => 
        )

    [errors] => 
)

[2025-08-14 01:05:48] Array
(
    [message] => The mobile number field is required.
    [errors] => Array
        (
            [mobile_number] => Array
                (
                    [0] => The mobile number field is required.
                )

        )

)

[2025-08-14 10:05:02] Array
(
    [status] => 1
    [message] => User information retrieved successfully
    [data] => Array
        (
            [mobile_number] => 8318973831
            [phonepe_data] => Array
                (
                    [is_registered] => 1
                    [name] => VISHAL GUPTA
                    [profile_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/element_8318973831_1755146039.png
                            [relative_path] => phonepe_screenshots/element_8318973831_1755146039.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/element_8318973831_1755146039.png
                            [file_exists] => 1
                            [file_size] => 19131
                        )

                    [cropped_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/contact_8318973831_1755146039.png
                            [relative_path] => phonepe_screenshots/contact_8318973831_1755146039.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/contact_8318973831_1755146039.png
                            [file_exists] => 1
                            [file_size] => 17469
                        )

                    [full_screenshot] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/phonepe_screenshots/full_8318973831_1755146039.png
                            [relative_path] => phonepe_screenshots/full_8318973831_1755146039.png
                            [public_url] => https://api.eitem.in/storage/phonepe_screenshots/full_8318973831_1755146039.png
                            [file_exists] => 1
                            [file_size] => 145086
                        )

                    [execution_time] => 37.12 seconds
                    [success] => 1
                )

            [gpay_data] => Array
                (
                    [is_registered] => 1
                    [name] => 
                    [banking_name] => VISHAL GUPTA
                    [upi_id] => megaitsolutionslko@okaxis
                    [joining_date] => January 2018
                    [profile_image] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/gpay_screenshots/element_8318973831_1755146096.png
                            [relative_path] => gpay_screenshots/element_8318973831_1755146096.png
                            [public_url] => https://api.eitem.in/storage/gpay_screenshots/element_8318973831_1755146096.png
                            [file_exists] => 1
                            [file_size] => 41925
                        )

                    [full_screenshot] => Array
                        (
                            [full_path] => C:\inetpub\vhosts\eitem.in\api.eitem.in\storage\app/public/gpay_screenshots/full_8318973831_1755146099.png
                            [relative_path] => gpay_screenshots/full_8318973831_1755146099.png
                            [public_url] => https://api.eitem.in/storage/gpay_screenshots/full_8318973831_1755146099.png
                            [file_exists] => 1
                            [file_size] => 154466
                        )

                    [execution_time] => 59.7 seconds
                    [success] => 1
                )

            [phonepe_error] => 
            [gpay_error] => 
            [status] => completed
            [execution_time] => 96.82 seconds
            [timestamp] => 2025-08-14T04:33:25.163408Z
            [success] => 1
            [partial_success] => 
            [complete_failure] => 
        )

    [errors] => 
)

[2025-08-14 10:05:37] Array
(
    [message] => The mobile number field is required.
    [errors] => Array
        (
            [mobile_number] => Array
                (
                    [0] => The mobile number field is required.
                )

        )

)

[2025-08-14 21:14:32] Array
(
    [message] => The mobile number field format is invalid.
    [errors] => Array
        (
            [mobile_number] => Array
                (
                    [0] => The mobile number field format is invalid.
                )

        )

)

[2025-08-14 21:19:50] Array
(
    [status] => 1
    [message] => User information retrieved successfully
    [data] => Array
        (
            [mobile_number] => **********
            [phonepe_data] => Array
                (
                    [is_registered] => 
                    [name] => 
                    [execution_time] => 26.14 seconds
                    [success] => 1
                )

            [gpay_data] => Array
                (
                    [is_registered] => 
                    [name] => 
                    [banking_name] => 
                    [upi_id] => 
                    [joining_date] => 
                    [execution_time] => 54.25 seconds
                    [success] => 1
                )

            [phonepe_error] => 
            [gpay_error] => 
            [status] => completed
            [execution_time] => 80.39 seconds
            [timestamp] => 2025-08-14T15:48:29.669962Z
            [success] => 1
            [partial_success] => 
            [complete_failure] => 
        )

    [errors] => 
)

