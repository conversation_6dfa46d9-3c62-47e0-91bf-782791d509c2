<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class CheckProxy extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
        if (strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime()) || empty($res) ) {
           return redirectTo("me/dashboard");
        }
    }
    function details()
    {
		

$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://rapi.mobikwik.com/recharge/infobip/getconnectiondetails?cn=9461101915',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
  CURLOPT_HTTPHEADER => array(
    'Accept: application/json, text/plain, */*',
    'Accept-Encoding: gzip, deflate, br, zstd',
    'Accept-Language: en-US,en;q=0.9',
    'Connection: keep-alive',
    'Host: rapi.mobikwik.com',
    'Origin: https://www.mobikwik.com',
    'Referer: https://www.mobikwik.com/',
    'sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-site',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'X-MClient: 0',
    'Cookie: __cf_bm=hifnYATmvR68G4KtGHrUaG.rt70rNYQTY_Ee9cRXE6o-1747484162-*******-uB.h1UWMcGXgyvDBiKSC1kzD2ImUQaxloIRhYtA2QKXIpXYjG8rsfqlrD9vb6CIX5LTZeay0wtLx6z2uT5LCguGvwvi.RcoVd2WBa1U7Nry.pkybWazNhPeDTeMmyP1E; _cfuvid=p79QxdR05cH9EDg8hYimBZiJ3YoR2WCdZSlmFUP7Xoo-1747479830418-*******-604800000'
  ),
));

$response = curl_exec($curl);

curl_close($curl);
echo $response; die;

		
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/checkproxy/search.js")];
        $this->viewDataBag->jsView = 'checkproxy/jsView';
	//	echo   $authToken = $this->viewDataBag->userSession->auth_token;die;
        $this->loadView('checkproxy/detailView', $this->viewDataBag);
    }
}
