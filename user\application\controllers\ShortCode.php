<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class ShortCode extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('short_code', 'short code', 'trim|required|xss_clean');
            if ($this->form_validation->run() === true) {
                $short_code = $this->input->post("short_code");
                ///$curlPost = "ShortCode=".$short_code;
				$curlPost = json_encode(["ShortCode"=>$short_code]);
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetShortCode');
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
				$headers[] = 'Content-Type: application/json';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $shortCodeData = json_decode($result);     
                $response = $shortCodeData->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('shortCode/listView', $this->viewDataBag);
    }

}