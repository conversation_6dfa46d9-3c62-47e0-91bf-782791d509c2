<?php

set_time_limit(0);

defined('BASEPATH') OR exit('No direct script access allowed');

class Vehicle extends AjaxSecureController
{

    function __construct()
    {
        parent::__construct();

		$res = accessModule('ip_grabber');

		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }

		$user_id = $this->viewDataBag->userSession->id;

		$users = $this->auth->userlimit($user_id);

		if(!$users || empty($users)){
			return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				return redirectTo("me/dashboard");
			}
		}
    }

    function search(){

		header('Content-Type: application/json');

		$user_id = $this->viewDataBag->userSession->id;

        if ($this->input->method() === 'post'){

            $param = [];
                     
			$this->form_validation->set_rules('vehicle', 'vehicle', 'trim|required');

			$vehicle = $this->input->post("vehicle");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$date = new DateTime("now");

                	$curr_date = $date->format('Y-m-d h:i:s');
					$check_date = $date->format('Y-m-d');

					$this->load->model('vehicleModel');

					$users = $this->auth->userlimit($user_id);

					if(!$users || empty($users)){
						return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
					}else{
						if($users['status']){
							$userlimit = $users['error'];
						}else{
							return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
						}
					}
						
					$truecallerDataCount = $this->vehicleModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(created_at)'=>$check_date]);
					
					if($truecallerDataCount >=$userlimit){
						return $this->exitDangerWithValidation("Daily limit over.Please try tomorrow");
					}
					
					$post = [
						"vehicle"=> $vehicle,
						"id"=>$user_id
					];

					$param = json_encode($post);

					log_message('error', 'vehicle$post'.$param);

					$authToken = $this->viewDataBag->userSession->auth_token;

					$curl = curl_init();

					$url = "https://eitem.in/vehicle/fetch-vehicle.php";					
					
					curl_setopt_array($curl, array(
						CURLOPT_URL => $url,
						CURLOPT_RETURNTRANSFER => true,
						CURLOPT_ENCODING => '',
						CURLOPT_MAXREDIRS => 10,
						CURLOPT_TIMEOUT => 0,
						CURLOPT_FOLLOWLOCATION => true,
						CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
						CURLOPT_CUSTOMREQUEST => 'POST',
						CURLOPT_SSL_VERIFYPEER => false,
						CURLOPT_SSL_VERIFYHOST => false,
					  	CURLOPT_POSTFIELDS =>$param,
					  	CURLOPT_HTTPHEADER => array(
							'Content-Type: application/json',
							'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
					  	),
					));

					$response = curl_exec($curl);

					//dd($response);

					log_message('error', 'vehicle$response'.$response);

					$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

					$responses = json_decode($response);

					curl_close($curl);

					if(isset($responses->response)){
						$dataInsert = $responses->response;
					}else{
						$dataInsert = '';
					}

					if(isset($responses->status)){
						$status = $responses->status;
					}else{
						$status = 'FAIL';
					}

					$inserData = [];

					$inserData = [
						'userId' => $this->viewDataBag->userSession->id,
						'response' => $dataInsert,
						'status' => $status,
						'vehicle' => $vehicle,
						'created_at' => $curr_date
					];

					$this->vehicleModel->attach($inserData);

					if(isset($responses->response) && $responses->status){

						// Array to hold extracted data
						$data = [];

						// Define regex patterns for each field
						$patterns = [
							'Registration Number' => '/Registration Number:\s*([^\n]+)/',
							'Owner Name' => '/Owner Name:\s*([^\n]+)/',
							'Owner No' => '/Owner\'s Mobile No:\s*([^\n]+)/',
							'Father\'s Name' => '/Father\'s Name:\s*([^\n]+)/',
							'Permanent Address' => '/Permanent Address:\s*([^\n]+)/',
							'Current Address' => '/Current Address:\s*([^\n]+)/',
							'Owner Serial Number' => '/Owner Serial Number:\s*([^\n]+)/',
							'Date Of Registration' => '/Date Of Registration:\s*([^\n]+)/',
							'State' => '/State:\s*([^\n]+)/'
						];

						$patterns1 = [
							'Manufacturer Model' => '/Manufacturer Model:\s*([^\n]+)/',
							'Manufacturer' => '/Manufacturer:\s*([^\n]+)/',
							'Manufacturing Year' => '/Manufacturing Year:\s*([^\n]+)/',
							'Vehicle\'s Class' => '/Vehicle\'s Class:\s*([^\n]+)/',
							'Color' => '/Colour:\s*([^\n]+)/',
							'Number Of Cylinder' => '/Number Of Cylinder:\s*([^\n]+)/',
							'Fuel Type' => '/Fuel Type:\s*([^\n]+)/',
							'Seating Capacity' => '/Seating Capacity:\s*([^\n]+)/',
							'Registered Place' => '/Registered Place:\s*([^\n]+)/',
							'Chassis Number' => '/Chasis Number:\s*([^\n]+)/',
							'Engine Number' => '/Engine Number:\s*([^\n]+)/'
						];

						$patterns2 = [
							'Insurance Name' => '/Insurance Name:\s*([^\n]+)/',
							'Insurance Policy Number' => '/Insurance Policy Number:\s*([^\n]+)/',
							'Insurance Validity' => '/Insurance Validity:\s*([^\n]+)/',
							'Financer' => '/Financer:\s*([^\n]+)/',
							'PUC Number' => '/PUC Number:\s*([^\n]+)/',
							'Fitness Upto' => '/Fitness Upto:\s*([^\n]+)/',
							'PUC Valid Upto' => '/PUC Valid Upto:\s*([^\n]+)/'
						];

						$result = "<p></p><h3>Personal Information</h3><p>";						
						$result = $this->getDetails($patterns,$responses->response,$result);
						$result .= "</p>";
						
						
						
						$result_vi = "</p><h3>Vehicle Information</h3><p>";
						$result_vi = $this->getDetails($patterns1,$responses->response,$result_vi);
						$result_vi .= "</p>";
						
						$result_id = "</p><h3>Insurance Details</h3><p>";
						$result_id = $this->getDetails($patterns2,$responses->response,$result_id);
						$result_id .= "</p>";
						
						$data = [ "pi" =>$result,"vinfo" =>$result_vi,"idetails" =>$result_id];
						
						echo $this->exitSuccessWithMultiple($data);
					}else{						
						if(isset($responses->response)){
							return $this->exitDangerWithValidation($responses->response);
						}else{
							return $this->exitDangerWithValidation("Some issue occured. Please contact admin");
						}						
					}
					
                    
                } catch (Exception $e) {
					return $this->exitDangerWithValidation($e->getMessage());
                }
            }else{
				return $this->exitDangerWithValidation($this->form_validation->errorArray());
			}
    	}else{
			return $this->exitDangerWithValidation("Unauthorized Access!");
		}
	}
	
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
	
}