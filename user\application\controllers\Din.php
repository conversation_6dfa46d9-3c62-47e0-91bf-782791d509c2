<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Din extends SecureController
{

    function __construct()
    {
        parent::__construct();
    }

    function search(){
        
		$dinData = [];

        if ($this->input->method() === 'post'){
            
            $this->form_validation->set_rules('din', 'din', 'trim|required');
            $dinNumbers = $this->input->post("din");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$curlpost = [
						"Din"=> $dinNumbers
					];
					
                   	$this->load->library('curl');

					$authToken = $this->viewDataBag->userSession->auth_token;
					$cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetDinPanDataAPI');

					curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, json_encode($curlpost));
					curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);

					$headers = array();
					$headers[] = 'Authorization: Bearer '.$authToken;
					$headers[] = 'Cache-Control: no-cache';
					$headers[] = 'Content-Type: application/json';
					curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);

					$result = curl_exec($cURLConnection);

					if (curl_errno($cURLConnection)) {
						echo 'Error:' . curl_error($cURLConnection);
					}

					curl_close($cURLConnection);

					$response = json_decode($result);

                    if (!empty($response->data)):
						$dinData = json_decode($response->data,true);
                        $this->alert->success("Successfully Done");
                    else:
                        $this->alert->danger('Not Found');
                    endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'din/jsView';
		$this->viewDataBag->osintData = $dinData;
        $this->viewDataBag->userData = [];
        $this->loadView('din/searchView', $this->viewDataBag);
    }

}