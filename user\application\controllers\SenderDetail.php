<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class SenderDetail extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('short_code', 'Short Code', 'trim|required|xss_clean|min_length[9]|max_length[9]');
            if ($this->form_validation->run() === true) {
                $short_code = $this->input->post("short_code");
                $curlPost = [];
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/SmsSenderIdDetails?SortCode='.$short_code);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $senderDetails = json_decode($result);
                $response = $senderDetails ? $senderDetails->Data : [];
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('senderDetail/listView', $this->viewDataBag);
    }

}