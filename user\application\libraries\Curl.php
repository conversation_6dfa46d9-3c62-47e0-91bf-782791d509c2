<?php

Class Curl {

    protected $ci;
    protected $curlHandler = NULL;
    protected $options = [];

    function __construct() {
        $this->ci = & get_instance();
    }

    protected function _setHttpHeader() {
        $this->options[CURLOPT_HEADER] = FALSE;
        $this->options[CURLOPT_HTTPHEADER] = [
            "content-type: multipart/form-data;",
            "Accept: application/json",
            /*"Authorization: Bearer a1w01--R6u5UqFN-wVYwt3GCvUwl9Fung3oOjz8l9lnXGWXT6vBuuFCUx5qd9qgb"*/
                /* "X-Api-Key:" . $this->API_Key,
                  "X-Auth-Token:" . $this->Auth_Token */
        ];
    }

    protected function _setHttpMethod($httpMethod) {
        $this->options[CURLOPT_CUSTOMREQUEST] = strtoupper($httpMethod);
    }

    protected function _setURL($url) {
        $this->options[CURLOPT_URL] = $url;
    }

    protected function _setDefaultOptions() {
        $this->options[CURLOPT_NOBODY] = FALSE;
        //$this->options[CURLOPT_FOLLOWLOCATION] = TRUE;
        $this->options[CURLOPT_HTTP_VERSION] = CURL_HTTP_VERSION_1_1;
        $this->options[CURLOPT_MAXREDIRS] = 10;
        $this->options[CURLOPT_TIMEOUT] = 60;
        $this->options[CURLOPT_RETURNTRANSFER] = TRUE;
//        $this->options[CURLOPT_SSL_VERIFYPEER] = 0;
//        $this->options[CURLINFO_HEADER_OUT] = TRUE;
    }

    protected function _setOptions($options) {
        $this->_setDefaultOptions();
        if (isArray($options)) {
            $this->options = array_merge($this->options, $options);
        }
        curl_setopt_array($this->curlHandler, $this->options);
    }

    protected function _init() {
        $this->curlHandler = curl_init();
    }

    function getTrueCallerResponse($urlWithParams, $options = []) {
        $this->_init();
        $this->_setURL($urlWithParams);
        $this->_setHttpMethod('get');
        $this->_setHttpHeader();
        $this->_setOptions($options);
        return $this->execute();
    }

    function CCAS($url, $params = array(), $options = [], $headers = []) {
        $this->_init();
        $this->_setURL($url);
        $this->options[CURLOPT_POST] = TRUE;
        $this->options[CURLOPT_POSTFIELDS] = $params;
        $this->options[CURLOPT_HTTPHEADER] = $headers;

        $this->_setHttpMethod('post');
        //$this->_setHttpHeader();
        $this->_setOptions($options);

        return $this->execute();
    }

    function get($url, $params = [], $options = []) {
        $url = is_array($params) ? $url . "?" . http_build_query($params, NULL, '&') : $url;
        $this->_init();
        $this->_setURL($url);
        $this->_setHttpMethod('get');
        $this->_setHttpHeader();
        $this->_setOptions($options);
        return $this->execute();
    }

    public function post($url, $params = array(), $options = array()) {
        $this->_init();
        $this->_setURL($url);
        $this->options[CURLOPT_POST] = TRUE;
        $this->options[CURLOPT_POSTFIELDS] = $params;

        $this->_setHttpMethod('post');
        $this->_setHttpHeader();
        $this->_setOptions($options);

        return $this->execute();
    }

    // End a session and return the results
    protected function execute() {
        $response = curl_exec($this->curlHandler);
        // Request failed
        if ($response === FALSE) {
            $errno = curl_errno($this->curlHandler);
            $error = curl_error($this->curlHandler);
            $info = curl_getinfo($this->curlHandler);
            $ch = $this->curlHandler;
            curl_close($this->curlHandler);
            $this->finish();
            return ["status" => (bool) FALSE,
                "error" => (string) "Error " . $errno . " " . $error,
                "debug_info" => $info,
                "param" => $ch];
        }
        // Request successful
        else {
            curl_close($this->curlHandler);
            $this->finish();
            return json_decode($response);
        }
    }

    protected function finish() {
        $this->options = [];
        $this->curlHandler = NULL;
    }

}
