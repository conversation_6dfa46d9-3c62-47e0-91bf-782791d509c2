<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Controller extends CI_Controller {

    public $viewDataBag;
    public $localDataBag;

    function __construct() {
        parent::__construct();
        $this->viewDataBag = new stdClass();
        $this->localDataBag = new stdClass();
        $this->form_validation->set_error_delimiters("<div class='text-danger error-message'>", "</div>");
    }

}
