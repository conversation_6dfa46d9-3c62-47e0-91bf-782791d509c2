<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class InSecureController extends MY_Controller
{

    function __construct()
    {
        parent::__construct();
        $cnf = &load_class('Config', 'core');
		
		$currentroute = $this->uri->segment(1);

        if(in_array(ipaddress(), user_ipaddress()) && $currentroute!='truecaller') {
            return redirectTo('truecaller/search');
        }

        $this->viewDataBag->jsView = '';
        $this->viewDataBag->scriptLink = '';

        $this->viewDataBag->pageTitle = $cnf->config['env']['pageTitle'];
        $this->viewDataBag->projectName = $cnf->config['env']['projectName'];
        if ($this->auth->has() === TRUE) {
            $this->viewDataBag->userSession = $this->auth->user();
            $this->load->model('userModel');
            $this->viewDataBag->user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], 'id,sessionId,expiryDate,isAccountType,registrationType,registrationRequest,activationDate');

        } else {
            $this->viewDataBag->userSession = (object)["id" => 0];
        }
    }

    /**
     * loadView
     *
     *  Load Content View with default views
     *
     * @param view $contenView
     * @param array $viewData pass data to view
     * @param boolean $optional (load type of view)
     * @return html
     * <AUTHOR> Kr. Bansal  <<EMAIL>>
     *
     */
    function loadView($contentView, $viewData = [], $optional = false)
    {
        if (is_object($viewData)) {
            $data = (array)$viewData;
        }
        $this->load->view('includes/headerTop', $data, $optional);
        $this->load->view('includes/ajaxLoader', $data, $optional);
        if(in_array(ipaddress(), user_ipaddress())) {
            $this->load->view('includes/UserleftSidebar', $data, $optional);
        } else {
            $this->load->view('includes/leftSidebar', $data, $optional);
        }
        $this->load->view('includes/header', $data, $optional);
        $this->load->view($contentView, $data, $optional);
        $this->load->view('includes/footer', $data, $optional);
        $this->load->view('includes/footerBottom', $data, $optional);
    }

    /* #Loading all countries */

    function setCountries()
    {
        /* #Loading of Country Model */
        $this->load->model('countryModel');
        $this->viewDataBag->countries = $this->countryModel->all();
    }

    /* #Loading all State by Country */

    function setStateByCountry($countryId = '')
    {
        /* #Loading of State Model */
        $this->load->model('stateModel');
        if ($this->input->method() === 'post') {
            $this->viewDataBag->states = $this->stateModel->find(['countryId' => myDropdownSelectedValue('countryId')]);
        } elseif ($countryId != '') {
            $this->viewDataBag->states = $this->stateModel->find(['countryId' => $countryId]);
        } else {
//            $this->viewDataBag->states = $this->stateModel->find(['countryId' => '101']);
            $this->viewDataBag->states = [];
        }
    }

    /* #Loading all city by State */

    function setCityByState($stateId = '')
    {
        /* #Loading of City Model */
        $this->load->model('cityModel');
        if ($this->input->method() === 'post') {
            $this->viewDataBag->cities = $this->cityModel->find(['stateId' => myDropdownSelectedValue('stateId')]);
        } elseif ($stateId != '') {
            $this->viewDataBag->cities = $this->cityModel->find(['stateId' => $stateId]);
        } else {
            $this->viewDataBag->cities = false;
        }
    }

}
