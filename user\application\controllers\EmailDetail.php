<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class EmailDetail extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$this->load->model('OsintModel');
    }

    function search(){
		$user_id = $this->viewDataBag->userSession->id;
		$emailData = [];

        if ($this->input->method() === 'post'){
            $param = [];
            //check user credit
            /*if ($userData->osintCredit == 0) {
                $this->alert->danger("Your Search Credit Over.");
                return redirectToItSelf('search/');
            }*/            
                $this->form_validation->set_rules('email', 'email', 'trim|required');
                $email = $this->input->post("email");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$post = [
						"email"=> $email,
						"userId"=>$user_id
					];
					
                   	$this->load->library('curl');
            		$response = $this->curl->post(apiURL("GoogleDetail/search/"), $post );
					//dd($response);
					
                    if (!empty($response->data)):
						$emailData = $response->data;
                        $this->alert->success("Successfully Done");
                    else:
                        $this->alert->danger('Not Found');
                    endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'din/jsView';
		$this->viewDataBag->emailData = $emailData;
        $this->loadView('emailDetail/searchView', $this->viewDataBag);
    }

}