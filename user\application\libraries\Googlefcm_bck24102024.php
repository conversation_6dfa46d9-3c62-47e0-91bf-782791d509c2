<?php

class Googlefcm_bck24102024
{

    private $ci;
    private $apiServerKey;
    private $apiURL;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
        $this->apiServerKey = $this->ci->config->item('GOOGLE_FCM_API_SERVER_KEY', 'env');
        $this->apiURL = $this->ci->config->item('GOOGLE_FCM_API_URL', 'env');
    }

    function send($deviceId, $title, $message, $type, $linkId = null, $link = null, $url = null, $image = null)
    {
        $fields = [];
        $fields['priority'] = 10;
        $fields['data'] = ['title' => $title,
            'body' => $message,
            'type' => $type,
            'linkId' => $linkId,
            'link' => $link,
            'url' => $url,
            'image' => $image,
        ];

        if (isArray($deviceId)) {
            $fields['registration_ids'] = $deviceId;
        } else {
            $fields['to'] = $deviceId;
        }
        //header with content_type api key
        $headers = array(
            'Content-Type:application/json',
            'Authorization:key=' . $this->apiServerKey
        );
        //dd(json_encode($fields));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiURL);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
        $result = curl_exec($ch);
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
        }
        curl_close($ch);
        return $result;
    }
	
	function notify($deviceId, $title, $message, $type, $linkId = null, $link = null, $url = null, $image = null)
    {
        $fcmUrl = 'https://fcm.googleapis.com/fcm/send';
 $token = $deviceId;

     $notification = [
            'title' =>'title',
            'body' => 'body of message.',
            'icon' =>'myIcon', 
            'sound' => 'mySound'
        ];
        $extraNotificationData = ["message" => $notification,"moredata" =>'dd'];

        $fcmNotification = [
            //'registration_ids' => $tokenList, //multple token array
            'to'        => $token, //single token
            'notification' => $notification,
            'data' => $extraNotificationData
        ];

        $headers = [
            'Authorization: key=' . $this->apiServerKey,
            'Content-Type: application/json'
        ];


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$fcmUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
        $result = curl_exec($ch);
        curl_close($ch);
		dd($result);

        echo $result;
    }

    function error()
    {
        return $this->responseError;
    }

}