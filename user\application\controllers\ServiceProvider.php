<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class ServiceProvider extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('mobile_number', 'Mobile Number', 'trim|required|xss_clean|min_length[10]|max_length[10]');
            if ($this->form_validation->run() === true) {
                $mobile_number = $this->input->post("mobile_number");
				$mobile_code = mb_substr($mobile_number, 0, 5);
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetServiceProviderByMobileCode?MobileCode='.$mobile_code);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $serviceProvider = json_decode($result);
                $response = $serviceProvider->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('serviceProvider/listView', $this->viewDataBag);
    }

}