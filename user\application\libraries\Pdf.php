<?php

include_once APPPATH . 'third_party/pdf/autoload.php';

class Pdf {

    private $ci;
    private $mpdf;

    function __construct() {
        $this->ci = &get_instance();
        $this->mpdf = new \Mpdf\Mpdf();
    }

    function createAndDownload($content, $name = '') {
        ob_clean();
        $this->mpdf->WriteHTML(utf8_encode($content));
        return $this->mpdf->Output($name . '.pdf', 'I');
    }
	
	function telegramLink($content, $path, $watermarkText = '') {
        ob_clean();
		
		if (!empty($watermarkText)) {
			$this->mpdf->SetWatermarkText($watermarkText);
			$this->mpdf->showWatermarkText = true;
		}
		
        $this->mpdf->WriteHTML(utf8_encode($content));
        return $this->mpdf->Output($path, \Mpdf\Output\Destination::FILE);
    }

}

?>