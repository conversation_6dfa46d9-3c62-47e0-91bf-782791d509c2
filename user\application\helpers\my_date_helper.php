<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

if (!function_exists('currentDateTime')) {

    function currentDateTime() {
        return date('Y-m-d H:i:s');
    }

}
if (!function_exists('tomorrowDateTime')) {

    function tomorrowDateTime() {
        return date('Y-m-d H:i:s', strtotime('+1 day'));
    }

}
if (!function_exists('yesterdayDateTime')) {

    function yesterdayDateTime() {
        return date('Y-m-d H:i:s', strtotime('-1 day'));
    }

}

if (!function_exists('currentDate')) {

    function currentDate() {
        return date('Y-m-d');
    }

}
if (!function_exists('tomorrowDate')) {

    function tomorrowDate() {
        return date('Y-m-d', strtotime('+1 day'));
    }

}
if (!function_exists('yesterdayDate')) {

    function yesterdayDate() {
        return date('Y-m-d', strtotime('-1 day'));
    }

}

if (!function_exists('currentTime')) {

    function currentTime() {
        return date('H:i:s');
    }

}

if (!function_exists('currentLongDate')) {

    function currentLongDate() {
        return date('l, d-M-Y');
    }

}
if (!function_exists('tomorrowLongDate')) {

    function tomorrowLongDate() {
        return date('l, d-M-Y', strtotime('+1 day'));
    }

}
if (!function_exists('yesterdayLongDate')) {

    function yesterdayLongDate() {
        return date('l, d-M-Y', strtotime('-1 day'));
    }

}

if (!function_exists('longDate')) {

    function longDate($date) {
        return date('l, d-M-Y', strtotime($date));
    }

}
if (!function_exists('longDateTime')) {
    function longDateTime($dateTime) {
        return date('l, d-M-Y, h:i A', strtotime($dateTime));
    }

}

if (!function_exists('onlyDate')) {

    function onlyDate($dateTime) {
        return date('d-m-Y,', strtotime($dateTime));
    }

}

if (!function_exists('halfTime')) {

    function halfTime($time) {
        return date('h:i:s', strtotime($time));
    }

}
if (!function_exists('fullTime')) {

    function fullTime($time) {
        return date('H:i:s', strtotime($time));
    }

}
if (!function_exists('meridiemTime')) {

    function meridiemTime($time) {
        return date('h:i A', strtotime($time));
    }

}

if (!function_exists('currentYear')) {

    function currentYear() {
        return date('Y');
    }

}
if (!function_exists('currentMonth')) {

    function currentMonth() {
        return date('m');
    }

}
if (!function_exists('currentFullMonth')) {

    function currentFullMonth() {
        return date('M');
    }

}
if (!function_exists('currentDay')) {

    function currentDay() {
        return date('d');
    }

}

if (!function_exists('nextYear')) {

    function nextYear() {
        return date('Y', strtotime('+1 year'));
    }

}
if (!function_exists('nextMonth')) {

    function nextMonth() {
        return date('m', strtotime('+1 month'));
    }

}
if (!function_exists('nextFullMonth')) {

    function nextFullMonth() {
        return date('M', strtotime('+1 month'));
    }

}
if (!function_exists('nextDay')) {

    function nextDay() {
        return date('d', strtotime('+1 day'));
    }

}

if (!function_exists('previousYear')) {

    function previousYear() {
        return date('Y', strtotime('-1 year'));
    }

}
if (!function_exists('previousMonth')) {

    function previousMonth() {
        return date('m', strtotime('-1 month'));
    }

}
if (!function_exists('previousFullMonth')) {

    function previousFullMonth() {
        return date('M', strtotime('-1 month'));
    }

}
if (!function_exists('previousDay')) {

    function previousDay() {
        return date('d', strtotime('-1 day'));
    }

}

if (!function_exists('dbDate')) {

    function dbDate($date) {
        return date('Y-m-d', strtotime($date));
    }

}
if (!function_exists('dbDateTime')) {

    function dbDateTime($dateTime) {
        return date('Y-m-d H:i:s', strtotime($dateTime));
    }

}
if (!function_exists('convertDateTo')) {

    function convertDateTo($format, $date) {
        return date($format, strtotime($date));
    }

}

if (!function_exists('dateByMonth')) {

    function dateByMonth($month) {
        return date("Y-m-d", strtotime($month . " month"));
    }

}
if (!function_exists('dateByDay')) {

    function dateByDay($day) {
        return date("Y-m-d", strtotime($day . " day"));
    }

}
if (!function_exists('dateByYear')) {

    function dateByYear($year) {
        return date("Y-m-d", strtotime($year . " day"));
    }

}
if (!function_exists('expireDate')) {

    function expireDate($date) {
        return date("d", strtotime(dbDate($date)) - strtotime(currentDate()));
    }

}
if (!function_exists('remainingExpireDays')) {

    function remainingExpireDays($date) {
        $datetime1 = new DateTime($date);
        $datetime2 = new DateTime(currentDateTime());
        $interval = $datetime1->diff($datetime2);
        return $remainingDayMonthTime = $interval->format('%m Month - %d Days - %H:%i:%s');
        // $remainingTimeInMinute = $interval->format('%H:%i:%s');
//        $remainingDays = strtotime($date) - strtotime(currentDateTime());
//        return $days = floor($remainingDays / (60 * 60 * 24));
    }

}
