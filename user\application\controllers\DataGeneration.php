<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class DataGeneration extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$this->load->model('OsintModel');
		$this->load->helper('download');
    }

    function createExcel(){
		$this->load->model('ExcelCategoryModel');
		$categories = $this->ExcelCategoryModel->all();

        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('category', 'Category', 'trim|required');
			
            if ($this->form_validation->run() === true) {
                $category_id = $this->input->post("category");
                
                try {
                    $this->load->model('ExcelDataModel');
                    $excelData = $this->ExcelDataModel->findOne(['category_id'=>$category_id]);
                    $filePath = $this->config->item('adminURL') .'uploads/data-creation-excel/'.$excelData->file;
					
					// Check if the file exists
					//if (file_exists($filePath)) {
						// Set headers
						header('Content-Description: File Transfer');
						header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
						header('Content-Disposition: attachment; filename="'.basename($filePath).'"');
						header('Expires: 0');
						header('Cache-Control: must-revalidate');
						header('Pragma: public');
						header('Content-Length: ' . filesize($filePath));

						// Clear output buffer
						ob_clean();
						flush();

						// Read the file and send it to the output
						readfile($filePath);
						exit;
					/*} else {
						// If the file does not exist, show a 404 error
						show_404();
					}*/
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
        $this->viewDataBag->jsView = 'dataGeneration/jsView';
		$this->viewDataBag->categories = $categories;
        $this->loadView('dataGeneration/createView', $this->viewDataBag);
    }

}