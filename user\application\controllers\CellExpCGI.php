<?php

defined('BASEPATH') OR exit('No direct script access allowed');
class CellExpCGI extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->library('curl');
        $this->load->model("userModel");
        $this->load->model('cellIdExcelModel');
    }

    public function all($offset = 0){
		return redirectToItSelf('search/');
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('cgi', 'CGI', 'trim|required|xss_clean|min_length[8]|max_length[25]');
            if ($this->form_validation->run() === true) {
                $cgi = $this->input->post("cgi");
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $CCASApiUrl = $this->config->item('CCASApiUrl','env').'GetCellExpByCGI?CGI='.$cgi;
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                $params = [];
                $options = [];
                $CGIDetails = $this->curl->CCAS($CCASApiUrl, $params, $options, $headers);
                $response = isset($CGIDetails->Data) ? $CGIDetails->Data : '';
            }
			//print_r($response);die;
        }
        $this->viewDataBag->response = $response;
        $this->loadView('CellExpCGI/listView', $this->viewDataBag);
    }
	
	function search(){
        
         $this->load->model("userModel"); 
		$this->load->model('cellIdExcelModel');
         $user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate");
         $truecallerSearchCredit = $user->truecallerSearchCredit;
         $truecallerDailySearch = $user->truecallerDailySearch;
         $response = "";$ids = [];
         $exportMobiles = []; $mobile = [];$commonUrl = 0;
         if ($this->input->method() === 'post'){
             $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');
             if ($this->form_validation->run() === true) {
                $mobiles = $this->input->post("mobile");
				$commonUrl = $this->input->post("common_url");
				$mobiles = preg_replace('/\s+/', ' ', $mobiles);
                $mobile = (explode(" ", $mobiles ));

                $date = new DateTime("now");
                $curr_date = $date->format('Y-m-d ');
                $mobileCount = count($mobile);                
                
                if (count($mobiles) <= 100) {                     
					$response = json_decode($this->callapi($mobile));
					
					if(isset($response->Message)){
						return redirectToItSelf('search/');
					}

					$responses = $response->data;
					//print_r($responses);die;
					$name='';
					if($commonUrl == '') $commonUrl = 0;
					if(isset($responses)){
						foreach ($responses as $key => $res) {
							if($commonUrl == 0){
								$name = '';
							}
							//$name = '';
							$urldetails = $this->cellIdExcelModel->insertCellData($res,$name,$commonUrl,$this->viewDataBag->userSession->id);
							$name = $urldetails[0];

							if($name == "ERROR"){
								$res->name = '<a href="https://www.google.com/maps/search/?api=1&query='.$res->latitude.','.$res->longitude.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
							}else{
								if($commonUrl == 0){
									$res->name = '<a href="https://msg.ccas.in/user/map/view/'.$urldetails[1].'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
								}else{
									$res->name = '<a href="https://msg.ccas.in/user/map/viewsingle/'.$name.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
								}

							}
							$ids[] = isset($urldetails[2]) ? $urldetails[2] : '';
						}
					}
					
				}else{
					$this->alert->danger("You Can Search Only 100 Number At Time");
					return redirectToItSelf('search/');
				}
             }
         }
        $this->viewDataBag->common_url = $commonUrl;
		$this->viewDataBag->exportMobiles = $mobile;
		$this->viewDataBag->exportCGI = $ids;
        $this->viewDataBag->userDetail = $user;
        $this->viewDataBag->response = isset($responses) ? $responses : '';
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/CellExpCGI/search.js")];
        $this->viewDataBag->jsView = 'CellExpCGI/jsView';
        $this->loadView('CellExpCGI/searchView', $this->viewDataBag);
    }
	
	function callapi($mobile){
		
		$param = array(
			"CGI" => $mobile
		);
		
		$param = json_encode($param, JSON_PRETTY_PRINT);
		
		//dd($param);
		//dd($this->config->item('CCASApiUrl','env').'GetCellExpByCGI');
		
		log_message('error', 'dd$param'.$param);
		$authToken = $this->viewDataBag->userSession->auth_token;
		//log_message('error', '$authToken'.$authToken);
		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => $this->config->item('CCASApiUrl','env').'GetCellExpByCGI',
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		  CURLOPT_POSTFIELDS =>$param,
		  CURLOPT_HTTPHEADER => array(
			'Content-Type: application/json',
			'Authorization: Bearer '.$authToken
		  ),
		));

		$response = curl_exec($curl);
		
		if (curl_errno($curl)) {
			$error_msg = curl_error($curl);
		}
		
		//log_message('error', '$response'.$response);
		curl_close($curl);
		//echo "<pre>";print_r($response);die;
		return $response;        
    }  
	
	function bulksearch(){

        $this->load->model("userModel"); 
        $this->load->model('truecallerModel');
        $this->load->model('cellIdExcelModel');
        $user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "cellIdDailySearch");
        $date = new DateTime("now");
        $curr_date = $date->format('Y-m-d ');
		$commonUrl = 0;
        $whereExcel = 'userId ='. $this->viewDataBag->userSession->id;
        $truecallerExcel = $this->cellIdExcelModel->find($whereExcel,'*', ['orderBy' => ['id', 'desc']]);
		
		$whereExcel = 'userId ='. $this->viewDataBag->userSession->id;
        //$truecallerDataCount = $this->cellIdExcelModel->sum(['userId' => $this->viewDataBag->userSession->id,'DATE(created_date)'=>$curr_date]);
		$truecallerDataCount = $this->cellIdExcelModel->select_sum(['userId' => $this->viewDataBag->userSession->id,'DATE(created_date)'=>$curr_date]);

		$truecallerDailySearch = $user->cellIdDailySearch;
		$response = "";
        $exportMobiles = [];

        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('mobile_excel', 'mobile excel', 'required');

            if ($this->form_validation->run() === true) {
               
				//check user daily limit is over
                if ($truecallerDataCount >= $truecallerDailySearch ) {
                    $this->alert->danger("Your Search Credit Over.");
                    return redirectToItSelf('bulksearch/');
                }
				
                //If one file is processing
                $excelProcessing = $this->cellIdExcelModel->count(
					['userId' => $this->viewDataBag->userSession->id,'status'=>'processing']
				);

                if ($excelProcessing>0) {
                    $this->alert->danger("You can upload one excel at a time.");
                    return redirectToItSelf('bulksearch/');
                }
				try{
					$path = 'uploads/';
					$result = [];
					$file = uploadFile('mobile_excel',$path,1);
					if(!isset($file['filename'])){
						 $this->alert->danger($file);
						return redirectToItSelf('bulksearch/');
					}
					//$mobiles = $file['data'];
					$filename = $file['filename'];    
				} catch (Exception $e) {
					$this->alert->danger("Some issue occured. Please try later or contact admin");
					return redirectToItSelf('bulksearch/');
				}
                //Insert into truecaller excel
               /* $inserData = [];
                $inserData = [
                    'userId' => $this->viewDataBag->userSession->id,
                    'filename' => $filename
                ];

                if($this->cellIdExcelModel->attach($inserData)) {
                   // $msg = 'Imported';
                   // $this->alert->success($msg);
                   // return redirectToItSelf('bulksearch/');
                }*/
				try{					
					$query = $this->excelDownload($filename,$truecallerDailySearch);
				}catch (Exception $e) {
					$this->alert->danger("Some error occured. Please try later or contact admin");
					return redirectToItSelf('bulksearch/');
				}
				if($query == 'ERROR'){
					$this->alert->danger("Please verify excel file again or download sample excel to check format");
					return redirectToItSelf('bulksearch/');
				}elseif($query == 'BLANK'){
					$this->alert->danger("Please verify excel file again , no data found");
					return redirectToItSelf('bulksearch/');
				}
            }
        }
        
        //print_r($response);die;
        $this->viewDataBag->response = $truecallerExcel;
		$this->viewDataBag->common_url = $commonUrl;
        $this->viewDataBag->truecallerSearch = $user;
        $this->viewDataBag->exportMobiles = $exportMobiles;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/truecaller/search.js")];
        $this->viewDataBag->jsView = 'CellExpCGI/jsView';
        $this->loadView('CellExpCGI/bulkSearchView', $this->viewDataBag);
    }

    function searchByLbs()
    {
        $user = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate");        
            
        $response = "";
        $ids = [];
		$commonUrl = 0;
		$mobile = [];

        if ($this->input->method() === 'post')
        {

            $this->form_validation->set_rules('template', 'Template', 'trim|required|xss_clean');

            if ($this->form_validation->run() === true) {

                $template = $this->input->post("template");
                $cellIds = extractNumberAfterKeyword($template, ['cgi', 'cellid','cell id']);				
                $commonUrl = $this->input->post("common_url");
                
                if (count($cellIds) <= 100) {                     

                    $response = json_decode($this->callapi($cellIds));

                    //echo "<pre>";print_r($response);die;

                    if(isset($response->Message)){
                        return redirectToItSelf('searchbylbs/');
                    }
                    
                    $responses = $response->data;
                    //print_r($responses);die;

                    $name='';

                    if($commonUrl == '') $commonUrl = 0;

                    if(isset($responses)){
                        foreach ($responses as $key => $res) {
                            if($commonUrl == 0){
                                $name = '';
                            }
                            //$name = '';
                            $urldetails = $this->cellIdExcelModel->insertCellData($res,$name,$commonUrl,$this->viewDataBag->userSession->id);
                            $name = $urldetails[0];

                            if($name == "ERROR"){
                                $res->name = '<a href="https://www.google.com/maps/search/?api=1&query='.$res->latitude.','.$res->longitude.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
                            }else{
                                if($commonUrl == 0){
                                    $res->name = '<a href="https://msg.ccas.in/user/map/view/'.$urldetails[1].'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
                                }else{
                                    $res->name = '<a href="https://msg.ccas.in/user/map/viewsingle/'.$name.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
                                }

                            }
                            $ids[] = isset($urldetails[2]) ? $urldetails[2] : '';
                        }
                    }
                    
                }else{
                    $this->alert->danger("You Can Search Only 100 Number At Time");
                    return redirectToItSelf('searchbylbs/');
                }
            }
        }

        $this->viewDataBag->common_url = $commonUrl;
        $this->viewDataBag->exportMobiles = $mobile;
        $this->viewDataBag->exportCGI = $ids;
        $this->viewDataBag->userDetail = $user;
        $this->viewDataBag->response = isset($responses) ? $responses : '';

        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/CellExpCGI/search.js")];
        $this->viewDataBag->jsView = 'CellExpCGI/jsView';
        $this->loadView('CellExpCGI/searchByLbsView', $this->viewDataBag);
    }

	function triangularization()
    {
        $user = $this->userModel->findOne(
			['id' => $this->viewDataBag->userSession->id], 
			"truecallerSearchCredit,truecallerDailySearch,truecallerExpiryDate"
		);
            
        $response = "";
        $ids = [];
		$commonUrl = 1;
		$mobile = [];

        if ($this->input->method() === 'post')
        {
			$templates = $this->input->post("templates");
			
			if (is_array($templates)) {
				foreach ($templates as $key => $template) {
					$this->form_validation->set_rules(
						"templates[$key]", 
						"CGI " . ($key + 1), 
						'required'
					);
				}
			}
			
            //$this->form_validation->set_rules('template', 'Template', 'trim|required|xss_clean');

            if ($this->form_validation->run() === true) {

				$lbsColors = ['orange','green','purple','yellow','pink'];

                //Iterate over each template
                foreach ($templates as $key => $template) {

                    $matchcellIds = extractNumberAfterKeyword($template, ['cgi', 'cellid','cell id']);
					$cellIds[] = $matchcellIds[0];					
                    $matchLbsLatitude = extractNumberAfterKeyword($template, ['Lat']);
                    $matchLbsLongitude = extractNumberAfterKeyword($template, ['Long']);
                    
                    // Use preg_match_all to find only A, O, E, and C
                    preg_match('/\b([AOEC])\b/', $template, $match);
					
                    $radiusTypeArr = $match[1];
                    
                    $lbsRadius = 500;
                    
                    if(isset($radiusTypeArr)) {
                        if($radiusTypeArr === "A") {
                            $lbsRadius = 30;
                        } else if($radiusTypeArr === "O") {
                            $lbsRadius = 150;
                        } else if($radiusTypeArr === "E") {
                            $lbsRadius = 570;
                        } else {
                            $lbsRadius = 1000;
                        }
                    }

                    $lbsRadiusArr[] = $lbsRadius;
                    $lbsLatitudeArr[] = $matchLbsLatitude[0];
                    $lbsLongitudeArr[] = $matchLbsLongitude[0];

                }
                
                if (count($cellIds) <= 100) {
					
                    $response = json_decode($this->callapi($cellIds));					

                    if(isset($response->Message)){
                        return redirectToItSelf('triangularization/');
                    }
                    
                    $responses = $response->data;
					$name='';

                    if(isset($responses)){
                        foreach ($responses as $key => $res) {
							$res->lbsLatitude = isset($lbsLatitudeArr[$key]) ? $lbsLatitudeArr[$key] : null;
							$res->lbsLongitude = isset($lbsLongitudeArr[$key]) ? $lbsLongitudeArr[$key] : null;
							$res->lbsRadius = $lbsRadiusArr[$key];
							$res->lbsColor = $lbsColors[$key];
                            
                            $urldetails = $this->cellIdExcelModel->insertCellData($res,$name,$commonUrl,$this->viewDataBag->userSession->id);
                            $name = $urldetails[0];

                            if($name == "ERROR"){
                                $res->name = '<a href="https://www.google.com/maps/search/?api=1&query='.$res->latitude.','.$res->longitude.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
                            }else{
								$res->name = '<a href="https://msg.ccas.in/user/map/triangularization/'.$name.'" target="_blank" class="btn btn-sm btn-info cutom-search-btn">Show Map</a>';
                            }
                            $ids[] = isset($urldetails[2]) ? $urldetails[2] : '';
                        }
                    }
                    
                }else{
                    $this->alert->danger("You Can Search Only 100 Number At Time");
                    return redirectToItSelf('searchbylbs/');
                }
            }
        }

        $this->viewDataBag->common_url = $commonUrl;
        $this->viewDataBag->exportMobiles = $mobile;
        $this->viewDataBag->exportCGI = $ids;
        $this->viewDataBag->userDetail = $user;
        $this->viewDataBag->response = isset($responses) ? $responses : '';

        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'CellExpCGI/jsView';

        $this->loadView('CellExpCGI/triangularizationView', $this->viewDataBag);
    }
	
	 public function excelDownload($filename,$truecallerDailySearch) {

		 $this->load->model('cellIdExcelModel');
		 $this->load->library('excel');
		 $userId = $this->viewDataBag->userSession->id;
		

		 $FileName = 'uploads/' . $filename;
		try{
			$query = $this->bulkCgiData($FileName,$truecallerDailySearch);
		} catch (Exception $e) {
			$this->alert->danger("Some issue occured on download. Please try later or contact admin");
			return redirectToItSelf('bulksearch/');
		}
		//log_message('error', 'ddquery11'.json_encode($query));
		 if($query == 'ERROR'){
			 return $query;
		 }
		 $inserData = [];
		 $inserData = [
			 'userId' => $this->viewDataBag->userSession->id,
			 'filename' => $filename,
			 'status' => 'Processed',
			 'search_records' => count($query)
		 ];

		 if($this->cellIdExcelModel->attach($inserData)) {
			 // $msg = 'Imported';
			 // $this->alert->success($msg);
			 // return redirectToItSelf('bulksearch/');
		 }
		 if(count($query) < 1){
			 return "BLANK";
		 }
		 try{
		 	$this->excel->customCellIdDownload($query, "CGI-Data");
		 }catch (Exception $e) {
			$this->alert->danger("Some issue occured on download.Please try later or contact admin");
			return redirectToItSelf('bulksearch/');
		 }
		
        return '';
    }
	
	public function bulkCgiData($inputFileName,$truecallerDailySearch){
		try {
			require_once APPPATH . "/third_party/Excel2PHP/PHPExcel.php";
			$inputFileType = PHPExcel_IOFactory::identify($inputFileName);
			$objReader = PHPExcel_IOFactory::createReader($inputFileType);
			$objPHPExcel = $objReader->load($inputFileName);
			$excelReader = PHPExcel_IOFactory::load($inputFileName);
			$sheet 			= $excelReader->getActiveSheet();
			$highestColumn = $sheet->getHighestColumn();
			$highestRow = $sheet->getHighestRow();
			$CGIColumn = null;
			for ($col = 'A'; $col <= $highestColumn; $col++) {
				$headerValue = $sheet->getCell($col . '1')->getValue(); // Reading header row (row 1)
				$name[] = $headerValue;
				if (strtoupper($headerValue) === 'CGI') { // Case-insensitive check for "CGI"
					$CGIColumn = $col;
					break;
				}else if (strtoupper($headerValue) === 'CELL ID' || strtoupper($headerValue) === 'CELLID') { 
					$CGIColumn = $col;
				}
			}

			//print_r([$name,$CGIColumn]);die;
			$excelData = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);

			$flag = true;
			$searchRecords = 0;

			$date = new DateTime("now");
			$curr_date = $date->format('Y-m-d ');
			$returnData = array();
			$i=0;$j=0;$cgiId=array();

			foreach ($excelData as $value) {
				if($flag){
					$flag = false;
					continue;
				}
				
				if(isset($CGIColumn)){
					$values = str_replace("-","",$value[$CGIColumn]);
					if(!empty(trim($values))){
						$cellId[] = trim($values);
						$cgiid[$j] = $cellId;
						$i++;
					}					
				}else{
					//print_r($value);die;
					return "ERROR";
				}
			}    
			//Daily search by user
			$mobileCount = count($i);

			if ($mobileCount > $truecallerDailySearch) {
				$this->alert->danger("You Can Search Only $truecallerDailySearch CellId At Time");
				return redirectToItSelf('bulksearch/');
			}
			if(isset($cgiid) && count($cgiid) > 0){
				unset($values);
				foreach($cgiid as $values){
					$response = json_decode($this->callapi($values));
					//log_message('error', 'dd$$response'.json_encode($response));
					if(isset($response->Message)){
						log_message('error', 'continue');
						continue;
					}else{
						$response = $response->data;
						//var_dump($response);die;
						$name='';
						$commonUrl = $this->input->post("common_url");
						if($commonUrl == '') $commonUrl = 0;

						if (isArray($response)) {
							foreach ($response as $key => $res) {
								if($commonUrl == 0){
									$name = '';
								}
								$urldetails = $this->cellIdExcelModel->insertCellData($res,$name,$commonUrl,$this->viewDataBag->userSession->id);
								$name = $urldetails[0];

								if($name == "ERROR"){
									$res->name = 'https://www.google.com/maps/search/?api=1&query='.$res->latitude.','.$res->longitude;
								}else{
									if($commonUrl == 0){
										$res->name = "https://msg.ccas.in/user/map/view/".$urldetails[1];
									}else{
										$res->name = "https://msg.ccas.in/user/map/viewsingle/".$name;
									}
								}
								$returnData[] =  array(
									(property_exists($res, 'cgi') ? $res->cgi : null),
									(property_exists($res, 'cellIDCode') ? $res->cellIDCode : null),
									(property_exists($res, 'latitude') ? $res->latitude : null),
									(property_exists($res, 'longitude') ? $res->longitude : null),
									(property_exists($res, 'siteAddress') ? $res->siteAddress : null),
									(property_exists($res, 'city') ? $res->city : null),
									(property_exists($res, 'tmRemark') ? $res->tmRemark : null),
									(property_exists($res, 'operator') ? $res->operator : null),
									(property_exists($res, 'circle') ? $res->circle : null),
									(property_exists($res, 'dateUpload') ? $res->dateUpload : null),
									(property_exists($res, 'uniqueID') ? $res->uniqueID : null),
									(property_exists($res, 'mcc') ? $res->mcc : null),
									(property_exists($res, 'mnc') ? $res->mnc : null),
									(property_exists($res, 'lac') ? $res->lac : null),
									(property_exists($res, 'sAzimuth') ? $res->sAzimuth : null),
									(property_exists($res, 'eAzimuth') ? $res->eAzimuth : null),
									(property_exists($res, 'cellIDCodeWLAC') ? $res->cellIDCodeWLAC : null),
									(property_exists($res, 'type1') ? $res->type1 : null),
									(property_exists($res, 'azimooth') ? $res->azimooth : null),
									$res->name
								); 
							}
							return $returnData;
						}
						
					}  
				}
			}else{
				log_message('error', 'blank');
				return [];
			}
		} catch (Exception $e) {
			$this->alert->danger("Some issue occured. Please try later or contact admin");
			return redirectToItSelf('bulksearch/');
		}
	}
	
	 public function export() {
		  $this->load->model('cellIdExcelModel');
        $userId = $this->viewDataBag->userSession->id;
        $where = 'FIND_IN_SET('.$userId.', userId)';
        $limit = '';
        
        if($this->input->post('exportCGI')){
            $exportMobiles = $this->input->post('exportCGI');
            $exportMobiles = implode(",",$exportMobiles);
            if($where != '') {
                $where .= ' AND ( id IN ('.$exportMobiles.') )' ;
            } else {
                $where = '( id IN ('.$exportMobiles.') )' ;
            }
        }
        $this->load->library('excel');
		$this->viewDataBag->queries = $this->cellIdExcelModel->export('all',$where,$limit);
		//echo "<pre>";print_r($this->viewDataBag->queries);die;
        $this->excel->customCellIdDownload($this->viewDataBag->queries, "CellCgi-Data");
        return 'success';
    }

}