<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Registration extends InSecureController {

    function __construct() {
        parent::__construct();
        $this->load->model('userModel');
    }

    function index() {
        if ($this->input->method() === 'post') {
			$email = $this->input->post('email');
			$exist = $this->userModel->count( 'mobile = "'.$this->input->post('mobile'). '" OR email = "'. $email.'"' );
			
			if($exist>0) {
				$this->alert->danger('Invalid Email or Mobile');
				return redirectToCurrent();
			}
			
            $this->form_validation->set_rules('firstName', 'first name', 'trim|required|xss_clean|_isHumanName');
            $this->form_validation->set_rules('lastName', 'last name', 'trim|required|xss_clean|_isHumanName');
            $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean|numeric|min_length[10]|max_length[10]|regex_match[/^[0-9]{10}$/]|is_unique[tb_user.mobile]');
            $this->form_validation->set_rules('email', 'email', 'trim|valid_email|max_length[100]|xss_clean|is_unique[tb_user.email]');
            $this->form_validation->set_rules('password', 'password', 'required|min_length[6]|max_length[50]|xss_clean');
            $this->form_validation->set_rules('confirmPassword', 'confirm password', 'required|matches[password]|xss_clean');
            if ($this->form_validation->run() == True) {
				
               /* name section */
                $name = ["firstName" => ucwords($this->input->post('firstName')),
                    "lastName" => ucwords($this->input->post('lastName')),];
                $name["displayName"] = $name["firstName"] . " " . $name["lastName"];
                $name["urlName"] = strtolower($name["firstName"]) . "-" . strtolower($name["lastName"]);

                $userDetails = ['name' => json_encode($name),
                    'mobile' => $this->input->post('mobile'),
                    'profileImage' => NULL,
                    'email' => $this->input->post('email'),
                    'password' => $this->input->makePasswordHash('confirmPassword'),
                    'createdOn' => currentDateTime(),
                    'redirectTo' => 'dashboard',
                    'isAccountType' => 'fresh',
                    'registrationType' => NULL,
                    'registrationDevice' => 'web',
                    'isStatus' => 'active',
                    'verified' => json_encode(["mobile" => TRUE, "email" => FALSE]),
                ];
				$userId = $this->userModel->attach($userDetails);
                if ($userId):
					$this->load->model('UserModuleModel');
					$modules = ['truecaller','ip_grabber','os_int'];
					foreach ($modules as $module) {
						$moduleData['userId'] = $userId;
						$moduleData['module'] = $module;
						$this->UserModuleModel->attach($moduleData);
					}
				
					//Add new service
					$headers = array();
            		$url = 'https://ccasociety.com/course/api/v1/services/add';
            		$fields = [
            			'name' => $name["displayName"],
            			'email' => $this->input->post('email'),
            			'mobile' => $this->input->post('mobile'),
            			'countryId' => 101,
            			'stateId' => 4121,
            			'cityId' => 48315,
            			'service' => 'ip_grabber',
            			'message' => 'Service created from ip',
            		];
            		$ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
                    $result = curl_exec($ch);
                    if ($result === FALSE) {
                        //$this->responseError = curl_error($ch);
                    }
                    curl_close($ch);
				
                    $this->alert->success('Your account has been created successfully');
                else:
                    $this->alert->danger(getErrorMessage());
                endif;
                return redirectToCurrent();
            }
        }
        $this->load->view('authentication/registration/formView', $this->viewDataBag);
    }
}
