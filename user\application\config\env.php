<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/*
  |--------------------------------------------------------------------------
  | Project Name
  |--------------------------------------------------------------------------
  |
  | Typically this will be your project name, it will used to set cookies name and session name.
  |
 */

$config['pageTitle'] = 'IP Tracking';
$config['projectName'] = 'IpTracking';
$config['CCASApiUrl'] = 'https://mobileapi.lisw.in/api/Tac/';
/*
  |--------------------------------------------------------------------------
  | Email smtp Details
  |--------------------------------------------------------------------------
  |
  | This determines which smtp is used for email.
  |
 */
$config['smtp_host'] = "";
$config['smtp_port'] = "";
$config['smtp_user'] = "";
$config['smtp_pass'] = "";
$config['smtp_crypto'] = ""; // tls or ssl defalut null
$config['from_email'] = "";
$config['from_name'] = "";
$config['reply_email'] = "";
$config['reply_name'] = "";

/*
  |--------------------------------------------------------------------------
  | SMS Details
  |--------------------------------------------------------------------------
  |
  | This determines which sms authentication is used for SMS.
  |
 */
$config['SMS_user'] = "CCASOC";
$config['SMS_password'] = "1915";
$config['SMS_sender'] = "CCASOC";
$config['SMS_priority'] = "ndnd";
$config['SMS_stype'] = "normal";
$config['SMS_url'] = "http://trans.avengingsecurity.com/api/sendmsg.php";
/*
  |--------------------------------------------------------------------------
  | Authentication
  |--------------------------------------------------------------------------
  |
  | This determines which table is used for user or admin login time.
  |
 */
// frontend user login table name
$config['loginTable'] = 'tb_user';
// frontend user login table primary key
$config['loginTablePrimaryKey'] = 'id';
/*
  |--------------------------------------------------------------------------
  | Social Api
  |--------------------------------------------------------------------------
  |
  | This determines which which social plugin is used.
  |
 */
/*
  |--------------------------------------------------------------------------
  | bitly Api
  |--------------------------------------------------------------------------
 */
$config['BitlyApiUrl']="http://api.bit.ly/v3/shorten";
$config['BitlyLogin']="iptracking";
$config['BitlyApiKey']="R_91e562aab394443c9ddd4ec924075f87";
/*
  |--------------------------------------------------------------------------
  | Facebook  Api
  |--------------------------------------------------------------------------
  |
 */

/*
  |--------------------------------------------------------------------------
  | Google FCM Notification Api
  |--------------------------------------------------------------------------
  |
 */
$config['GOOGLE_FCM_API_SERVER_KEY'] = "AIzaSyARgcPNgFgH5KcyWb14PnrCxWlPGvs3AYg";
$config['GOOGLE_FCM_API_URL'] = "https://fcm.googleapis.com/fcm/send";
/*
  |--------------------------------------------------------------------------
  | Google Plus  Api
  |--------------------------------------------------------------------------
  |
 */


/*
  |--------------------------------------------------------------------------
  | Twitter  Api
  |--------------------------------------------------------------------------
  |
 */

/*
  |--------------------------------------------------------------------------
  | Instagram  Api
  |--------------------------------------------------------------------------
  |
 */


/*
  |--------------------------------------------------------------------------
  | Payment Gateway Api
  |--------------------------------------------------------------------------
  |
  | This determines which payment gateway is used.
  |
 */

/*
  |--------------------------------------------------------------------------
  | paytm  Api
  |--------------------------------------------------------------------------
  |
 */

/*$config['PAYTM_ENVIRONMENT'] = "TEST";
$config['PAYTM_MERCHANT_KEY'] = "5fXrMHO%OErrp!zX";
$config['PAYTM_MERCHANT_MID'] = "etdupy55105434174524";
$config['PAYTM_MERCHANT_WEBSITE'] = "WEBSTAGING";
$config['PAYTM_STATUS_QUERY_NEW_URL'] = "https://securegw-stage.paytm.in/merchant-status/getTxnStatus";
$config['PAYTM_TXN_URL'] = "https://securegw-stage.paytm.in/theia/processTransaction";*/


$config['PAYTM_ENVIRONMENT'] = "PROD";
$config['PAYTM_MERCHANT_KEY'] = "@&9pUK3AZJtK@IJr";
$config['PAYTM_MERCHANT_MID'] = "jUbbFW71300658037896";
$config['PAYTM_MERCHANT_WEBSITE'] = "WEBPROD";
$config['PAYTM_STATUS_QUERY_NEW_URL'] = "https://securegw.paytm.in/merchant-status/getTxnStatus";
$config['PAYTM_TXN_URL'] = "https://securegw.paytm.in/theia/processTransaction";

$config['RAZORPAY_KEY_ID'] = "***********************";
$config['RAZORPAY_KEY_SECRET'] = "TmdeOXQyFoLZJUHN4CoRczom";

/*$config['RAZORPAY_KEY_ID'] = "rzp_test_x5DuRPUVnocU8b";
$config['RAZORPAY_KEY_SECRET'] = "K7aEyZgbW9VKx71uHXLmvRQ5";*/

/*
  |--------------------------------------------------------------------------
  | paypal  Api
  |--------------------------------------------------------------------------
  |
 */

/*
  |--------------------------------------------------------------------------
  | payu money  Api
  |--------------------------------------------------------------------------
  |
 */

/*
  |--------------------------------------------------------------------------
  | Instamojo  Api
  |--------------------------------------------------------------------------
  |
 */
