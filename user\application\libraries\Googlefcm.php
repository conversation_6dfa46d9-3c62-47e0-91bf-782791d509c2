<?php
//require_once APPPATH . 'vendor/autoload.php'; // Load composer dependencies
//use Google\Client;
//use GuzzleHttp\Client as GuzzleClient;

class Googlefcm {
	
	private $client;
    private $accessToken;

    private $ci;
    private $apiServerKey;
    private $apiURL;
    private $responseError;
	private $clientSecret;
	private $clientId;
	private $code;
	private $redirectUri;
	private $refreshToken;

    function __construct() {
        $this->ci = & get_instance();
        $this->ci->config->load('env', true);
        $this->apiServerKey = $this->ci->config->item('GOOGLE_FCM_API_SERVER_KEY', 'env');
        $this->apiURL = $this->ci->config->item('GOOGLE_FCM_API_URL', 'env');
		//$serviceAccountPath = APPPATH . 'config/service-account.json';
		$this->clientId='*************-isc577h9rgtt1ah8b01dith6fihi5ith.apps.googleusercontent.com';
		$this->clientSecret='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';
		$this->code='4/0AVG7fiQpkG-kuIy0Ze8YTMJSJwoU3-v62aLnjnTU2G2K8re3oGi_Yvux9TyKVQuzdONGIw'; //ipaddress project
		$this->refreshToken='1//0gR1qF84IO2mOCgYIARAAGBASNwF-L9IrYiCnG3gIkBYa05tCYJDwmGTXlPhucq7VboLLFCiLykLbhNtOH_biZUYQf1dxbuyfMIE';
		$this->redirectUri='https://msg.ccas.in/user/firebase/token';

    }
	function send($deviceId, $title, $message, $type, $linkId = null, $link = null, $url = null, $image = null){
		$message = [
			'message' => [
				'token' => $deviceId,
				'notification' => [
					'title' => $title,
					'body' => $message,
					'image' => $image
				],
				'data' => [
					'link' => $link
				]
			]
		];
		return $response = $this->sendFCMMessage($message);

	}
	
	public function sendFCMMessage($message) {
		$serviceAccountPath = APPPATH . 'config/ipaddress-service-account.json';
		
		$firebaseProjectId = 'ipaddress-5e1d5'; // Your Firebase project ID
		$this->apiURL = "https://fcm.googleapis.com/v1/projects/{$firebaseProjectId}/messages:send";
		
		log_message('error', 'sendFCMMessage'.json_encode($message));
		
		/*$this->ci->load->model('GoogletokenModel');
		$data  = $this->ci->GoogletokenModel->get_all_data();
		if (isset($data) && count($data) > 0) {
			foreach ($data as $row) {
				$accessToken = $row->token;  
			}			
		} else {
			$accessToken=$this->get_access_token();
		}*/
		
		$accessToken=$this->get_access_token();
		
		$headers = [
			"Authorization: Bearer $accessToken",
			"Content-Type: application/json"
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->apiURL);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		$result = curl_exec($ch);
		curl_close($ch);
		log_message('error', 'sendFCMMessage'.json_encode($result));
		return json_decode($result, true);
	}
	
	function get_access_token_old() {
		/*$url = 'https://oauth2.googleapis.com/token';
		$data = [
			'code' => $this->code,
			'client_id' => $this->clientId,
			'client_secret' => $this->clientSecret,
			'redirect_uri' => $this->redirectUri,
		    'grant_type' => 'authorization_code',
		];*/
		$url = 'https://accounts.google.com/o/oauth2/token';
		$data = [
			'refresh_token' => $this->refreshToken,
			'client_id' => $this->clientId,
			'client_secret' => $this->clientSecret,
			'redirect_uri' => $this->redirectUri,
		    'grant_type' => 'refresh_token',
		];
		//print_r($data);echo "<BR>";echo "<BR>";
		//echo http_build_query($data);

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

		$result = curl_exec($ch);
		curl_close($ch);
		//echo "<pre>";print_r($result);echo "<BR>";
		$result = json_decode($result, true);

		if(array_key_exists('access_token', $result)) {
			
			$this->ci->load->model('GoogletokenModel');
			$insert = [
				'token'=>$result['access_token'],
				'refresh_token'=>$this->refreshToken,
				'created_at'=> currentDateTime(),
			];
			//$this->ci->GoogletokenModel->attach($insert);
			$this->ci->GoogletokenModel->update_token($result['access_token'],$this->refreshToken);
			return $result['access_token'];
		} else {
			return null;
		}
	}
	
	function get_access_token() {
		$serviceAccountPath = APPPATH . 'config/ipaddress-service-account.json';
		$serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

		$jwtHeader = base64_encode(json_encode([
			'alg' => 'RS256',
			'typ' => 'JWT'
		]));

		$now = time();
		$jwtClaim = base64_encode(json_encode([
			'iss' => $serviceAccount['client_email'],
			'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
			'aud' => 'https://oauth2.googleapis.com/token',
			'iat' => $now,
			'exp' => $now + 3600
		]));

		$signature = '';
		openssl_sign("$jwtHeader.$jwtClaim", $signature, $serviceAccount['private_key'], 'SHA256');
		$jwtSignature = base64_encode($signature);

		$jwt = "$jwtHeader.$jwtClaim.$jwtSignature";

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
			'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
			'assertion' => $jwt
		]));

		$response = curl_exec($ch);
		curl_close($ch);

		$data = json_decode($response, true);
		return $data['access_token'] ? $data['access_token'] : null;
	}
    
	function sendold($deviceId, $title, $message,$type,$linkId = null,$link = null) {
        $fields = [];
        $fields['priority'] =10;
        $fields['data'] = ['title' => $title,
            'body' => $message,
            'type' =>$type,
            'linkId' =>$linkId,
            'link' =>$link];

        if (isArray($deviceId)) {
            $fields['registration_ids'] = $deviceId;
        } else {
            $fields['to'] = $deviceId;
        }
		log_message('error', 'dd$param'.json_encode($fields));
		log_message('error', 'apiurl'.$this->apiURL);
        //header with content_type api key
        $headers = array(
            'Content-Type:application/json',
            'Authorization:key=' . $this->apiServerKey
        );
		log_message('error', '$headers'.json_encode($headers));

        //dd(json_encode($fields));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiURL);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
        $result = curl_exec($ch);
		log_message('error', '$result'.json_encode($result));
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
        }
        curl_close($ch);
        return $result;
    }

    function error() {
        return $this->responseError;
    }

}