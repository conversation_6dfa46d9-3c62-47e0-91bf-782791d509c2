<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Cookie {

    private $ci;
    private $expire;

    function __construct() {
        $this->ci = & get_instance();
        $this->ci->load->library('encryption');
        $this->expire = time() + (365 * 86400);
    }

    private function encrypt($cookieDataArrayOrObject) {
        return urlencode($this->ci->encryption->encrypt(json_encode($cookieDataArrayOrObject)));
    }

    private function decrypt($cookieData) {
        return json_decode($this->ci->encryption->decrypt(urldecode($cookieData)));
    }

    function push($cookieName, $cookieDataArrayOrObject, $expire = "") {
        if ($expire == "") {
            $expire = $this->expire;
        }
        setcookie($cookieName, $this->encrypt($cookieDataArrayOrObject), $expire, '/');
        return true;
    }

    function get($cookieName) {
        return $this->has($cookieName) ? $this->decrypt($_COOKIE[$cookieName]) : false;
    }

    function has($cookieName) {
        return (isset($_COOKIE[$cookieName]) && $_COOKIE[$cookieName] != '') ? true : false;
    }

    function put($cookieName, $cookieDataArrayOrObject, $expire = "") {
        $this->push($cookieName, $cookieDataArrayOrObject, $expire);
        return true;
    }

    function flush($cookieName) {
        if (isset($_COOKIE[$cookieName])) {
            unset($_COOKIE[$cookieName]);
        }
        setcookie($cookieName, '', $this->expire, '/');
        return true;
    }

}
