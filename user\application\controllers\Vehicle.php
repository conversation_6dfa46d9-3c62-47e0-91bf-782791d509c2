<?php

defined('BASEPATH') OR exit('No direct script access allowed');

set_time_limit(0);

class Vehicle extends SecureController
{

    function __construct()
    {
        parent::__construct();

		$user_id = $this->viewDataBag->userSession->id;

		$res = accessModule('ip_grabber');

		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);

		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
    }

    function search(){

		$emailData = [];

		$this->viewDataBag->vehicles = $emailData;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'vehicle/jsView';

        $this->loadView('vehicle/searchView', $this->viewDataBag);
    }
	
	function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');

        $to = $this->input->get('to');

        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $user_id = $this->viewDataBag->userSession->id;
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.vehicle like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;

        $this->load->model('vehicleModel');

        $totalRows = $this->vehicleModel->totalCount($where);

        $offset = ($offset > $totalRows) ? 0 : $offset;

        $this->viewDataBag->users = $this->vehicleModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);

        $this->load->library('pagination');

        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;

        $this->pagination->initialize($config);

        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        $this->viewDataBag->jsView = 'vehicle/jsView';
		
        $this->loadView('vehicle/listView', $this->viewDataBag);
    }

	function detail($slug)
    {
        $id = slugId();

        if ($id) {

            $this->load->model('vehicleModel');

            $value = $this->vehicleModel->findOne(['id' => $id], '*');

            if (isObject($value)) {

				$result = $result_vi = $result_id = '';

				if(isset($value->response) && $value->status){
					// Array to hold extracted data
					$data = [];

					// Define regex patterns for each field
					$patterns = [
						'Registration Number' => '/Registration Number:\s*([^\n]+)/',
						'Owner Name' => '/Owner Name:\s*([^\n]+)/',
						'Owner No' => '/Owner\'s Mobile No:\s*([^\n]+)/',
						'Father\'s Name' => '/Father\'s Name:\s*([^\n]+)/',
						'Permanent Address' => '/Permanent Address:\s*([^\n]+)/',
						'Current Address' => '/Current Address:\s*([^\n]+)/',
						'Owner Serial Number' => '/Owner Serial Number:\s*([^\n]+)/',
						'Date Of Registration' => '/Date Of Registration:\s*([^\n]+)/',
						'State' => '/State:\s*([^\n]+)/'
					];
					$patterns1 = [
						'Manufacturer Model' => '/Manufacturer Model:\s*([^\n]+)/',
						'Manufacturer' => '/Manufacturer:\s*([^\n]+)/',
						'Manufacturing Year' => '/Manufacturing Year:\s*([^\n]+)/',
						'Vehicle\'s Class' => '/Vehicle\'s Class:\s*([^\n]+)/',
						'Color' => '/Colour:\s*([^\n]+)/',
						'Number Of Cylinder' => '/Number Of Cylinder:\s*([^\n]+)/',
						'Fuel Type' => '/Fuel Type:\s*([^\n]+)/',
						'Seating Capacity' => '/Seating Capacity:\s*([^\n]+)/',
						'Registered Place' => '/Registered Place:\s*([^\n]+)/',
						'Chassis Number' => '/Chasis Number:\s*([^\n]+)/',
						'Engine Number' => '/Engine Number:\s*([^\n]+)/'
					];
					$patterns2 = [
						'Insurance Name' => '/Insurance Name:\s*([^\n]+)/',
						'Insurance Policy Number' => '/Insurance Policy Number:\s*([^\n]+)/',
						'Insurance Validity' => '/Insurance Validity:\s*([^\n]+)/',
						'Financer' => '/Financer:\s*([^\n]+)/',
						'PUC Number' => '/PUC Number:\s*([^\n]+)/',
						'Fitness Upto' => '/Fitness Upto:\s*([^\n]+)/',
						'PUC Valid Upto' => '/PUC Valid Upto:\s*([^\n]+)/'
					];
					$result = "<p></p><h3>Personal Information</h3><p>";						
					$result = $this->getDetails($patterns,$value->response,$result);
					$result .= "</p>";



					$result_vi = "</p><h3>Vehicle Information</h3><p>";
					$result_vi = $this->getDetails($patterns1,$value->response,$result_vi);
					$result_vi .= "</p>";

					$result_id = "</p><h3>Insurance Details</h3><p>";
					$result_id = $this->getDetails($patterns2,$value->response,$result_id);
					$result_id .= "</p>";
				}else{
					$this->viewDataBag->response = $value->response;
				}
				 
				$this->viewDataBag->pi = $result;
				$this->viewDataBag->vinfo = $result_vi;
				$this->viewDataBag->idetails = $result_id;
				$this->viewDataBag->res_status = $value->status;
				$this->viewDataBag->vehicles = $value->vehicle;
                $this->viewDataBag->jsView = 'vehicle/jsView';
                $this->loadView('vehicle/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }

	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
}