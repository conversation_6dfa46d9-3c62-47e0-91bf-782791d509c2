<?php
set_time_limit(0);
defined('BASEPATH') OR exit('No direct script access allowed');
class Mobile extends AjaxSecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		$user_id = $this->viewDataBag->userSession->id;
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
    }

    function search(){
		header('Content-Type: application/json');
		$user_id = $this->viewDataBag->userSession->id;
		$emailData = [];

        if ($this->input->method() === 'post'){
            $param = [];
                     
			$this->form_validation->set_rules('vehicle', 'vehicle', 'trim|required');
			$mobile = $this->input->post("vehicle");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$date = new DateTime("now");
                	$curr_date = $date->format('Y-m-d h:i:s');
					$check_date = $date->format('Y-m-d');
					$this->load->model('mobileModel');
					$users = $this->auth->userlimit($user_id);
					if(!$users || empty($users)){
						return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
					}else{
						if($users['status']){
							$userlimit = $users['error'];
						}else{
							return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
						}
					}
						
					$truecallerDataCount = $this->mobileModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(created_at)'=>$check_date]);
					
					if($truecallerDataCount >=$userlimit){
						return $this->exitDangerWithValidation("Daily limit over.Please try tomorrow");
					}
					
					$post = [
						"mobile"=> $mobile,
						"id"=>$user_id
					];
					$param = json_encode($post);
					log_message('error', 'mobile$post'.$param);
					$authToken = $this->viewDataBag->userSession->auth_token;
					$curl = curl_init();
					$url = "https://eitem.in/vehicle/fetch-mobile.php";					
					
					curl_setopt_array($curl, array(
						CURLOPT_URL => $url,
						CURLOPT_RETURNTRANSFER => true,
						CURLOPT_ENCODING => '',
						CURLOPT_MAXREDIRS => 10,
						CURLOPT_TIMEOUT => 0,
						CURLOPT_FOLLOWLOCATION => true,
						CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
						CURLOPT_CUSTOMREQUEST => 'POST',
						CURLOPT_SSL_VERIFYPEER => false,
						CURLOPT_SSL_VERIFYHOST => false,
					  	CURLOPT_POSTFIELDS =>$param,
					  	CURLOPT_HTTPHEADER => array(
							'Content-Type: application/json',
							'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
					  	),
					));

					$response = curl_exec($curl);
					log_message('error', 'mobile$response'.$response);
					$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
					$responses = json_decode($response);
					curl_close($curl);
					if(isset($responses->response)){
						$dataInsert = $responses->response;
					}else{
						$dataInsert = '';
					}
					if(isset($responses->status)){
						$status = $responses->status;
					}else{
						$status = 'FAIL';
					}
					$inserData = [];
					$inserData = [
						'userId' => $this->viewDataBag->userSession->id,
						'response' => $dataInsert,
						'status' => $status,
						'mobile' => $mobile,
						'created_at' => $curr_date
					];

					$this->mobileModel->attach($inserData);
					if(isset($responses->response) && $responses->status){
						// Array to hold extracted data
						//$data = [];
						
						$data = json_decode($responses->response, true);
						
						$networkName = isset($data[0]['result']['source_output']['current_service_provider']['network_name']) ? $data[0]['result']['source_output']['current_service_provider']['network_name'] : 'N/A';
						$msisdn = isset($data[0]['result']['source_output']['msisdn_details']['msisdn']) ? $data[0]['result']['source_output']['msisdn_details']['msisdn'] : 'N/A';
						$imsi = isset($data[0]['result']['source_output']['msisdn_details']['imsi']) ? $data[0]['result']['source_output']['msisdn_details']['imsi'] : 'N/A';
						$portingHistory = isset($data[0]['result']['source_output']['mobile_number_details']['porting_history']) ? $data[0]['result']['source_output']['mobile_number_details']['porting_history'] : 'N/A';
						$name = isset($data[0]['result']['source_output']['mobile_number_details']['name']) ? $data[0]['result']['source_output']['mobile_number_details']['name'] : 'N/A';
						$mobile_number_status = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_number_status']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_number_status'] : 'N/A';
						$mobile_connection_type = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type'] : 'N/A';
						$mobile_connection_type = isset($data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type']) ? $data[0]['result']['source_output']['mobile_number_details']['mobile_connection_type'] : 'N/A';
						$alternate_number = isset($data[0]['result']['source_output']['mobile_number_details']['alternate_number']) ? $data[0]['result']['source_output']['mobile_number_details']['alternate_number'] : 'N/A';
						$is_ported = isset($data[0]['result']['source_output']['mobile_number_details']['is_ported']) ? $data[0]['result']['source_output']['mobile_number_details']['is_ported'] : 'N/A';
						$network = isset($data[0]['result']['source_output']['current_service_provider']['network_name']) ? $data[0]['result']['source_output']['current_service_provider']['network_name'] : 'N/A';
						$network_region = isset($data[0]['result']['source_output']['current_service_provider']['network_region']) ? $data[0]['result']['source_output']['current_service_provider']['network_region'] : 'N/A';
						$status_code = isset($data[0]['result']['source_output']['connection_status']['status_code']) ? $data[0]['result']['source_output']['connection_status']['status_code'] : 'N/A';

						// Define regex patterns for each field
						$patterns = [
							'Network Name' => $networkName,
							'Msisdn' => $msisdn,
							'Imsi' => $imsi,
							'Name' => $name,
							'Porting history' => $portingHistory,
							'Mobile Number Status' => $mobile_number_status,
							'Mobile Connection Type' => $mobile_connection_type,
							'Alternate Number' => $alternate_number,
							'Is Ported' => $is_ported,
							'Mobile Connection Type' => $mobile_connection_type,
							'Network Name' => $network,
							'Network Region' => $network_region,
							'Status Code' => $status_code
						];
						
						
						$result = "<p></p><h3>Mobile Information</h3><p>";						
						$result = $this->getDetails($patterns,$responses->response,$result);
						$result .= "</p>";
						$data = [];
						$data = [ "pi" =>$result];
						echo $this->exitSuccessWithMultiple($data);
					}else{
						//echo $this->exitSuccessWithOne(['status'=>$responses->status,'data' => $responses->response]);
						//echo $this->exitSuccessWithMultiple("No Data Found".$responseCode);
						if(isset($responses->response)){
							return $this->exitDangerWithValidation($responses->response);
						}else{
							return $this->exitDangerWithValidation("Some issue occured. Please contact admin");
						}						
					}
					
                    
                } catch (Exception $e) {
					return $this->exitDangerWithValidation($e->getMessage());
                }
            }else{
				 return $this->exitDangerWithValidation($this->form_validation->errorArray());
			}
    	}else{
			 return $this->exitDangerWithValidation("Unauthorized Access!");
		}
	}
	
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			$result .= "<b>".$key."</b>: ".$pattern."<br>";
		}
		return $result;
	}
}