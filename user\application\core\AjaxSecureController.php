
<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class AjaxSecureController extends AjaxInSecureController {

    public function __construct() {
        parent::__construct();
        if ($this->auth->has() === false) {
            echo json_encode([
                'status' => (bool) false,
                'code' => (string) "ULO",
                'error' => 'You are now logged out.',
                'csrf' => (array) csrfArray(),
            ]);
            exit;
        }
        return;
    }

}
