<?php

defined('BASEPATH') OR exit('No direct script access allowed');
set_time_limit(0);
class BankAcc extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$user_id = $this->viewDataBag->userSession->id;
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
		

    }

    function search(){
		$emailData = [];
		$this->viewDataBag->banks = $emailData;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'bank/jsView';
        $this->loadView('bank/searchView', $this->viewDataBag);
    }
	function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $user_id = $this->viewDataBag->userSession->id;
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.bank like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;
        $this->load->model('bankModel');
        $totalRows = $this->bankModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $this->viewDataBag->users = $this->bankModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        $this->viewDataBag->jsView = 'bank/jsView';
        $this->loadView('bank/listView', $this->viewDataBag);
    }
	function detail($slug)
    {
        $id = slugId();
        if ($id) {
            $this->load->model('bankModel');
            $value = $this->bankModel->findOne(['id' => $id], '*');
            if (isObject($value)) {
				$result = $result_vi = $result_id = '';
				if(isset($value->response) && $value->status){
					// Array to hold extracted data
					$data = [];

					// Define regex patterns for each field
					$patterns = [
							'Name' => '/Name:\s*([^\n]+)/',
							'Bank A/c No' => '/Bank A\/c No:\s*([^\n]+)/',
							'IFSC Code' => '/IFSC Code:\s*([^\n]+)/',
							'A/c exists' => '/A\/c exists:\s*([^\n]+)/'
						];
					$result = "<p></p><h3>Bank Account Information</h3><p>";						
					$result = $this->getDetails($patterns,$value->response,$result);
					$result .= "</p>";
				}else{
					$this->viewDataBag->response = $value->response;
				}
				 
				$this->viewDataBag->pi = $result;
				$this->viewDataBag->res_status = $value->status;
				$this->viewDataBag->banks = $value->bank;
                $this->viewDataBag->jsView = 'vehicle/jsView';
                $this->loadView('bank/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
}