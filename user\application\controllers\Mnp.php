<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Mnp extends SecureController
{

    function __construct()
    {
        parent::__construct();
    }

    function search(){
		$user_id = $this->viewDataBag->userSession->id;
		$mnpData = [];

        if ($this->input->method() === 'post'){
            $param = [];
			
            $this->form_validation->set_rules('queryString', 'Email', 'trim|required|xss_clean');
			
            if ($this->form_validation->run() === true) {
				
				$queryString = $this->input->post('queryString');
                
                try {	
					$param['authToken'] = $this->viewDataBag->userSession->auth_token;
					$param['userId'] = $this->viewDataBag->userSession->id;
					$param['queryString'] = $queryString;
					
                   	$this->load->library('curl');
            		$response = $this->curl->post(apiURL("mnp/search/"), $param);
					
                    if (!empty($response->data)):
						$mnpData = $response->data;
                        $this->alert->success("Successfully Done");
                    else:
                        $this->alert->danger('Not Found');
                    endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'mnp/jsView';
		$this->viewDataBag->mnpData = $mnpData;
        $this->viewDataBag->userData = [];
        $this->loadView('mnp/searchView', $this->viewDataBag);
    }

}