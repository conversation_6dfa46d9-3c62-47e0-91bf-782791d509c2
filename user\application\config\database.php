<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/* #Start database selection */
$active_group = 'default';
if ($_SERVER['SERVER_NAME'] != 'localhost') {
    $active_group = 'live';
}
/* #End database selection */

$query_builder = TRUE;

/* #Start Local database configration */

$db['default'] = array(
    'dsn' => '',
    'hostname' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'dbiptracking',
    'dbdriver' => 'mysqli',
    'dbprefix' => '',
    'pconnect' => FALSE,
    'db_debug' => (ENVIRONMENT !== 'production'),
    'cache_on' => FALSE,
    'cachedir' => '',
    'char_set' => 'utf8',
    'dbcollat' => 'utf8_general_ci',
    'swap_pre' => '',
    'encrypt' => FALSE,
    'compress' => FALSE,
    'stricton' => FALSE,
    'failover' => array(),
    'save_queries' => TRUE
);
/* #End Local database configration */

/* #Start Live database configration */
$db['live'] = array(
    'dsn' => '',
    'hostname' => 'localhost',
    'username' => 'ccas_tracking',
    'password' => 'Tracking@123',
    'database' => 'ccas_db_tracking',
    'dbdriver' => 'mysqli',
    'dbprefix' => '',
    'pconnect' => FALSE,
    'db_debug' => (ENVIRONMENT !== 'production'),
    'cache_on' => FALSE,
    'cachedir' => '',
    'char_set' => 'utf8mb4',
    'dbcollat' => 'utf8mb4_general_ci',
    'swap_pre' => '',
    'encrypt' => FALSE,
    'compress' => FALSE,
    'stricton' => FALSE,
    'failover' => array(),
    'save_queries' => TRUE
);
/* #End Live database configration*/
