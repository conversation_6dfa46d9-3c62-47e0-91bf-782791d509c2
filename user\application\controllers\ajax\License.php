<?php
set_time_limit(0);
defined('BASEPATH') OR exit('No direct script access allowed');
class License extends AjaxSecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		$user_id = $this->viewDataBag->userSession->id;
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
    }

    function search(){
		header('Content-Type: application/json');
		$user_id = $this->viewDataBag->userSession->id;
		$emailData = [];

        if ($this->input->method() === 'post'){
            $param = [];
                     
			$this->form_validation->set_rules('vehicle', 'vehicle', 'trim|required');
			$this->form_validation->set_rules('dob', 'dob', 'trim|required');
			$vehicle = $this->input->post("vehicle");
			$dob = $this->input->post("dob");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$date = new DateTime("now");
                	$curr_date = $date->format('Y-m-d h:i:s');
					$check_date = $date->format('Y-m-d');
					$this->load->model('licenseModel');
					$users = $this->auth->userlimit($user_id);
					if(!$users || empty($users)){
						return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
					}else{
						if($users['status']){
							$userlimit = $users['error'];
						}else{
							return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
						}
					}
						
					$truecallerDataCount = $this->licenseModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(created_at)'=>$check_date]);
					
					if($truecallerDataCount >=$userlimit){
						return $this->exitDangerWithValidation("Daily limit over.Please try tomorrow");
					}
					
					$post = [
						"license"=> $vehicle,
						"dob"=> $dob,
						"id"=>$user_id
					];
					$param = json_encode($post);
					log_message('error', 'license$post'.$param);
					$authToken = $this->viewDataBag->userSession->auth_token;
					$curl = curl_init();
					$url = "https://eitem.in/vehicle/fetch-license.php";					
					
					curl_setopt_array($curl, array(
						CURLOPT_URL => $url,
						CURLOPT_RETURNTRANSFER => true,
						CURLOPT_ENCODING => '',
						CURLOPT_MAXREDIRS => 10,
						CURLOPT_TIMEOUT => 0,
						CURLOPT_FOLLOWLOCATION => true,
						CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
						CURLOPT_CUSTOMREQUEST => 'POST',
						CURLOPT_SSL_VERIFYPEER => false,
						CURLOPT_SSL_VERIFYHOST => false,
					  	CURLOPT_POSTFIELDS =>$param,
					  	CURLOPT_HTTPHEADER => array(
							'Content-Type: application/json',
							'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
					  	),
					));

					$response = curl_exec($curl);
					log_message('error', 'license$response'.$response);
					$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
					$responses = json_decode($response);
					curl_close($curl);$profile=null;
					if(isset($responses->response)){
						$dataInsert = $responses->response;
						$profile = $responses->profile;
					}else{
						$dataInsert = '';
					}
					if(isset($responses->status)){
						$status = $responses->status;
					}else{
						$status = 'FAIL';
					}
					if(!isset($profile)){
						return $this->exitDangerWithValidation("Data Loading!");
					}
					$inserData = [];
					$inserData = [
						'userId' => $this->viewDataBag->userSession->id,
						'response' => $dataInsert,
						'profile' => $profile,
						'status' => $status,
						'license' => $vehicle,
						'created_at' => $curr_date
					];

					$this->licenseModel->attach($inserData);
					if(isset($responses->response) && $responses->status){
						// Array to hold extracted data
						$data = [];


						// Define regex patterns for each field
						$patterns = [
							'Name' => '/Name:\s*([^\n]+)/',
							'ID Number' => '/ID Number:\s*([^\n]+)/',
							'DL Status' => '/DL Status:\s*([^\n]+)/',
							'D.O.B' => '/D.O.B:\s*([^\n]+)/',
							'RTO' => '/RTO:\s*([^\n]+)/',
							'Date Of Issue' => '/Date Of Issue:\s*([^\n]+)/',
							'Validity From' => '/Validity From:\s*([^\n]+)/',
							'Validity To' => '/Validity To:\s*([^\n]+)/',
							//'Cov Details' => '/Cov Details:\s*([^\n]+)/',
							'State' => '/RTO:\s*([^\n]+)/',
							'Is Minor' => '/Is Minor:\s*([^\n]+)/',
							'Profile Image' => '/Profile Image:\s*([^\n]+)/',							
						];
						
						$result = "<p></p><h3>License Information</h3><p>";						
						$result = $this->getDetails($patterns,$responses->response,$result,$profile);
						$result .= "</p>";
						
						$data = [ "pi" =>$result];
						
						echo $this->exitSuccessWithMultiple($data);
					}else{
						//echo $this->exitSuccessWithOne(['status'=>$responses->status,'data' => $responses->response]);
						//echo $this->exitSuccessWithMultiple("No Data Found".$responseCode);
						if(isset($responses->response)){
							return $this->exitDangerWithValidation($responses->response);
						}else{
							return $this->exitDangerWithValidation("Some issue occured. Please contact admin");
						}						
					}
					
                    
                } catch (Exception $e) {
					return $this->exitDangerWithValidation($e->getMessage());
                }
            }else{
				 return $this->exitDangerWithValidation($this->form_validation->errorArray());
			}
    	}else{
			 return $this->exitDangerWithValidation("Unauthorized Access!");
		}
	}
	
	public function getDetails($patterns,$response,$result,$profile){
		foreach ($patterns as $key => $pattern) {
			if($key =='Profile Image' && !is_null($profile)){
				$profile = "<img src='".$profile."' />";
				$result .= "<b>".$key."</b>: ".$profile."<br>";
			}else{
				if (preg_match($pattern, $response, $matches)) {
					$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
				} else {
					$result .= "<b>".$key."</b>: ".'N/A'."<br>";
				}
			}
			
		}
		return $result;
	}
}