<?php

class Dreameleven {

    private $ci;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
    }

    function accessToken() {

        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://api.dream11.com/refreshToken',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => '{"refreshToken":"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","appId":"D11","appsFlyerChannelName":"Pro","appsFlyerId":"1664813340393-3849170384233344950","device":"androidfull","deviceId":"f834539f783d731b","siteId":1}',
          CURLOPT_HTTPHEADER => array(
            'a1: /MEPRMxPZkvjN4YXH+n4s1U4yCJE4X9ifcyRqOHSmzkNtLF09p35WkrXRlW8Jwz9g7EVK1D5mS3d1JmG/VbkVk3lYGILuUF/rnjxi++aIeXo8xnH804+Q9ZnmA3tC3NuyxIU82koENAkkTiPH74ffA==',
            'accept: application/json',
            'accept-encoding: gzip',
            'app_version: 4.63.0',
            'authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            'cache-control: no-cache',
            'connection: Keep-Alive',
            'content-length: 627',
            'content-type: application/json; charset=UTF-8',
            'device: androidfull',
            'deviceid: f834539f783d731b',
            'ek1: qcvDwhTW0TqR5gb6SmRBLA==',
            'ek2: /MEPRMxPZkvjN4YXH+n4s+f3IMF1L3fMQtB0JiLya/o=',
            'guest-id: ba9a6551-0ddd-4d75-bd93-a7f82f4a78ae',
            'host: api.dream11.com',
            'locale: en-US',
            'siteid: 1',
            'user-agent: Dalvik/2.1.0 (Linux; U; Android 6.0; Samsung Build/MRA58K)',
            'version: 923',
            'x-app-version-name: 4630',
            'x-manufacturer: unknown',
            'x-os-type: Android',
            'x-os-version: 23',
            'x-resolution: Unknown'
          ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $result = json_decode($response);
        return $result->accessToken;
    }

    function phoneBook($mobile) {

        $curl = curl_init();
        
        $authToken = $this->accessToken();//'*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://www.dream11.com/graphql/mutation/androidfull/phonebook-sync-mutation',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{"operationName":"PhonebookSyncMutation","variables":{"phoneNumber":["'.$mobile.'"]},"query":"mutation PhonebookSyncMutation($phoneNumber:[String!]) { phoneBookSyncV1(phoneBook: $phoneNumber) { __typename edges { __typename id isCurrentUser mobileNumber teamName userGuid myConnectionStatus userFullName artwork { __typename src } } } }"}',
          CURLOPT_HTTPHEADER => array(
            'a1: /MEPRMxPZkvjN4YXH+n4s1U4yCJE4X9ifcyRqOHSmzkNtLF09p35WkrXRlW8Jwz9g7EVK1D5mS3d1JmG/VbkVk3lYGILuUF/rnjxi++aIeXo8xnH804+Q9ZnmA3tC3NuyxIU82koENAkkTiPH74ffA==',
            'accept: application/json',
            'accept-encoding: gzip',
            'app_version: 4.63.0',
            'authorization: Bearer '.$authToken,
            'cache-control: no-cache',
            'connection: Keep-Alive',
            'content-type: application/json; charset=utf-8',
            'device: androidfull',
            'deviceid: f834539f783d731b',
            'ek1: qcvDwhTW0TqR5gb6SmRBLA==',
            'ek2: /MEPRMxPZkvjN4YXH+n4s+f3IMF1L3fMQtB0JiLya/o=',
            'guest-id: ba9a6551-0ddd-4d75-bd93-a7f82f4a78ae',
            'host: www.dream11.com',
            'locale: en-US',
            'siteid: 1',
            'user-agent: Dalvik/2.1.0 (Linux; U; Android 6.0; Samsung Build/MRA58K)',
            'version: 923',
            'x-apollo-operation-id: 93d34ed085697da0ccc610b90fd7609708c1cd326054895385773ac38aa010e8',
            'x-apollo-operation-name: PhonebookSyncMutation',
            'x-app-version-name: 4630',
            'x-manufacturer: unknown',
            'x-os-type: Android',
            'x-os-version: 23',
            'x-resolution: Unknown'
          ),
        ));

        $result = curl_exec($curl);
        //$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        $result = json_decode($result);
        $response['status'] = 0;
        $response['data'] = '';
        $response['authToken'] = $authToken;
        $phoneBookSyncV1 = $result->data->phoneBookSyncV1;
        
        if ($result === FALSE || $phoneBookSyncV1=='') {
            $this->responseError = curl_error($curl);
            $response['data'] = curl_error($curl);
        } else {
            $edges = $result->data->phoneBookSyncV1->edges;
            $response['status'] = count($edges)>0 ? 1 : 0;
            $response['data'] = count($edges)>0 ? $edges[0] : '';
        }
        
        curl_close($curl);

        return $response;
    }

    function account($userName) {

        $phoneBook = $this->phoneBook($userName);

        if($phoneBook['status']==0)
            return $phoneBook;

        $userId = $phoneBook['data']->id;
        $authToken = $phoneBook['authToken'];

        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://www.dream11.com/graphql/query/androidfull/profile-query',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{"operationName":"profileQuery","variables":{"userId":'.$userId.',"sites":["cricket","kabaddi","football","nba","baseball","handball","volleyball"],"limit":5,"after":"","isAwardsEnabled":true,"showSkillScore":true},"query":"query profileQuery($userId: Int!, $sites: [String!]!, $limit: Int!, $after: String!, $isAwardsEnabled: Boolean!, $showSkillScore: Boolean!) { user(id: $userId) { __typename id teamName name coverPicColorCode userType coverPic { __typename src } profilePic { __typename src } officialTick { __typename src } userTeamPrivacy { __typename isPrivacyEnabled message } referralCode isMobileVerified mobileNumber loyaltyLevel skillScore { __typename totalScore matchesRemaining } userGuid userLocation relationStatus countOfFollowers { __typename count } countOfFollowing { __typename count } careerStats(showSkillScore: $showSkillScore) { __typename you opp statName statType youDisplayName oppDisplayName } recentPerformance(sites: $sites) { __typename recentMatches(limit: $limit, after: $after) { __typename ...GRecentPerformance } } recentCommonMatches(sites: $sites) { __typename commonMatchesStats { __typename commonMatchesCount commonStats { __typename you opp statName statType youDisplayName oppDisplayName } } commonMatches(limit: $limit, after: $after) { __typename edges { __typename ...GRecentCommonMatch } } } channelInfo { __typename channelUrl customType } Badges: awards(status: UNLOCKED) @include(if: $isAwardsEnabled) { __typename totalAwards AwardEdge: edges { __typename id tag { __typename src } isSeen image { __typename src } awardType labelDisplayName tierType } } } } fragment GRecentPerformance on PaginatedRecentMatches { __typename edges { __typename id tour { __typename id } name startTime userTeamsCount dreamTeamPoints roundResult slug displayName squads { __typename flag { __typename src } shortName } bestUserTeam { __typename id totalPoints } } } fragment GRecentCommonMatch on RecentCommonMatch { __typename id name startTime slug displayName squads { __typename flag { __typename src } shortName } roundResult youTeamSummary { __typename displayName teamsCount bestUserTeam { __typename id totalPoints } } oppTeamSummary { __typename displayName teamsCount bestUserTeam { __typename id totalPoints } } tour { __typename id } }"}',
          CURLOPT_HTTPHEADER => array(
            'a1: /MEPRMxPZkvjN4YXH+n4s1U4yCJE4X9ifcyRqOHSmzkNtLF09p35WkrXRlW8Jwz9g7EVK1D5mS3d1JmG/VbkVk3lYGILuUF/rnjxi++aIeXo8xnH804+Q9ZnmA3tC3NuyxIU82koENAkkTiPH74ffA==',
            'accept: application/json',
            'accept-encoding: gzip',
            'app_version: 4.63.0',
            'authorization: Bearer '.$authToken,
            'cache-control: no-cache',
            'connection: Keep-Alive',
            'content-type: application/json; charset=utf-8',
            'device: androidfull',
            'deviceid: f834539f783d731b',
            'ek1: qcvDwhTW0TqR5gb6SmRBLA==',
            'ek2: /MEPRMxPZkvjN4YXH+n4s+f3IMF1L3fMQtB0JiLya/o=',
            'guest-id: ba9a6551-0ddd-4d75-bd93-a7f82f4a78ae',
            'host: www.dream11.com',
            'locale: en-US',
            'siteid: 1',
            'user-agent: Dalvik/2.1.0 (Linux; U; Android 6.0; Samsung Build/MRA58K)',
            'version: 923',
            'x-apollo-cache-do-not-store: false',
            'x-apollo-cache-fetch-strategy: NETWORK_ONLY',
            'x-apollo-cache-key: 26de819a74003fc76aacdcba0d43d2c4',
            'x-apollo-expire-after-read: false',
            'x-apollo-expire-timeout: 0',
            'x-apollo-operation-id: 27f753997729b6fd70cd081ac2ba9b15c0f230f70bfca8415c979a3490a97641',
            'x-apollo-operation-name: profileQuery',
            'x-apollo-prefetch: false',
            'x-app-version-name: 4630',
            'x-manufacturer: unknown',
            'x-os-type: Android',
            'x-os-version: 23',
            'x-resolution: Unknown'
          ),
        ));

        $result = curl_exec($curl);
        $result = json_decode($result);

        curl_close($curl);
        
        //$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        $response['status'] = 0;
        $response['data'] = '';
        $user = $result->data->user;
        
        if ($result === FALSE) {
            $this->responseError = curl_error($ch);
        } else {
            $response['status'] = 1;
            $response['data'] = $user;
        }
        return $response;
    }

    function error()
    {
        return $this->responseError;
    }

}