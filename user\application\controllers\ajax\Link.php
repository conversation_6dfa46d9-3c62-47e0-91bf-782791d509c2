<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Link extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('linkModel');
    }

    function takeAction() 
    {
        $status = $this->input->post('status');
        $execute = false;
        if ($status == "delete") {
            $this->linkModel->detach(['id' => $this->input->post('id')]);
            $this->load->model('locationModel');
            $this->locationModel->detach(['linkId' => $this->input->post('id')]);
            $execute = true;
        } else {
            $this->linkModel->modify(['isStatus' => $status], ['id' => $this->input->post('id')]);
            $execute = true;
        }

        if ($execute) {
            unset($where);
            unset($updateData);
            $this->exitSuccess();
        } else {
            unset($where);
            unset($updateData);
            $this->exitDanger();
        }
    }
	/*function getOgmage($url){
		$urlEncrypt = "https://msg.ccas.in/user/public/getOGimage.php?url=".$url;
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $urlEncrypt);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HEADER, false);
		$data = curl_exec($curl);
		curl_close($curl);
		return $data;
	}*/
	
    function add()
    {
        $this->form_validation->set_rules('victimNameOrFir', 'victim name or fir', 'trim|required|min_length[2]|max_length[40]|xss_clean');
        $this->form_validation->set_rules('redirectUrl', 'redirectUrl', 'trim|xss_clean');
        $this->form_validation->set_rules('image', 'imagePath', 'xss_clean');
        $this->form_validation->set_rules('description', 'description', 'trim|xss_clean');
		$this->form_validation->set_rules('location', 'location', 'trim|xss_clean');
		$this->form_validation->set_rules('isCamera', 'isCamera', 'trim|xss_clean');
		$this->form_validation->set_rules('isPaste', 'isPaste', 'trim|xss_clean');
        if ($this->form_validation->run() === true):
            $featureImage = '';
            if (!$this->input->post('image')) {
                /* #start file uploading */
                if (($_FILES['image']['name'] && $_FILES['image']['type']) != NULL):
                    $featureImage = NULL;
                    $curlPostData = [
                        'image' => makeCurlFile('image'),
                        "folder" => "link/images",
                        "resize" => json_encode([[200, 200]]),
                    ];
                    $this->load->library('curl');
                    $uploadedData = (array)$this->curl->post(IMAGE_BASE_URL . 'v1/image/uploadAndResize', $curlPostData);
                    if ($uploadedData['status'] === true):
                        $featureImage = $uploadedData['filePath'][0];
                    else:
                        $this->alert->danger($uploadedData['error']);
                        return redirectToCurrent();
                    endif;
                /* #end file uploading */
                else:
                    $featureImage = NULL;
                endif;
            } else {
                $featureImage = NULL;
            }

            /* #end file uploading */
            /* input grab section */
		
            $userData = [
                'userId' => $this->viewDataBag->userSession->id,
                'victimNameOrFir' => $this->input->post("victimNameOrFir"),
                'description' => $this->input->post("description"),
				'location' => $this->input->post("location"),
				'isCamera' => $this->input->post("isCamera"),
				'isPaste' => $this->input->post("isPaste"),
                'createdOn' => currentDateTime(),
                'isStatus' => 'active',
            ];

            if ($featureImage != NULL) {
                $userData['imagePath'] = $featureImage;
                $userData["title"] = $this->input->post("description");
                $userData["favicon"] = IMAGE_BASE_URL . $featureImage;
            }
            if ($this->input->post('redirectUrl') != NULL) {
                $userData["redirectUrl"] = $this->input->post('redirectUrl');
                $userData["isRedirect"] = 'yes';
                $header = getHeader($userData["redirectUrl"]);
			
                $userData["title"] = //htmlspecialchars($header['title']);
                $userData["description"] = htmlspecialchars($header['description']);
                $userData["favicon"] = $header['favicon'];
				
				/*$url = base64_encode($userData["redirectUrl"]);
				$imageOg = $this->getOgmage($url);
				$userData['og_image'] = $imageOg;*/
				

            } else {
                $userData["isRedirect"] = 'no';
            }
            $this->load->model('linkModel');
		
            if ($insertId = $this->linkModel->attach($userData)) {
                $linkData = $this->linkModel->findOne(['id' => $insertId]);
                return $this->exitSuccessWithOne(['linkData' => $linkData]);
            } else {
                return $this->exitDanger();
            }
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
	
    function updateShortLink()
    {
        $this->form_validation->set_rules('linkId', 'link id', 'trim|required');
        $this->form_validation->set_rules('userId', 'user id', 'trim|required');
        if ($this->form_validation->run() === true):
            $post = $this->input->post();
            $post['link'] = TRACK_URL . "goo/" . base64_encode($post['linkId']);
            $this->load->library('curl');
            $response = $this->curl->post(apiURL("link/updateShortLink/"), $post);
            $response = $this->exitOnly($response);
            return $this->exitSuccessWithOne(['response' => $response]);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
    function updateCoustomezeLink()
    {
        $this->form_validation->set_rules('link', 'link', 'trim|required');
        $this->form_validation->set_rules('linkId', 'link id', 'trim|required');
        $this->form_validation->set_rules('userId', 'user id', 'trim|required');
        if ($this->form_validation->run() === true):
            $post = $this->input->post();
            $this->load->library('curl');
            $response = $this->curl->post(apiURL("link/updateCoustomezeLink/"), $post);
            $response = $this->exitOnly($response);
            if ($response->status == true) {
                return $this->exitSuccess();
            } else {
                return $this->exitDanger();
            }
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
    function getcustomizeUrl()
    {
        $this->form_validation->set_rules('linkId', 'link id', 'trim|required');
        if ($this->form_validation->run() === true):
            $post = $this->input->post();
            $userSession = $this->auth->user();
            $post['userId'] = $userSession->id;
            $this->load->library('curl');
            $response = $this->curl->post(apiURL("link/customizeUrls/"), $post);
            return $this->exitOnly($response);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
	
	function generateQRCode()
    {
        $this->form_validation->set_rules('linkId', 'link id', 'trim|required');
        $this->form_validation->set_rules('userId', 'user id', 'trim|required');
        if ($this->form_validation->run() === true):
            $post = $this->input->post();
            $this->load->library('curl');
            $response = $this->curl->post(apiURL("link/GenerateQRCode/"), $post);
            $response = $this->exitOnly($response);
            return $this->exitSuccessWithOne(['response' => $response]);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }


}
