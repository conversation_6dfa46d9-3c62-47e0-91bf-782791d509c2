<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

if (!function_exists('numberToDecimal')) {

    function numberToDecimal($number) {
        $decimal = '.';
        $brokenNumber = explode($decimal, $number);
        if (is_array($brokenNumber) && count($brokenNumber) == 2) {
            return(strlen($brokenNumber[1]) == 2) ? $brokenNumber[0] . '.' . $brokenNumber[1] : ((strlen($brokenNumber[1]) == 1) ? $brokenNumber[0] . '.' . $brokenNumber[1] . '0' : $brokenNumber[0] . '.' . substr($brokenNumber[1], 2));
        } elseif (is_array($brokenNumber) && count($brokenNumber) == 1) {
            return $brokenNumber[0] . '.00';
        } else {
            return '00.00';
        }
    }

}

function concatStringIfNotNull($string, $column) {
    $newColumn = explode('.', $column);
    $newColumn = array_pop($newColumn);
    return 'IF(' . $column . ' IS NULL OR ' . $column . ' = "", "", concat("' . $string . '",' . $column . ')) ' . $newColumn;
}

if (!function_exists('d')) {

    function d(...$data) {
        if (isArray($data)) {
            foreach ($data as $k => $v) {
                echo "<pre>";
                print_r($v);
                echo "</pre>";
            }
        }
    }

}
if (!function_exists('dd')) {

    function dd(...$data) {
        if (isArray($data)) {
            foreach ($data as $k => $v) {
                echo "<pre>";
                print_r($v);
                echo "</pre>";
            }
        }
        die;
    }

}
if (!function_exists('deleteDirectory')) {

    function deleteDirectory($dirPath) {
        if (is_dir($dirPath)) {
            $objects = scandir($dirPath);
            foreach ($objects as $object) {
                if ($object != "." && $object != "..") {
                    if (filetype($dirPath . DIRECTORY_SEPARATOR . $object) == "dir") {
                        deleteDirectory($dirPath . DIRECTORY_SEPARATOR . $object);
                    } else {
                        unlink($dirPath . DIRECTORY_SEPARATOR . $object);
                    }
                }
            }
            reset($objects);
            rmdir($dirPath);
        }
    }

}
if (!function_exists('makeDirectory')) {

    function makeDirectory($dirPath) {
        if (!file_exists($dirPath)) {
            $dirs = explode('/', $dirPath);
            $newDirPath = '';
            if ($dirs[0] != '.') {
                $dirPath = './' . $dirPath;
            }
            @mkdir($dirPath, 0777, true);
        }
    }

}
if (!function_exists('validationRules')) {

    /** getInputs creating insert input function and validation array
     * used to creating validation array and getting input field array.
     */
    function validationRules() {
        echo "if(\$this->input->method()==='post'){<br>";
        foreach ($_POST as $key => $value) {
            $label = $key;
            if (strtolower($label) == 'titlename') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'required');<br>";
            } elseif (strtolower($label) == 'firstname') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'trim|required|alpha|min_length[2]|max_length[40]');<br>";
            } elseif (strtolower($label) == 'middlename') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'trim|required|alpha|min_length[2]|max_length[40]');<br>";
            } elseif (strtolower($label) == 'lastname') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'trim|required|alpha|min_length[2]|max_length[40]');<br>";
            } elseif (strtolower($label) == 'email') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'trim|required|valid_email|max_length[100]');<br>";
            } elseif (strtolower($label) == 'password') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'required| min_length[6]|max_length[50]');<br>";
            } elseif (strtolower($label) == 'confirmpassword') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'required|matches[password]');<br>";
            } elseif (strtolower($label) == 'newpassword') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'required| min_length[6]|max_length[50]');<br>";
            } elseif (strtolower($label) == 'confirmnewpassword') {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'required|matches[newpassword]');<br>";
            } else {
                echo "\$this->form_validation->set_rules('" . $key . "', '" . camelCaseToString($label) . "', 'trim|required');<br>";
            }
        }
        echo "if (\$this->form_validation->run() == True){";


        echo "<br>}<br>}";
        die;
    }

}

function isValidationError($name) {
    return (validation_errors()) ? ((form_error($name)) ? true : false ) : false;
}

if (!function_exists('isArray')) {

    function isArray($array) {
        return (is_array($array) && count($array) > 0) ? true : false;
    }

}
if (!function_exists('isObject')) {

    function isObject($obj) {
        return (is_object($obj) && count((array) $obj) > 0) ? true : false;
    }

}
if (!function_exists('isArrayOfArray')) {

    function isArrayOfArray($arrayOfArray) {
        return (isArray($arrayOfArray) && isArray(current($arrayOfArray))) ? true : false;
    }

}
if (!function_exists('isArrayOfObject')) {

    function isArrayOfObject($arrayOfObject) {
        return (isArray($arrayOfObject) && isObject(current($arrayOfObject))) ? true : false;
    }

}
if (!function_exists('isNotEmptyAndNull')) {

    function isNotEmptyOrNull($val) {
        return ($val != '' && $val != NULL) ? true : false;
    }

}
if (!function_exists('lastQuery')) {

    function lastQuery() {
        $ci = & get_instance();
        d($ci->db->last_query());
    }

}
if (!function_exists('convertNullToStr')) {

    function convertNullToStr($var) {
        return is_null($var) ? '' : (empty($var) ? '' : $var);
    }

}
if (!function_exists('convertNullToInt')) {

    function convertNullToInt($var) {
        return is_null($var) ? 0 : (empty($var) ? 0 : $var);
    }

}
if (!function_exists('isSetToInt')) {

    function isSetToInt($var) {
        return isset($var) ? $var : 0;
    }

}if (!function_exists('isSetToStr')) {

    function isSetToStr($var) {
        return isset($var) ? $var : '';
    }

}

function getTableRowClassByStatus($isStatus) {
    $class = 'success';
    if ($isStatus == 'inactive') {
        $class = 'warning';
    } elseif ($isStatus == 'delete') {
        $class = 'danger';
    } elseif ($isStatus == 'coming') {
        $class = 'warning';
    } elseif ($isStatus == 'running') {
        $class = 'info';
    }
    return $class;
}

function getIconByStatus($isStatus) {
    $icon = '<i class="fa fa-check-square-o text-success"></i>';
    if ($isStatus == 'inactive') {
        $icon = '<i class="fa fa-check-square-o text-success"></i>';
    } elseif ($isStatus == 'delete') {
        $class = 'danger';
    } elseif ($isStatus == 'coming') {
        $class = 'warning';
    } elseif ($isStatus == 'running') {
        $class = 'info';
    }
    return $class;
}

if (!function_exists('makeHumanName')) {

    function makeHumanName($user) {
        $name = array_key_exists('titleName', $user) ? $user->titleName . ' ' : '';
        $name .= array_key_exists('firstName', $user) ? $user->firstName . ' ' : '';
        $name .= array_key_exists('middleName', $user) ? $user->middleName . ' ' : '';
        $name .= array_key_exists('lastName', $user) ? $user->lastName : '';
        $name = array_key_exists('name', $user) ? $user->name : $name;
        return ucwords($name);
    }

}

if (!function_exists('makeURIName')) {

    function makeURIName($user) {
        return strtolower(str_replace(" ", "-", makeHumanName($user)));
    }

}

if (!function_exists('dbJsonDecode')) {

    function dbJsonDecode($jsonString, $key = "", $isArray = false) {
        if ($key === "" || $key === null) {
            return json_decode((string) $jsonString);
        } else {
            $jsonObject = json_decode((string) $jsonString);
            if ($isArray === false) {
                return $jsonObject->$key;
            } else {
                return $jsonObject[$key];
            }
        }
    }
}

if(!function_exists('imeiFifteenthDigit')) {

    function imeiFifteenthDigit($imeiNumber) {
        $imeiArray = str_split($imeiNumber);
                $odd = array();
                $even = array();
                $both = array(&$odd, &$even);
                array_walk($imeiArray, function($v, $k) use ($both) { $both[$k % 2][] = $v; });
                //dd(array_sum($odd));
            
                $evenSum = 0;
                sort($even);
                //dd($even);
                //$i = 1;
                foreach($even as $ev) {
                    if( $ev>=0 && $ev<=4 ) {
                        $evenSum += $ev * 2;
                        //echo ($ev * 2).'|'.$ev;
                    } elseif($ev>=5 && $ev<=8 ) {
                        if($ev==5) {
                            $evenSum += 1;
                            //echo (1).'|'.$ev;
                        } else if($ev==6) {
                            $evenSum += 3;
                            //echo (3).'|'.$ev;
                        } else if($ev==7) {
                            $evenSum += 5;
                            //echo (5).'|'.$ev;
                        } else {
                            $evenSum += 7;
                            //echo (7).'|'.$ev;
                        }
                        //echo $ev;
                    } else {
                        $evenSum += $ev;
                        //echo $ev;
                    }
                    //echo "\n";
                }
                //die;
                //dd($evenSum);
                
                $evenoddSum = $evenSum + array_sum($odd);
                $nearTen = ceil($evenoddSum / 10) * 10;
                $fifteenthNumber = $nearTen - $evenoddSum;

                return $fifteenthNumber;
    }
}

if (!function_exists('companyInfo')) {
    function companyInfo($imeiNumber) {
        $ci = & get_instance();
        //$curlPost = "IMEI=".$imeiNumber;
 		$curlPost = json_encode(["IMEI"=>$imeiNumber]);
        $authToken = $ci->viewDataBag->userSession->auth_token;
        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCompanyName');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Cache-Control: no-cache';
		$headers[] = 'Content-Type: application/json';
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($cURLConnection);
        if (curl_errno($cURLConnection)) {
            echo 'Error:' . curl_error($cURLConnection);
        }
        curl_close($cURLConnection);
        $companyData = json_decode($result);
        return $companyData;
    }
}

if (!function_exists('uploadFile')) {
    function uploadFile($file, $path) {
        $ci = & get_instance();
        $result = [];
        require_once APPPATH . "/third_party/Excel2PHP/PHPExcel.php";
        $config['upload_path'] = $path;
        $config['allowed_types'] = 'xlsx|xls|csv|ods';
        $config['remove_spaces'] = TRUE;

        $ci->load->library('upload', $config);
        $ci->upload->initialize($config);

        if (!$ci->upload->do_upload($file)) {
            $error = array('error' => $ci->upload->display_errors());
        } else {
            $data = array('upload_data' => $ci->upload->data());
        }

        if(empty($error)){
            if (!empty($data['upload_data']['file_name'])) {
                $import_xls_file = $data['upload_data']['file_name'];
            } else {
                $import_xls_file = 0;
            }
            $inputFileName = $path . $import_xls_file;

            try {
                $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
                $objReader = PHPExcel_IOFactory::createReader($inputFileType);
                $objPHPExcel = $objReader->load($inputFileName);
                $data = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);
                $result = [
                    'filename'=>$import_xls_file,
                    'data'=>$data
                ];
                return $result;
            } catch (Exception $e) {
                return 'Error loading file "' . pathinfo($inputFileName, PATHINFO_BASENAME). '": ' .$e->getMessage();
            }

        } else {
            return $error['error'];
        }

    }
}

if (!function_exists('accessModule')) {
    function accessModule($module) {
        $ci = & get_instance();
        $ci->load->model('UserModuleModel');
        $user_id = $ci->viewDataBag->userSession->id;
        $res = $ci->UserModuleModel->check($module,$user_id);
        return $res;
    }
}

if (!function_exists('userModules')) {
    function userModules() {
        $ci = & get_instance();
        $user_id = $ci->viewDataBag->userSession->id;
        $ci->load->model('UserModuleModel');
        $result = $ci->UserModuleModel->find("userId=".$user_id,"module");
        $data = [];
        foreach($result as $res) {
            $data[] = $res->module;
        }
        return $data;
    }
}

if (!function_exists('setting')) {
    function setting($name) {
        $ci = & get_instance();
        $ci->load->model('settingModel');
        $result = $ci->settingModel->findOne(['name'=>$name]);
        return $result;
    }
}

if (!function_exists('encrypt_url')) {
    function encrypt_url($string) {
        $output = false;
        /*
         * read security.ini file & get encryption_key | iv | encryption_mechanism value for generating encryption code
         */
         /*$security = parse_ini_file('security.ini');
         $secret_key = $security['encryption_key'];
         $secret_iv = $security['iv'];
         $encrypt_method = $security['encryption_mechanism'];*/
         $CI =& get_instance();
         $secret_key = $CI->config->item('encryption_key');
         $secret_iv = $CI->config->item('iv');
         $encrypt_method = $CI->config->item('encryption_mechanism');

         // hash
         $key = hash('sha256', $secret_key);
         // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
         $iv = substr(hash('sha256', $secret_iv), 0, 16);
         //do the encryption given text/string/number
         $result = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
         $output = base64_encode($result);
         return $output;
    }
}

if (!function_exists('decrypt_url')) {
    function decrypt_url($string) {
        $output = false;
        /*
         * read security.ini file & get encryption_key | iv | encryption_mechanism value for generating encryption code
         */
         //$security = parse_ini_file('security.ini');
         $CI =& get_instance();
         $secret_key = $CI->config->item('encryption_key');
         $secret_iv = $CI->config->item('iv');
         $encrypt_method = $CI->config->item('encryption_mechanism');
         // hash
         $key = hash('sha256', $secret_key);
         // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
         $iv = substr(hash('sha256', $secret_iv), 0, 16);
         //do the decryption given text/string/number
         $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
         return $output;
    }
}

if (!function_exists('generateQRCode')) {
    function generateQRCode($data, $filename, $level = 'L', $size = 6, $margin = 2) {
        include APPPATH . 'third_party/phpqrcode/qrlib.php';
        
        // Define the path where the QR code will be saved
        $filepath = FCPATH . 'uploads/qr/' . $filename;

        // Generate the QR code
        QRcode::png($data, $filepath, $level, $size, $margin);

        return base_url('uploads/qr/' . $filename);
    }
}
if (!function_exists('isJson')) {
	function isJson($string)
	{
		json_decode($string);
		return (json_last_error() === JSON_ERROR_NONE);
	}
}

if (!function_exists('findValueByKey')) {
	function findValueByKey($data, $key=null) {
		
        // If key is not found
        if(!$key || $key == null) {
            return null;
        }

        /* if (is_array($data)) {
            foreach ($data as $k => $v) {
                // If the key matches, return the value
                if (stripos($k, $key) !== false) {
                    return $v;
                }

                // If the value is an array or object, recursively search
                if (is_array($v) || is_object($v)) {
                    $result = findValueByKey($v, $key);
                    if ($result !== null) {
                        return $result;
                    }
                }
            }
        }  */
        if (is_object($data) || is_array($data)) {
            foreach ($data as $k => $v) {
				
                // If the value is an array or object, recursively search
                if (is_array($v) || is_object($v)) {
                    $result = findValueByKey($v, $key);
                    if ($result !== null) {
                        return $result;
                    }
                } else {
                    // If the key matches, return the value
                    //if ($k === $key) {
                    if (stripos($k, $key) !== false) {
                        return $v;
                    }
                }
            }
        }
    }

}

if (!function_exists('extractNumberAfterKeyword')) {
    function extractNumberAfterKeyword($text, $keywords) {		
        $numbers = [];
        foreach($keywords as $keyword) {
			
            // Build a dynamic regex pattern for the given keyword
            //$pattern = '/' . preg_quote($keyword, '/') . '\s+([\d\-]+)/i';
			//$pattern = '/' . preg_quote($keyword, '/') . '\s+([a-zA-Z0-9\-]{6,25})/i';
			//$pattern = '/' . preg_quote($keyword, '/') . '\s*([a-zA-Z0-9\-]{1,50})/i';
			$pattern = '/' . preg_quote($keyword, '/') . '\s*([a-zA-Z0-9\-.]{1,50})/i';
            
            if (preg_match($pattern, $text, $matches)) {				
                $numbers[] = str_replace('-', '', $matches[1]); // captured number
            }
        }
		
		return $numbers;
    }
}

if (!function_exists('writeLog')) {
	function writeLog($message, $file) {
		$log_path = APPPATH . 'logs/'; // CodeIgniter's logs folder
		$filename = $log_path . $file . '-' . date('Y-m-d') . '.log'; // e.g., telegram_log-2025-04-26.log
		$content = "[" . date('Y-m-d H:i:s') . "] " . print_r($message, true) . "\n";

		file_put_contents($filename, $content, FILE_APPEND);
	}
}