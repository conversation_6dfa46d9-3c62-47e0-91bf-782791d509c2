<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Defining CURD function
 *
 * C = create (insert)
 * U = update
 * R = read  (select)
 * D = delete
 */
class MY_Model extends CI_Model
{

    protected $primaryTable = '';
    protected $primaryTablePK = '';

    function __construct($primaryTable, $primaryTablePK)
    {
        parent::__construct();
        $this->primaryTable = $primaryTable;
        $this->primaryTablePK = $primaryTablePK;
    }

    protected function setExtraParameters($extra)
    {
        if (!is_null($extra) && isArray($extra)) {
            if (array_key_exists('orderBy', $extra) && isArray($extra['orderBy'])) {
                $this->db->order_by($extra['orderBy'][0], $extra['orderBy'][1]);
            }
            if (array_key_exists('limit', $extra) && isArray($extra['limit'])) {
                $this->db->limit($extra['limit']['perPage'], $extra['limit']['offset']);
            }
        }
    }

    private function setSelectQueryParameters($select = null, $extra = null, $where = null, $col = null)
    {
        if (!is_null($select)) {
            $this->db->select($select);
        } else {
            $this->db->select('*');
        }
        if (!is_null($where)) {
            if (!is_null($col)) {
                $this->db->where_in($col, $where);
            } else {
                $this->db->where($where);
            }
        }
        $this->setExtraParameters($extra);
    }

    function all($select = null, $extra = null)
    {
        $this->setSelectQueryParameters($select, $extra);
        return $this->getResult($this->db->get($this->primaryTable));
    }

    function find($where, $select = null, $extra = null)
    {
        $this->setSelectQueryParameters($select, $extra, $where);
        return $this->getResult($this->db->get($this->primaryTable));
    }

    function findOne($where, $select = null)
    {
        $this->setSelectQueryParameters($select, '', $where);
        return $this->getRow($this->db->get($this->primaryTable));
    }

    function findIn($col, $where, $select = null, $extra = null)
    {
        $this->setSelectQueryParameters($select, $extra, $where, $col);
        return $this->getResult($this->db->get($this->primaryTable));
    }

    function findInOne($col, $where, $select = null)
    {
        $this->setSelectQueryParameters($select, '', $where, $col);
        return $this->getRow($this->db->get($this->primaryTable));
    }

    function attach($insertData)
    {
        if (isArray($insertData)) {
            $this->db->insert($this->primaryTable, $insertData);
            return $this->pushDB();
        }
        return false;
    }

    function attachMultiple($insertData)
    {
        if (isArray($insertData)) {
            $this->db->insert_batch($this->primaryTable, $insertData);
            return $this->pushDB();
        }
        return false;
    }

    function modify($updateData, $where)
    {
        if (isArray($updateData)) {
            $this->db->where($where)->update($this->primaryTable, $updateData);
            return true;
        }
        return false;
    }
	
	function incrementOrDecrement($column, $amount, $where)
	{
		// Ensure $amount is numeric
		if (!is_numeric($amount) || empty($column) || empty($where)) {
			return false;
		}

		// Build query
		$this->db->where($where);
		$this->db->set($column, "$column + ({$amount})", FALSE);
		return $this->db->update($this->primaryTable);
	}


    function modifyMultiple($updateData, $where)
    {
        list($key, $value) = each($where);
        if (isArray($updateData)) {
            $this->db->where_in($key, array_values($value))->update($this->primaryTable, $updateData);
            return true;
        }
        return false;
    }

    function increment($field, $increment = 1, $where = null)
    {
        $this->db->set($field, $field . "+" . $increment, FALSE);
        if ($where != null && $where != '') {
            $this->db->where($where);
        }
        $this->db->update($this->primaryTable);
        return true;
    }

    function decrement($field, $increment = 1, $where = null)
    {
        $this->db->set($field, $field . "-" . $increment, FALSE);
        if ($where != null && $where != '') {
            $this->db->where($where);
        }
        $this->db->update($this->primaryTable);
        return true;
    }

    function detach($where)
    {
        $this->db->where($where)->delete($this->primaryTable);
        return true;
    }

    function detachMultiple($where)
    {
        list($key, $value) = each($where);
        if (isArray($value)) {
            $this->db->where_in($key, array_values($value))->delete($this->primaryTable);
        } else {
            $this->db->where_in($key, $value)->delete($this->primaryTable);
        }
        return true;
    }

    function sum($field, $where = null)
    {
        $this->db->select_sum($field)
            ->from($this->primaryTable);
        if ($where != null && $where != '') {
            $this->db->where($where);
        }
        $sum = $this->db->get()->row()->$field;
        return $sum ? $sum : 0;
    }

    function count($where = null)
    {
        if ($where) {
            $this->db->where($where);
        }
        return $this->db->from($this->primaryTable)->count_all_results();
    }

    function has($where = NULL)
    {
        return ($this->db->where($where)->get($this->primaryTable)->num_rows() > 0) ? true : false;
    }

    protected function pushDB()
    {
        /**
         * Insert data into the database
         *
         * @param type $table_name inserting table name
         * @param type $data inserting data array
         * @return boolean or number
         */
        return ($this->db->affected_rows() > 0) ? (($this->primaryTablePK != '' && $this->primaryTablePK != NULL) ? $this->db->insert_id() : true) : false;
    }

    protected function putDB()
    {
        /*
         *  check  record updated or not

         * @return boolean
         */
        return $this->checkAction();
    }

    protected function flushDB()
    {
        /*
         * check  record deleted or not
         * @return boolean
         */
        return $this->checkAction();
    }

    private function checkAction()
    {
        return ($this->db->affected_rows() > 0) ? true : false;
    }
    protected function checkTransAction() {
        $this->db->trans_complete();
        //check if transaction status TRUE or FALSE
        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return FALSE;
        } else {
            $this->db->trans_commit();
            return TRUE;
        }
    }
    protected function getResult($query)
    {
        return ($query->num_rows() > 0) ? $query->result() : [];
    }

    protected function getResultArray($query)
    {
        return ($query->num_rows() > 0) ? $query->result_array() : [];
    }

    protected function getRow($query)
    {
        return ($query->num_rows() > 0) ? $query->row() : (object)[];
    }

}
