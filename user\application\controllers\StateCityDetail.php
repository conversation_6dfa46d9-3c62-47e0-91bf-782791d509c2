<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class StateCityDetail extends SecureController {

    function __construct() {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->library('curl');
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('std_code', 'STD Code', 'trim|required|xss_clean|min_length[2]|max_length[6]');
            if ($this->form_validation->run() === true) {
                $std_code = $this->input->post("std_code");
                $curlPost = "SortCode=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $CCASApiUrl = $this->config->item('CCASApiUrl','env');
                $cURLConnection = curl_init($CCASApiUrl.'GetCityNameAndStateNameBySTDCode?STDCode='.$std_code);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $stateDetails = json_decode($result);
                $response = $stateDetails->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('stateDetail/listView', $this->viewDataBag);
    }

    

}