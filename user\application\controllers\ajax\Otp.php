<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Otp extends AjaxInSecureController {

    function __construct() {
        parent::__construct();
    }

    function registration() {
        if ($this->session->has(USER_REGISTRATION_SESSSION)) {
            $userRagistrationData = $this->session->get(USER_REGISTRATION_SESSSION);
            $now = strtotime(currentDateTime());
            if ($now - $userRagistrationData->activeTime <= MAX_OTP_TIME) {
                if (isset($userRagistrationData->otp)):
                    $message = $userRagistrationData->otp . " " . REGISTRATION_OTP_MESSAGE;
                    $userRagistrationData->otpCount += $userRagistrationData->otpCount;
                    $this->session->push($userRagistrationData, USER_REGISTRATION_SESSSION);
                    $this->sms->dispatch($userRagistrationData->mobile, $message);
                    $this->exitSuccessWithOne(["message" => "Verification code sent!", "otpResendLinkDisable" => (bool) false]);
                else:
                    $this->exitDanger('Session has been expired. Sign up again!');
                endif;
            } else {
                $this->exitDanger('Session has been expired. Sign up again!');
            }
        } else {
            $this->exitDanger('Session has been expired. Sign up again!');
        }
    }

    function forgotPassword() {
        if ($this->session->has(USER_FORGOTPASSWORD_SESSSION)) {
            $userData = $this->session->get(USER_FORGOTPASSWORD_SESSSION);
            $now = strtotime(currentDateTime());
            if ($now - $userData->activeTime <= MAX_OTP_TIME) {
                if (isset($userData->otp)):
                    $message = $userData->otp . " " . FORGOTPASSWORD_OTP_MESSAGE;
                    $userData->otpCount += $userData->otpCount;
                    $this->session->push($userData, USER_FORGOTPASSWORD_SESSSION);
                    $this->sms->dispatch($userData->mobile, $message);
                    $this->exitSuccessWithOne(["message" => "Verification code sent!", "otpResendLinkDisable" => (bool) false]);
                else:
                    $this->exitDanger('Session has been expired. Try again!');
                endif;
            } else {
                $this->exitDanger('Session has been expired. Try again!');
            }
        } else {
            $this->exitDanger('Session has been expired. Try again!');
        }
    }

}
