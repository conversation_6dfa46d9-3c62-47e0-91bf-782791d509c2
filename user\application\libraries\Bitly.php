<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Bitly{

    private $ci;
    function __construct() {
        $this->ci = & get_instance();
        $this->ci->config->load('env', true);
    }
    function urlShorten($trackUrl){
        if(empty($trackUrl)){
          return false;  
        }
        $bitlyApiUrl = $this->ci->config->item('BitlyApiUrl','env');
        $login = $this->ci->config->item('BitlyLogin','env');
        $apiKey = $this->ci->config->item('BitlyApiKey','env');
        $url=$bitlyApiUrl.'?login='.$login.'&apiKey='.$apiKey.'&format=json&longUrl='.$trackUrl;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
         $response = curl_exec($ch);
        if(curl_errno($ch)){
           $error=curl_error($ch);
           return false;
        }
        curl_close($ch);
        return $response;
    }
}
?>