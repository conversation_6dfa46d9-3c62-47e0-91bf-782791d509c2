<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Cin extends SecureController
{

    function __construct()
    {
        parent::__construct();
    }

    function callApi($url, $params) {
        $authToken = $this->viewDataBag->userSession->auth_token;

        $ch = curl_init();
		$param = json_encode($params);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Content-Type: application/json';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $result = curl_exec($ch);

		if($result === false) {
			$this->alert->danger(curl_error($ch));
			return redirectToBack();
		}

        $response = json_decode($result);
		curl_close($ch);
        return $response;
    }

    function search(){
		
		$cinData = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('queryString', 'CIN', 'trim|required');
			
            if ($this->form_validation->run() === true) {

                $cinNo = explode(" ",$this->input->post("queryString"));
				
				if(count($cinNo)>10) {
                    $this->alert->danger("Only 10 CIN are allowed.");
                    return redirectToItSelf('search/');
				}
				
                try {	
					
                    $result = $this->callApi('https://mobileapi.lisw.in/api/Tac/GetCINDataAPI', ['Cin'=> $cinNo ] );
					
					if (!empty($result)):
					
						//$this->alert->danger('Not Found');
						
						foreach($result as $key => $res) {
							
							if(!empty($res->cindata)) {
								
								//$directorData = $res->cindata->data->directorData;
								$directorData = $res->getDirectorModelDataInfos->data;

								$uniqueDin = array_unique(array_map(function($director) {
									return $director->din;
								}, $directorData));
								$dinData = implode(",",$uniqueDin);
								
								$cinData[$key]['companyData'] = $res->cindata->data->companyData;
								$cinData[$key]['companyData']->din = $dinData;
							}
						}
						$this->alert->success("Successfully Done");
					else:
						$this->alert->danger('Not Found');
						return redirectToItSelf('search/');
					endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'cin/jsView';
		$this->viewDataBag->cinData = $cinData;
        $this->loadView('cin/searchView', $this->viewDataBag);
    }

	function search_by_name(){
		$cinData = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('companyName', 'Name', 'trim|required');
			$this->form_validation->set_rules('companyType', 'Company Type', 'required');
			
            if ($this->form_validation->run() === true) {
				
				$companyName = $this->input->post("companyName");
        		$companyType = jsDropdownSelectedValue($this->input->post('companyType'));
					
                try {
						
					$result = $this->callApi('https://mobileapi.lisw.in/api/Tac/CompanynameSearch', ['CompanyType'=> $companyType, 'CompanyName'=>$companyName ] );

					if (!empty($result)):
						
						$this->alert->danger('Not Found');
						
						foreach($result as $key => $res) {
							
							if(!empty($res->cindata)) {
								$directorData = $res->getDirectorModelDataInfos->data;
								//$directorData = $res->cindata->data->directorData;
								
								$uniqueDin = array_unique(array_map(function($director) {
									return $director->din;
								}, $directorData));
								$dinData = implode(",",$uniqueDin);
								
								$cinData[$key]['companyData'] = $res->cindata->data->companyData;
								$cinData[$key]['companyData']->din = $dinData;
								
								$this->alert->success("Successfully Done");
							}
						}
					else:
						$this->alert->danger('Not Found');
					endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
            }
        }
		
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'cin/jsView';
		$this->viewDataBag->cinData = $cinData;
        $this->loadView('cin/searchByNameView', $this->viewDataBag);
    
	}

	function detail() {
		
		$cin = urldecode($this->input->get('cin'));
		$din = urldecode($this->input->get('din'));
		
		if(!$cin)
			show_404();
		
		$cinArr = explode(",",$cin);
		$dinArr = explode(",",$din);
		$dinData = '';
		
		try {	
					
			$result = $this->callApi('https://mobileapi.lisw.in/api/Tac/GetCINDataAPI', ['Cin'=> $cinArr ] );
			//$result = $this->callApi('https://mobileapi.lisw.in/api/Tac/CompanynameSearch', ['CompanyType'=> $companyType, 'CompanyName'=>$companyName ] );
			
			if (!empty($result)) {
			
				$this->alert->danger('Not Found');
				
				foreach($result as $key => $res) {
					if(!empty($res->cindata)) {
						$this->alert->success("Successfully Done");
						$dinData = $res->getDirectorModelDataInfos->data;
					}
				}
				
                $result = $this->callApi('https://mobileapi.lisw.in/api/Tac/GetDinAddressAPI', ['Din'=> $dinArr ] );
                if (!empty($result)) {
                    foreach($result as $key => $res) {
                        if(!empty($res->getDinpandata->data)) {
                            $dinData[$key]->fullAddress = $res->getDinpandata->data;
                        }
                    }
                }
				
				
				
            } else {
				$this->alert->danger('Not Found');
				return redirectToBack();
            };
			
		} catch (Exception $e) {
			$this->alert->danger($e->getMessage());
		}
		
		$this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'cin/jsView';
		$this->viewDataBag->dinData = $dinData;
        $this->loadView('cin/detailView', $this->viewDataBag);
	}

	function address_detail() {
		$din = urldecode($this->input->get('din'));
		
		if(!$din)
			show_404();
		
		$dinArr = explode(",",$din);
		$dinData = [];

		try {

            $result = $this->callApi('https://mobileapi.lisw.in/api/Tac/GetDinAddressAPI', ['Din'=> $dinArr ] );
			
			
			if (!empty($result)):
				$this->alert->danger('Not Found');
				foreach($result as $res) {
					if(!empty($res->getDinpandata->data)) {
						$dinData[] = $res->getDinpandata->data;
						$this->alert->success("Successfully Done");
					}
				}
			else:
				$this->alert->danger('Not Found');
			endif;
			
			if(count($dinData)==0) {
				$this->alert->danger('Not Found');
			}
		
		} catch (Exception $e) {
			$this->alert->danger($e->getMessage());
		}

		$this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'cin/jsView';
		$this->viewDataBag->dinData = $dinData;
        $this->loadView('cin/addressDetailView', $this->viewDataBag);
	}

}