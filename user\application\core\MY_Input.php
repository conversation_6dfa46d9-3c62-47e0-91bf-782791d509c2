<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Input extends CI_Input {

    public $projectName;

    function __construct() {
        parent::__construct();
    }

    function only(...$inputs) {

        return $this->post($inputs, TRUE);
    }

    function extra($extra) {
        $returnInputArray = [];
        if (isArray($extra)) {
            if (array_key_exists('selectedValue', $extra) && isArray($extra['selectedValue'])) {
                foreach ($extra['selectedValue'] as $key => $name) {
                    $returnInputArray = array_merge($returnInputArray, [$name => myDropdownSelectedValue($name)]);
                }
            }
            if (array_key_exists('selectedName', $extra) && isArray($extra['selectedName'])) {
                foreach ($extra['selectedName'] as $key => $name) {
                    $returnInputArray = array_merge($returnInputArray, [$name => myDropdownSelectedName($name)]);
                }
            }
            if (array_key_exists('date', $extra) && isArray($extra['date'])) {
                foreach ($extra['date'] as $key => $name) {
                    $returnInputArray = array_merge($returnInputArray, [$name => ($this->post($name)) ? dbDate($this->post($name)) : '']);
                }
            }
            if (array_key_exists('dateTime', $extra) && isArray($extra['dateTime'])) {
                foreach ($extra['dateTime'] as $key => $name) {
                    $returnInputArray = array_merge($returnInputArray, [$name => ($this->post($name)) ? dbDateTime($this->post($name)) : '']);
                }
            }
        }
        return $returnInputArray;
    }

    function onlyWithExtra($only, $with = [], $extra = []) {
        $returnInputArray = [];
        if (isArray($only)) {
            $returnInputArray = array_merge($returnInputArray, $this->post($only, TRUE));
        }
        if (isArray($with)) {
            $returnInputArray = array_merge($returnInputArray, $with);
        }

        $returnInputArray = array_merge($returnInputArray, $this->extra($extra));
        return $returnInputArray;
    }

    function all($with = [], $without = []) {
        $returnInputArray = $this->post();
        if (isArray($without)) {
            foreach ($without as $key) {
                unset($returnInputArray[$key]);
            }
        }
        if (isArray($with)) {
            $returnInputArray = array_merge($returnInputArray, $with);
        }

        return $returnInputArray;
    }

    function allWithout(...$without) {
        $returnInputArray = $this->post();
        if (isArray($without)) {
            foreach ($without as $key) {
                unset($returnInputArray[$key]);
            }
        }
        return $returnInputArray;
    }

    function allWith($with = null) {
        $returnInputArray = $this->post();
        if (isArray($with)) {
            $returnInputArray = array_merge($returnInputArray, $with);
        }
        return $returnInputArray;
    }

    function makePasswordHash($inputName) {
        $options = ['cost' => 8,
            'salt' => mcrypt_create_iv(22, MCRYPT_DEV_URANDOM)];
        return password_hash($this->post($inputName), PASSWORD_BCRYPT, $options);
    }

    function verifyPasswordHash($inputName, $hash) {
        return password_verify($this->post($inputName), $hash) ? true : false;
    }

}
