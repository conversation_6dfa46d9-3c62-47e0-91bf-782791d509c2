<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Link extends SecureController
{
	
    function __construct()
    {
        parent::__construct();
		//show_404();
		$this->load->model('UserModuleModel');
		$user_id = $this->viewDataBag->userSession->id;
		$res = $this->UserModuleModel->check('ip_grabber',$user_id);
		//die;
        if ( empty($res) || strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime()) ) {
           return redirectTo("me/dashboard");
        }
    }
	
	function add_new()
    {
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/sendLink/sendBySms.js"),
            base_url("public/assets/js/plugin/link/common.js"),
            base_url("public/assets/js/plugin/link/create.js")];
        $this->viewDataBag->jsView = 'link/jsView';
        $this->loadView('link/createView', $this->viewDataBag);
    }

    function add_qr_new()
    {
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/sendLink/sendBySms.js"),
            base_url("public/assets/js/plugin/link/common.js"),
            base_url("public/assets/js/plugin/link/create.js")];
        $this->viewDataBag->jsView = 'link/jsView';
        $this->loadView('link/createQRView', $this->viewDataBag);
    }
	
    function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */
        $searchItem = $this->input->get('searchItem');
        $isStatus = $this->input->get('isStatus');

        $isStatus = jsDropdownSelectedValue($isStatus);
        $where = '';
        if ($searchItem != '') {
            $where .= '( victimNameOrFir like "%' . $searchItem . '%" || redirectUrl like "%' . $searchItem . '%" || isRedirect like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(createdOn) >=  "' . $from . '" && DATE(createdOn) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(createdOn) >=  "' . $from . '" && DATE(createdOn) <=  "' . $to . '")';
            endif;
        }
        if ($isStatus != '') {
            if ($where != ''):
                $where .= ' AND ( isStatus = "' . $isStatus . '")';
            else:
                $where = ' ( isStatus = "' . $isStatus . '")';
            endif;
        }
        $where .= ' AND ( userId = "' . $this->viewDataBag->userSession->id . '")';

        if ($isStatus == '' && $searchItem == '' && $from == '' && $to == '') {
            $where = ' ( userId = "' . $this->viewDataBag->userSession->id . '")';
        }
        $perPage = 50;
        $this->load->model('linkModel');
        $totalRows = $this->linkModel->count($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;

        $this->viewDataBag->links = $this->linkModel->find($where, '*', ['orderBy' => ['createdOn', "DESC"],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        $this->viewDataBag->jsView = 'link/jsView';
        $this->loadView('link/listView', $this->viewDataBag);
    }

    function details($slug)
    {

        $linkId = base64_decode(slugId());

        if ($linkId) {
            $this->load->model('linkModel');
            $linkData = $this->linkModel->findOne(['id' => $linkId,'userId' => $this->viewDataBag->userSession->id], '*');
            if (isObject($linkData)) {
                $this->viewDataBag->linkData = $linkData;
                $this->load->model('locationModel');
                $locationTrack = $this->locationModel->find(['linkId' => $linkId, 'userId' => $this->viewDataBag->userSession->id], "*");
               if (isArray($locationTrack)) {
                    $this->viewDataBag->linkDetails = $locationTrack;
                    $this->viewDataBag->noRecordFound = FALSE;
                } else {
                    $this->viewDataBag->noRecordFound = TRUE;
                }
                $this->viewDataBag->linkId = $linkId;
                $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
                    base_url("public/assets/js/plugin/link/common.js"),
                    base_url("public/assets/js/plugin/sendLink/sendBySms.js"),
                    base_url("public/assets/js/plugin/link/detail.js")];
                $this->viewDataBag->jsView = 'link/jsView';
                $this->loadView('link/detailsView', $this->viewDataBag);
            } else {
                show_404();
            }
        } else {
            show_404();
        }
    }
	
	public function generateExcel()
	{
		$tokenVal = bin2hex(openssl_random_pseudo_bytes(16 / 2));
		
		// Data to be encoded in the QR code
        $data = 'https://msg.ccas.in/api/token/'.$tokenVal;
        
        // File name for the generated QR code
        $filename = 'qrcode.png';

        $imgUrl = generateQRCode($data, $filename);

        echo '<img src="'.$imgUrl.'">'; die;
		
		$this->load->library('excel');
		$d = $this->excel->getData(FCPATH . 'uploads/Link-1716193418.xlsx',1,[]);
		
		dd($d);
		die;
        $this->excel->customLink([], "Link");
        return '';
	}
		
}
