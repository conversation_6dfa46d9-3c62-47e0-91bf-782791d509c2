<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Osint extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$this->load->model('OsintModel');
        $this->load->model("userModel");
		$this->load->model("OsintApplicationModel");
		$this->load->library('Encryption');
    }

    function search(){
		$user_id = $this->viewDataBag->userSession->id;

        $input = [
            'user_id' => $user_id,
            'mobile' => '+919461101915',
        ];        

        //['userId' => $user_id]        
		$osintData = $this->OsintModel->find(['userId' => $user_id], "*",['orderBy'=>['id','desc']]);
        $userData = $this->userModel->findOne(['id' => $user_id], "osintCredit");

        if ($this->input->method() === 'post'){

            $param = [];

            //check user credit
            if ($userData->osintCredit == 0) {
                $this->alert->danger("Your Search Credit Over.");
                return redirectToItSelf('search/');
            }

            if($this->input->post("mobile")) {
                $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|regex_match[/^[0-9]{10}$/]');
                $username = $this->input->post("mobile");
                $param['MobileNumber'] = $username;
				$param['EmailId'] = '';
            } else {
                $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                $username = $this->input->post("email");
                $param['EmailId'] = $username;
				$param['MobileNumber'] = '';
            }
			
            if ($this->form_validation->run() === true) {
                
                try {

                    //$authToken = $this->viewDataBag->userSession->auth_token;
				
                    $inputData['userId'] = $user_id;
                    $inputData['username'] = $username;                    

                    $osintId = $this->OsintModel->attach($inputData);

                    if ($osintId):

                        $this->userModel->modify(["osintCredit" => $userData->osintCredit-1], ['id' => $user_id]);
                        $this->alert->success("We have received your request. Please wait while we process your data.");
                    else:
                        $this->alert->danger(getErrorMessage());
                    endif;
                    
                } catch (Exception $e) {
                    $this->alert->danger($e->getMessage());
                }
				
                return redirectToCurrent();
            }
        }
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'osint/jsView';
		$this->viewDataBag->osintData = $osintData;
        $this->viewDataBag->userData = $userData;
        $this->loadView('osint/searchView', $this->viewDataBag);
    }

    function generateData()
    {		
        $this->config->load('osint_api_mappings');
		$osintId = decrypt_url($this->input->get('key'));
        if(!$osintId) {
            show_404();
        }
		$osintDetails = $this->OsintApplicationModel->find(['osintId'=>$osintId]);
        $osint = $this->OsintModel->findOne(['id' => $osintId]);
        $mappings = $this->config->item('api_field_mappings');
		$nameList = [];
        $emailList = [];
		$imageList = [];
        $osintList = [];
        $locationList = [];
        $leakData = [];
		
		foreach($osintDetails as $detail) {            
			
			if($detail->details && $detail->details !== 'null') {                

                $content =  isJson($detail->details) ? json_decode($detail->details) : $detail->details;

                //Google API Data
                if($detail->appName == 'GoogleAPI') {
                    $pairs = explode('||', $detail->details);
					$content = [];
					foreach ($pairs as $pair) {
						list($key, $value) = explode(': ', $pair);
						$content[$key] = $value;
					}
                }
				
                $fields = $mappings[$detail->appName];

                $id = isset($fields['id']) ? findValueByKey($content, $fields['id']) : null;
                $id = isset($fields['id']) ? findValueByKey($content, $fields['id']) : null;
                $name = isset($fields['name']) ? findValueByKey($content, $fields['name']) : null;
                $email = isset($fields['email']) ? findValueByKey($content, $fields['email']) : null;
                $image = isset($fields['image']) ? findValueByKey($content, $fields['image']) : null;
                $phone = isset($fields['phone']) ? findValueByKey($content, $fields['phone']) : null;
                $address = isset($fields['address']) ? findValueByKey($content, $fields['address']) : null;
                $state = isset($fields['state']) ? findValueByKey($content, $fields['state']) : null;
                $operator = isset($fields['operator']) ? findValueByKey($content, $fields['operator']) : null;
                $url = isset($fields['url']) ? findValueByKey($content, $fields['url']) : null;
                $account_exist = isset($fields['account_exist']) ? findValueByKey($content, $fields['account_exist']) : null;

                if(isset($name)) {
                    $nameList[$name] = true;
                }
                if(isset($email)) {
                    $emailList[$email] = true;
                }
                if(isset($address)) {
                    $locationList[$address] = true;
                }

                $extractedData = [
                    'api_name' => $detail->appName,
                    'account_exist' => $account_exist,
                    'id'    => $id,
                    'name'  => $name,
                    'email' => $email,
                    'image' => $image,
                    'phone' => $phone,
                    'state' => $state,
                    'address' => $address,
                    'operator' => $operator,
                    'url'   => $url
                ];
                $osintList[] = $extractedData;
			} elseif($detail->content && $detail->content !== 'null') {

                $content =  json_decode($detail->content);
				
				if(isset($content->error))
                    continue;
				
                $fields = $mappings[$detail->appName];

                if($detail->appName == 'LeakData API') {
                    
                    $leakData = $content->List;
					
                    foreach($content->List as $key => $value) {
                        $name = findValueByKey($value, $fields['name']);
                        $first_name = findValueByKey($value, $fields['first_name']);
                        $email = findValueByKey($value, $fields['email']);
                        $address = findValueByKey($value, $fields['address']);

                        if(isset($name)) {
                            $nameList[$name] = true;
                        }

                        if(isset($first_name)) {
                            $nameList[$first_name] = true;
                        }

                        if(isset($email)) {
                            $emailList[$email] = true;
                        }

                        if(isset($address)) {
                            $locationList[$address] = true;
                        }
                    }
                } else {
                    $id = isset($fields['id']) ? findValueByKey($content, $fields['id']) : null;
                    $uid = isset($fields['uid']) ? findValueByKey($content, $fields['uid']) : null;
                    $name = isset($fields['name']) ? findValueByKey($content, $fields['name']) : null;
                    $last_name = isset($fields['last_name']) ? findValueByKey($content, $fields['last_name']) : null;
                    $full_name = isset($fields['full_name']) ? findValueByKey($content, $fields['full_name']) : null;
                    $email = isset($fields['email']) ? findValueByKey($content, $fields['email']) : null;
                    $gender = isset($fields['gender']) ? findValueByKey($content, $fields['gender']) : null;
                    $image = isset($fields['image']) ? findValueByKey($content, $fields['image']) : null;
                    $phone = isset($fields['phone']) ? findValueByKey($content, $fields['phone']) : null;
                    $city = isset($fields['city']) ? findValueByKey($content, $fields['city']) : null;
                    $country = isset($fields['country']) ? findValueByKey($content, $fields['country']) : null;
                    $address = isset($fields['address']) ? findValueByKey($content, $fields['address']) : null;
                    $state = isset($fields['state']) ? findValueByKey($content, $fields['state']) : null;
                    $bio = isset($fields['bio']) ? findValueByKey($content, $fields['bio']) : null;
                    $operator = isset($fields['operator']) ? findValueByKey($content, $fields['operator']) : null;
                    $profileId = isset($fields['profile_id']) ? findValueByKey($content, $fields['profile_id']) : null;
                    $status = isset($fields['status']) ? findValueByKey($content, $fields['status']) : null;
                    $url = isset($fields['url']) ? findValueByKey($content, $fields['url']) : null;

                    if(isset($name)) {
						if($detail->appName == 'Telegram') {
							if($name == 'Not found'){
							}else{
								$nameArray = explode(',', $name);
								foreach ($nameArray as $n) {
									$trimmedName = trim($n); // remove leading/trailing spaces
									$nameList[$trimmedName] = true;
								}
							}
						}else{
							$nameList[$name] = true;
						}						
                    }
                    if(isset($email)) {
                        $emailList[$email] = true;
                    }
                    if(isset($address)) {
                        $locationList[$address] = true;
                    }

                    $extractedData = [
                        'api_name' => $detail->appName,
                        'id' => $id,
                        'uid' => $uid,
                        'name'  => $name,
                        'last_name' => $last_name,
                        'full_name' => $full_name,
                        'gender' => $gender,
                        'email' => $email,
                        'image' => $image,
                        'phone' => $phone,
                        'city'  => $city,
                        'state' => $state,
                        'country' => $country,
                        'address' => $address,
                        'bio' => $bio,
                        'operator' => $operator,
                        'profile_id' => $profileId,
                        'url' => $url,
                        'status' => $status,
                    ];
                    $osintList[] = $extractedData;
                }				
			} else {

                $extractedData = [
                    'api_name' => $detail->appName,
                    'account_exist' => 1
                ];
                $osintList[] = $extractedData;
            }

            $reportCreated = $detail->createdOn;
			
		}

        $nameList = array_keys($nameList);
        $emailList = array_keys($emailList);
		$locationList = array_keys($locationList);
		
        $pdfName = 'OSInt';
		
        $this->viewDataBag->osint = $osint;
		$this->viewDataBag->leakData = $leakData;
        $this->viewDataBag->osintList = $osintList;
		$this->viewDataBag->nameList = $nameList;
        $this->viewDataBag->emailList = $emailList;
		$this->viewDataBag->imageList = $imageList;
        $this->viewDataBag->locationList = $locationList;
        $this->viewDataBag->reportCreated = $reportCreated;
		
        $osint_data = $this->load->view('osint/pdf', $this->viewDataBag, true);
        $this->load->library('pdf');
        $this->pdf->createAndDownload($osint_data,$pdfName);
    }

}