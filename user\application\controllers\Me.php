<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Me extends SecureController
{

    function __construct()
    {
        parent::__construct();
    }

    function dashboard()
    {
		
        $this->viewDataBag->breadcrumb = 'DashBoard';
        $userId = $this->viewDataBag->userSession->id;

        $this->load->model('userModel');
        $userCredits = $this->userModel->findOne(['id' => $userId], 'truecallerSearchCredit,smsCredit');
        $this->viewDataBag->userCredits = $userCredits;

        $this->load->model('linkModel');
        $this->viewDataBag->totalLink = $this->linkModel->count(['userId' => $userId]);
        $this->viewDataBag->totalActiveLink = $this->linkModel->count(['userId' => $userId, 'isStatus' => 'active']);
        $this->viewDataBag->totalInactiveLink = $this->linkModel->count(['userId' => $userId, 'isStatus' => 'inActive']);
        $this->load->model('paymentModel');
        $paybleAmount = $this->paymentModel->findOne([], '*');
        $this->load->model('paymentDetailsModel');
        //if ($this->paymentDetailsModel->has(['userId' => $userId, 'paymentIsStatus' => 'success'])) {
        if ($this->viewDataBag->userSession->registrationType=='paid') {
            $this->viewDataBag->paybleAmount = $paybleAmount->renewPrice;
        } else {
            $this->viewDataBag->paybleAmount = $paybleAmount->proVersionPrice;
        }
        
        $this->viewDataBag->razorpay_keyid = $this->config->item('RAZORPAY_KEY_ID', 'env');
        $this->viewDataBag->jsView = 'dashboard/jsView';
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/user/user.js"), 'https://checkout.razorpay.com/v1/checkout.js'];
        $this->loadView('dashboard/dashboardView', $this->viewDataBag);
    }

    function profile()
    {
        $this->viewDataBag->breadcrumb = 'Profile';
        $this->loadView('me/profileView', $this->viewDataBag);
    }

    function edit_profile()
    {
        $this->load->model('userModel');
        if ($this->input->method() === 'post'):
            $this->form_validation->set_rules('id', 'id', 'trim|required|xss_clean');
            $this->form_validation->set_rules('firstName', 'first name', 'trim|required|min_length[2]|max_length[40]|xss_clean|_isHumanName');
            $this->form_validation->set_rules('lastName', 'last name', 'trim|required|min_length[2]|max_length[40]|xss_clean|_isHumanName');
            $this->form_validation->set_rules('email', 'email', 'trim|valid_email|max_length[100]|_isUniqueExceptItself[tb_user.email|id.id]|xss_clean');
            $this->form_validation->set_rules('dateOfBirth', 'date of birth', 'trim|xss_clean');
            $this->form_validation->set_rules('gender', 'gender', 'trim|xss_clean');
            $this->form_validation->set_rules('profileImage', 'profileImage', 'file_allowed_type[image]|file_size_max[2048000]|xss_clean');
            if ($this->form_validation->run() === true) {
                /* #start file uploading */
                if (($_FILES['profileImage']['name'] && $_FILES['profileImage']['type']) != NULL):
                    $featureImage = NULL;
                    $curlPostData = [
                        'image' => makeCurlFile('profileImage'),
                        "folder" => "user/profile",
                        "resize" => json_encode([[200, 200]]),
                        "unlink" => json_encode($this->viewDataBag->userSession->profileImage),
                    ];
                    $this->load->library('curl');
                    $uploadedData = (array)$this->curl->post(IMAGE_BASE_URL . 'v1/image/uploadAndResize', $curlPostData);
                    if ($uploadedData['status'] === true):
                        $profileImage = $uploadedData['filePath'][0];
                    else:
                        $this->alert->danger($uploadedData['error']);
                        return redirectToCurrent();
                    endif;
                /* #end file uploading */
                else:
                    $profileImage = $this->viewDataBag->userSession->profileImage;
                endif;
                /* #end file uploading */
                /* name section */
                $name = ["firstName" => ucwords($this->input->post('firstName')),
                    "lastName" => ucwords($this->input->post('lastName')),];
                $name["displayName"] = $name["firstName"] . " " . $name["lastName"];
                $name["urlName"] = strtolower($name["firstName"]) . "-" . strtolower($name["lastName"]);
                /* input grab section */

                $updatedData = [
                    "name" => json_encode($name),
                    "email" => $this->input->post("email"),
                    "dateOfBirth" => ($this->input->post("dateOfBirth") ? dbDate($this->input->post("dateOfBirth")) : NULL),
                    "gender" => ($this->input->post("gender")) ? $this->input->post("gender") : NULL,
                    'profileImage' => $profileImage,
                ];

                $this->userModel->modify($updatedData, ['id' => $this->viewDataBag->userSession->id]);
                $this->viewDataBag->userSession->name = (object)json_decode($updatedData['name']);
                $this->viewDataBag->userSession->email = $updatedData['email'];
                $this->viewDataBag->userSession->gender = $updatedData['gender'];
                $this->viewDataBag->userSession->dateOfBirth = $updatedData['dateOfBirth'];
                $this->viewDataBag->userSession->profileImage = $updatedData['profileImage'];
                $this->auth->reset($this->viewDataBag->userSession);
                $this->alert->success("Profile has been updated successfully.");
                unset($updatedData);
                return redirectToItSelf('edit-profile/');
            }
        endif;
        $this->viewDataBag->jsView = 'me/jsView';
        $this->loadView('me/editView', $this->viewDataBag);
    }

    function change_password()
    {
        $this->load->model('userModel');
        if ($this->input->method() === 'post'):
            $this->form_validation->set_rules('id', 'id', 'trim|required|xss_clean');
            $this->form_validation->set_rules('newPassword', 'new password', 'required|_isPassword|differs[currentPassword]|xss_clean');
            $this->form_validation->set_rules('confirmNewPassword', 'confirm new password', 'required|_isPassword|matches[newPassword]|xss_clean');
            if ($this->form_validation->run() === true) {
                $updatedData = [
                    'password' => $this->input->makePasswordHash('newPassword'),
                ];
                $this->userModel->modify($updatedData, ['id' => $this->viewDataBag->userSession->id]);
                $this->alert->success("Psssword has been updated successfully.");
                unset($updatedData);
                return redirectToItSelf('change-password/');
            }
        endif;
        $this->viewDataBag->jsView = 'me/jsView';
        $this->loadView('me/changePasswordView', $this->viewDataBag);
    }
}
