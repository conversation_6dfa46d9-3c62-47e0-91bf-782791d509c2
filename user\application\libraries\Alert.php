<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Alert {

    private $ci;
    private $smileyDanger = '&#x1F62D;';
    private $smileySuccess = '&#x1F60A;';
    private $smileyInfo = '&#x1F60F;';
    private $smileyWarning = '&#x1F914;';
    private $headerDanger = 'Oh snap !';
    private $headerSuccess = 'Well done !';
    private $headerInfo = 'Heads up !';
    private $headerWarning = 'Warning !';

    function __construct() {
        $this->ci = & get_instance();
    }

    private function _alertIcon($alertType) {
        return ($alertType == 'danger') ? "<i class='fa fa-thumbs-down alert-text-white' aria-hidden='true'></i>" :
                (($alertType == 'success') ? "<i class='fa fa-thumbs-up alert-text-white' aria-hidden='true'></i>" :
                        (($alertType == 'info') ? "<i class='fa fa-info-circle alert-text-white' aria-hidden='true'></i>" :
                                (($alertType == 'warning') ? "<i class='fa fa-exclamation-triangle alert-text-white' aria-hidden='true'></i>" : '')));
    }

    private function _alertEmoji($alertType) {
        return ($alertType == 'danger') ? $this->smileyDanger :
                (($alertType == 'success') ? $this->smileySuccess :
                        (($alertType == 'info') ? $this->smileyInfo :
                                (($alertType == 'warning') ? $this->smileyWarning : '')));
    }

    private function _alertHeader($alertType) {
        return ($alertType == 'danger') ? $this->headerDanger :
                (($alertType == 'success') ? $this->headerSuccess :
                        (($alertType == 'info') ? $this->headerInfo :
                                (($alertType == 'warning') ? $this->headerWarning : '')));
    }

    private function _set($alertType, $message, $link, $linkText) {
        $link = ($link != '') ? "<a href='" . $link . "'>" . (($linkText != '') ? $linkText : 'Click Here') . "</a>" : '';
        $alertMessage = "<script>swal({  title: \"" . $this->_alertIcon($alertType) . " <strong class='alert-text-white'>  " . $this->_alertHeader($alertType) . "</strong>\",
html:\"<hr class='message-inner-separator'><p class='alert-text-white'>" . $message . "&nbsp;" . $this->_alertEmoji($alertType) . "&nbsp;" . $this->_alertEmoji($alertType) . "</p>" . $link . "\",
 customClass: \"alert alert-" . $alertType . "\",
      animation: true,
});</script>";

        $this->ci->session->set_flashdata('alertMessage', $alertMessage);
    }

    function danger($message, $link = '', $linkText = '') {
        return $this->_set('danger', $message, $link, $linkText);
    }

    function success($message, $link = '', $linkText = '') {
        return $this->_set('success', $message, $link, $linkText);
    }

    function info($message = '', $link = '', $linkText = '') {
        return $this->_set('info', $message, $link, $linkText);
    }

    function warning($message = '', $link = '', $linkText = '') {
        return $this->_set('warning', $message);
    }

    function display($openingTag = '', $closingTag = '') {
        if ($this->ci->session->flashdata('alertMessage')) {

            echo $openingTag . $this->ci->session->flashdata('alertMessage') . $closingTag;
        }
    }

}

?>