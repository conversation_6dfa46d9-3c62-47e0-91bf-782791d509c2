<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class BankDetail extends SecureController {

    function __construct() {
		parent::__construct();
		$this->load->model('UserModuleModel');
		$user_id = $this->viewDataBag->userSession->id;
		$res = $this->UserModuleModel->check('ip_grabber',$user_id);
		
        if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
    }

    public function all($offset = 0){
        $response = [];
        if ($this->input->method() === 'post'){
            $this->form_validation->set_rules('ifsc', 'ifsc', 'trim|required|xss_clean|min_length[5]|max_length[25]');
            if ($this->form_validation->run() === true) {
                $ifsc = $this->input->post("ifsc");
                $curlPost = "ABHY0065002=AA-00test";
                $authToken = $this->viewDataBag->userSession->auth_token;
                $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetBankDetailsByIFSCCode?IFSCCode='.$ifsc);
                curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
                curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
                $headers = array();
                $headers[] = 'Authorization: Bearer '.$authToken;
                $headers[] = 'Cache-Control: no-cache';
                curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($cURLConnection);
                if (curl_errno($cURLConnection)) {
                    echo 'Error:' . curl_error($cURLConnection);
                }
                curl_close($cURLConnection);
                $bankDetails = json_decode($result);
				$response = $bankDetails->Data;
            }
        }
        $this->viewDataBag->response = $response;
        $this->loadView('bankDetail/listView', $this->viewDataBag);
    }

}