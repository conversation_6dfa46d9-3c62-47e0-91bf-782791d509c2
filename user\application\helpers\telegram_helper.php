<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');


if (!function_exists('companyDetails')) {
    function companyDetails($imeiNumber, $authToken) {
		
        $ci = & get_instance();
		
 		$curlPost = json_encode(["IMEI"=>$imeiNumber]);
		
        $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCompanyName');
        curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Cache-Control: no-cache';
		$headers[] = 'Content-Type: application/json';
        curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($cURLConnection);
		
        if (curl_errno($cURLConnection)) {
            echo 'Error:' . curl_error($cURLConnection);
        }
		
        curl_close($cURLConnection);
        $companyData = json_decode($result);
        return $companyData;
    }
}

if(!function_exists('generateOsintPdfHtml')) {
    function generateOsintPdfHtml($records, $input)
    {
        $html = "<html><head><style>
            body { font-family: Arial, sans-serif; font-size: 13px; }
            h2 { color: #2E86C1; }
            h3 { background-color: #f2f2f2; padding: 5px; }
            ul { padding-left: 20px; }
            li { margin-bottom: 4px; }
            .leak-section { margin-bottom: 10px; }
            .info-leak { font-style: italic; color: #555; margin-top: 5px; }
            </style></head><body>";

        $html .= "<h2>LIS OSINT Report : $input</h2>";

        $html .= '<p>"Leaders in Cyber Crime Cases, Investigation, Seminars and Security Auditing !"</p>';

        $html .= "<h3>Report Detais</h3>";

        $html .= "<p><strong>Generate By:</strong> CCAS</p>";

        $html .= "<p><strong>Date/Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

        $alternateNames = [];
        $alternateEmails = [];

        foreach($records as $appName => $allRecord) {

            if($appName === 'bulk' && isset($allRecord['status']) && $allRecord['status'] === 'success') {
                $bulk = $allRecord['data'];
                foreach ($bulk as $record) {

                    $status = strtolower($record['Status']);

                    if (strpos($status, 'not exist') !== false || strpos($status, 'unauthorized') !== false) continue;

                    // Decode PayloadData
                    $payload = $record['PayloadData'];
                    if (!empty($payload) && is_string($payload)) {
                        $payload = json_decode($payload, true);
                    }

                    // 🧱 Special formatting for LeakData
                    if ($record['APIName'] === 'LeakData API' && isset($payload['List'])) {

                        $html .= "<h3>LeakData</h3>";

                        foreach ($payload['List'] as $source => $sourceData) {

                            $html .= "<div class='leak-section'><h4>$source</h4><ul>";

                            foreach ($sourceData['Data'] as $dataItem) {
                                foreach ($dataItem as $k => $v) {
                                    $v = is_array($v) ? json_encode($v) : $v;
                                    
                                    if (stripos($k, 'name') !== false) {
                                        $alternateNames[] = $v;
                                    }
                                    
                                    if (stripos($k, 'email') !== false) {
                                        $alternateEmails[] = $v;
                                    }
                                    
                                    $html .= "<li><strong>$k:</strong> $v</li>";
                                }
                            }

                            $html .= "</ul>";

                            /* if (!empty($sourceData['InfoLeak'])) {
                                $html .= "<p class='info-leak'>{$sourceData['InfoLeak']}</p>";
                            } */

                            $html .= "</div>";
                        }
                    }

                    if ($record['APIName'] === 'Quora Search') {
                        $payload = $record['PayloadData'];
                        if (is_string($payload)) {
                            $payload = json_decode($payload, true);
                        }

                        if (!empty($payload['data']['searchQuery'])) {
                            foreach ($payload['data']['searchQuery'] as $qresult) {
                                $contact = $qresult['contact'];
                                $user = $contact['contactUser'];

                                $email = $contact['email'] ? $contact['email'] : 'N/A';
                                $id = $contact['id'] ? $contact['id'] : 'N/A';
                                $uid = $user['uid'] ? $user['uid'] : 'N/A';
                                $url = $user['profileUrl'] ? $user['profileUrl'] : 'N/A';
                                $image = $user['profileImageUrl'] ? $user['profileImageUrl'] : 'N/A';
                                $fullName = 'N/A';

                                if (!empty($user['names']) && is_array($user['names'][0])) {
                                    $first = $user['names'][0]['givenName'] ? $user['names'][0]['givenName'] : '';
                                    $last = $user['names'][0]['familyName'] ? $user['names'][0]['familyName'] : '';
                                    $fullName = trim($first . ' ' . $last);
                                }

                                $alternateNames[] = $fullName;
                                $alternateEmails[] = $email;

                                $html .= "<h3>Quora</h3><ul>";
                                $html .= "<li><strong>ID:</strong> $id</li>";
                                $html .= "<li><strong>UID:</strong> $uid</li>";
                                $html .= "<li><strong>Full Name:</strong> $fullName</li>";
                                $html .= "<li><strong>Email:</strong> $email</li>";
                                $html .= "<li><strong>Profile URL:</strong> <a href=\"$url\">$url</a></li>";
                                
                                if ($image) {
                                    $html .= "<li><strong>Profile Image:</strong><br><img src=\"$image\" width=\"100\" height=\"100\" style=\"margin-top:5px;border:1px solid #ccc;\" /></li>";

                                }
                                $html .= "</ul>";
                            }
                        }
                    }

                    if ($record['APIName'] === 'GoogleAPI') {
                        $data = $record['Data'] ? $record['Data'] : '';

                        if (!empty($data)) {
                            $html .= "<h3>Google API</h3><ul>";

                            // If || separated format
                            if (strpos($data, '||') !== false) {
                                $parts = explode('||', $data);
                                $info = [];

                                foreach ($parts as $part) {
                                    $pair = explode(':', $part, 2);
                                    $key = isset($pair[0]) ? trim($pair[0]) : '';
                                    $val = isset($pair[1]) ? trim($pair[1]) : '';
                                    $info[$key] = $val;
                                }

                                $id = $info['ID'] ? $info['ID'] : 'N/A';
                                $name = $info['Name'] ? $info['Name'] : 'N/A';
                                $url = $info['URL'] ? $info['URL'] : '#';
                                $photo = $info['Photo'] ? $info['Photo'] : '';

                            } else {
                                // Fallback for : separated lines
                                preg_match('/ID\s*:\s*(\d+)/', $data, $idMatch);
                                preg_match('/Full Name\s*:\s*(.+)/', $data, $nameMatch);
                                preg_match('/URL\s*:\s*(https:\\/\\/[^\\s]+)/', $data, $urlMatch);

                                $id = $idMatch[1] ? $idMatch[1] : 'N/A';
                                $name = $nameMatch[1] ? $nameMatch[1] : 'N/A';
                                $url = $urlMatch[1] ? $urlMatch[1] : '#';
                                $photo = ''; // No photo in fallback
                            }

                            $alternateNames[] = $name;

                            $html .= "<li><strong>ID:</strong> $id</li>";
                            $html .= "<li><strong>Full Name:</strong> $name</li>";
                            $html .= "<li><strong>URL:</strong> <a href=\"$url\">$url</a></li>";

                            if (!empty($photo)) {
                                $html .= "<li><strong>Photo:</strong><br><img src=\"$photo\" width=\"100\" height=\"100\" style=\"margin-top:5px;border:1px solid #ccc;\" /></li>";
                            }

                            $html .= "</ul>";
                        }
                    }

                    if ($record['APIName'] === 'Skype') {
                        $payload = $record['PayloadData'];

                        if (is_string($payload)) {
                            $payload = json_decode($payload, true);
                        }

                        $results = $payload['results'] ? $payload['results'] : [];

                        foreach ($results as $res) {
                            $profile = $res['nodeProfileData'] ? $res['nodeProfileData'] : [];

                            $id = $profile['skypeId'] ? $profile['skypeId'] : 'N/A';
                            $name = $profile['name'] ? $profile['name'] : 'N/A';
                            $city = $profile['city'] ? $profile['city'] : 'N/A';
                            $state = $profile['state'] ? $profile['state'] : 'N/A';
                            $country = $profile['country'] ? $profile['country'] : 'N/A';
                            $avatar = $profile['avatarUrl'] ? $profile['avatarUrl'] : '';                    

                            $alternateNames[] = $name;

                            $html .= "<h3>Skype</h3><ul>";
                            $html .= "<li><strong>Profile Id:</strong> $id</li>";
                            $html .= "<li><strong>Full Name:</strong> $name</li>";
                            $html .= "<li><strong>Country:</strong> $country</li>";
                            $html .= "<li><strong>City:</strong> $city</li>";
                            $html .= "<li><strong>State:</strong> $state</li>";
                            
                            if ($avatar) {
                                $html .= "<li><strong>Profile Image:</strong><br><img src=\"$avatar\" width=\"100\" height=\"100\" style=\"margin-top:5px;border:1px solid #ccc;\" /></li>";

                            }
                            $html .= "</ul>";
                        }
                    }

                    if($record['APIName'] === 'MNP') {
                        $data = $record['Data'] ? json_decode($record['Data'], true) : [];

                        if (!empty($data)) {
                            $operator = isset($data['operator']) ? $data['operator'] : 'N/A';
                            $circle = isset($data['circle']) ? $data['circle'] : 'N/A';

                            $html .= "<h3>MNP</h3><ul>";
                            $html .= "<li><strong>Operator:</strong> $operator</li>";
                            $html .= "<li><strong>Circle:</strong> $circle</li>";
                            $html .= "</ul>";
                        }
                    }

                    if($record['APIName'] === 'Truecaller') {
                        $payload = $record['PayloadData'];

                        if (is_string($payload)) {
                            $payload = json_decode($payload, true);
                        }

                        if (isset($payload['status']) && $payload['status'] === 'Unauthorized') {
                            $html .= "<h3>Truecaller</h3><p>Unauthorized access. Please check your API key.</p>";
                            continue;
                        }

                        $tcData = $payload[0] ? $payload[0] : [];
                        
                        $name = $tcData['name'] ? $tcData['name'] : 'N/A';
                        $image = $tcData['image'] ? $tcData['image'] : '';
                        $gender = $tcData['gender'] ? $tcData['gender'] : 'N/A';
                        $altName = $tcData['altName'] ? $tcData['altName'] : 'N/A';
                        $about = $tcData['about'] ? $tcData['about'] : 'N/A';
                        $email = $tcData['internetAddresses'][0]['id'] ? $tcData['internetAddresses'][0]['id'] : 'N/A';
                        $address = $tcData['addresses'][0]['address'] ? $tcData['addresses'][0]['address'] : 'N/A';
                        $region = $tcData['addresses'][0]['city'] ? $tcData['addresses'][0]['city'] : 'N/A';                
                        $carrier = $tcData['phones'][0]['carrier'] ? $tcData['phones'][0]['carrier'] : 'N/A';
                        $nationalFormat = $tcData['phones'][0]['nationalFormat'] ? $tcData['phones'][0]['nationalFormat'] : 'N/A';
                        $spamDescription = isset($tcData['spam_description']) ? $tcData['spam_description'] : [];
                        if (is_array($spamDescription) && !empty($spamDescription)) {
                            $spamDescription = implode(', ', $spamDescription);
                        } else {
                            $spamDescription = 'No';
                        }

                        $alternateNames[] = $name;
                        if ($email !== 'N/A') $alternateEmails[] = $email;

                        $html .= "<h3>Truecaller</h3><ul>";
                        $html .= "<li><strong>Full Name:</strong> $name</li>";
                        $html .= "<li><strong>Phone:</strong> $nationalFormat</li>";
                        $html .= "<li><strong>Gender:</strong> $gender</li>";
                        $html .= "<li><strong>Alternate Name:</strong> $altName</li>";
                        $html .= "<li><strong>About:</strong> $about</li>";
                        $html .= "<li><strong>Email:</strong> $email</li>";
                        $html .= "<li><strong>State:</strong> $region</li>";
                        $html .= "<li><strong>Country:</strong> $address</li>";
                        $html .= "<li><strong>Carrier:</strong> $carrier</li>";
                        $html .= "<li><strong>Spam Description:</strong> $spamDescription</li>";

                        if (!empty($image)) {
                            $html .= "<li><strong>Profile Image:</strong><br><img src=\"$image\" width=\"100\" height=\"100\" style=\"margin-top:5px;border:1px solid #ccc;\" /></li>";
                        }

                        $html .= "</ul>";
                    }

                    else {

                        if(in_array($record['APIName'], ['GoogleAPI', 'Quora Search', 'LeakData API', 'Skype', 'MNP', 'Truecaller'])) {
                            continue; // Skip these as they are handled above
                        }

                        $html .= "<h3>{$record['APIName']}</h3>";
                        $html .= "<p>Found</p>";

                        // Raw Data (string)
                        if (!empty($record['Data']) && is_string($record['Data'])) {
                            $html .= "<p><strong>Data:</strong> {$record['Data']}</p>";
                        }

                        if (!empty($payload) && is_array($payload)) {
                            $html .= "<ul>";
                            foreach ($payload as $k => $v) {
                                if (is_array($v)) {
                                    foreach ($v as $kk => $vv) {
                                        $vv = is_array($vv) ? json_encode($vv) : $vv;
                                        $html .= "<li><strong>$kk:</strong> $vv</li>";
                                    }
                                } else {
                                    $html .= "<li><strong>$k:</strong> $v</li>";
                                }
                            }
                            $html .= "</ul>";
                        }
                    }
                    

                }
            }

            if($appName === 'fetch_whatsapp' && $allRecord['status'] === 'success') {
                $whatsappData = $allRecord['fetch_whatsapp']['data'][0]['PayloadData'][0] ? $allRecord['fetch_whatsapp']['data'][0]['PayloadData'][0] : [];
                if (!empty($whatsappData)) {
                    $html .= "<h3>WhatsApp</h3>";
                    /* $html .= "<p>Status: {$allRecord['fetch_whatsapp']['data'][0]['Status']}</p>"; */
                    $html .= "<p>Details: {$whatsappData['details']}</p>";
                    $html .= "<p>Name: {$whatsappData['name']}</p>";
                    $html .= "<p>Mobile: {$whatsappData['mobile']}</p>";
                }
            }

            if($appName === 'fetch_telegram' && $allRecord['status'] === 'success') {
                $telegramData = $allRecord['fetch_telegram']['data'][0]['PayloadData'][0] ? $allRecord['fetch_telegram']['data'][0]['PayloadData'][0] : [];
                if (!empty($telegramData)) {
                    $html .= "<h3>Telegram</h3>";
                    /* $html .= "<p>Status: {$allRecord['fetch_telegram']['data'][0]['Status']}</p>"; */
                    $html .= "<p>Name: {$telegramData['name']}</p>";
                    $html .= "<p>Mobile: {$telegramData['mobile']}</p>";
                }
            }

        }

        $html .= "<h3>Alternate Name</h3><ul>";

        foreach($alternateNames as $name) {
            $html .= "<li>$name</li>";
        }
        
        $html .= "</ul>";

        $html .= "<h3>Email Address</h3><ul>";

        foreach($alternateEmails as $email) {
            $html .= "<li>$email</li>";
        }
        
        $html .= "</ul>";

        $html .= "<hr><p><em>Disclaimer: The content provided in this OSINT-generated report is specifically designed to assist law enforcement
                agencies in their investigative efforts. This report is not admissible as legal evidence in court and
                should not be treated as definitive proof. Its primary purpose is to support investigations by offering
                actionable insights derived from open-source intelligence.
                </em></p>";

        $html .= "<p><em>For any queries or feedback, please reach out to +91-9461101915 </em></p>";

        $html .= "</body></html>";

        return $html;
    }
}

    function testData() {

        $data = [
            // Entry for 2025-05-30 23:24:51
            [
                "bulk" => [
                    "status" => "success",
                    "data" => [
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Byjus",
                            "Status" => "Mobile number is exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Flipkart",
                            "Status" => "Mobile number is not exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Indane gas",
                            "Status" => "Mobile number is not exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Swiggy",
                            "Status" => "Mobile number is not exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Truecaller",
                            "Status" => "Mobile Number is exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => [
                                [
                                    "status" => 1,
                                    "mobile" => "9610505499",
                                    "name" => "Mohit Jajani",
                                    "gender" => "UNKNOWN",
                                    "image" => "https://images-noneu.truecallerstatic.com/myview/1/61463ac9520be5ed40561b741c329a1c/1",
                                    "altName" => "",
                                    "about" => "",
                                    "phones" => [
                                        [
                                            "e164Format" => "+************",
                                            "numberType" => "MOBILE",
                                            "nationalFormat" => "096105 05499",
                                            "dialingCode" => "91",
                                            "countryCode" => "IN",
                                            "carrier" => "Vi",
                                            "type" => "openPhone",
                                        ]
                                    ],
                                    "addresses" => [
                                        [
                                            "city" => "Rajasthan",
                                            "countryCode" => "IN",
                                            "timeZone" => "+05:30",
                                            "type" => "address",
                                            "address" => "IN",
                                        ]
                                    ],
                                    "internetAddresses" => [
                                        [
                                            "id" => "<EMAIL>",
                                            "service" => "email",
                                            "caption" => "Mohit Jajani",
                                            "type" => "internetAddress",
                                        ]
                                    ],
                                    "spam_description" => [],
                                ]
                            ],
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Patanjali",
                            "Status" => "Mobile Number is exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Skype",
                            "Status" => "Mobile Number is not exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => [
                                "requestId" => "Query20",
                                "results" => []
                            ],
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "MNP",
                            "Status" => "Mobile Number is exist",
                            "Data" => '{"operator":"Vi","circle":"Rajasthan"}',
                            "image" => "",
                            "PayloadData" => "",
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "LeakData API",
                            "Status" => "Mobile Number is exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => '{"List":{"BigBasket":{"Data":[{"Address":"c-74","Address2":"gopalpura mod","Amount":"1016.50","CountOrders":"1","Credits":"0.00","Email":"<EMAIL>","FirstName":"Mohit","IP":"**************","Landmark":"pooja tower","LastLogin":"2018-06-25 23:51:27","LastName":"Jajani","Password(SHA1)":"sha1$23586$7948aafc351d140a7c02f93a1038a9ae1d972b9d","Phone":"************","PinCode":"2880","PostCode":"302018","RegDate":"2018-06-24 18:44:14","Region":"8586"}],"InfoLeak":"In October 2020, Indian grocery platform bigbasket suffered a data breach. More than 20 million customer records were exposed. The data was initially sold and became public the following April. The data included email, IP, physical addresses, names, phone numbers, dates of birth, and passwords as Django hashes (SHA-1).","NumOfResults":1},"IndiaMART":{"Data":[{"City":"null","Country":"India","Phone":"************"}],"InfoLeak":"In August 2021, 38 million entries from Indian e-commerce site IndiaMART were discovered on a popular hacking forum. The data included more than 20 million unique emails, as well as names, phone numbers and physical addresses. It is unclear whether IndiaMART intentionally disclosed data attributes or whether the data was obtained by exploiting a vulnerability.","NumOfResults":1},"TrueCaller India":{"Data":[{"Address":"Rajasthan in","Email":"<EMAIL>","FullName":"Mohit Jajani","IndianState":"Rajesthan","MobileOperator":"Vodafone India","Phone":"************"}],"InfoLeak":"In February 2019, the \"world\'s best caller ID and spam blocking app\" Truecaller suffered a data breach affecting 286 million Indian users. Phone numbers, phone operators, full names, genders, locations, job titles, company names, emails, websites, Facebook handles, and Twitter usernames were revealed.","NumOfResults":1}},"NumOfDatabase":3,"NumOfResults":3,"free_requests_left":79,"price":0,"search time":0.0}'
                        ],
                        [
                            "EnterdData" => "9610505499",
                            "APIName" => "Amazon",
                            "Status" => "Mobile Number is not exist",
                            "Data" => "",
                            "image" => "",
                            "PayloadData" => "",
                        ]
                    ]
                ],
                "fetch_whatsapp" => [
                    "status" => "success",
                    "data" => [
                        [
                            "Status" => "is exist",
                            "PayloadData" => [
                                [
                                    "status" => true,
                                    "url" => "",
                                    "details" => "Hey there! I am using WhatsApp.",
                                    "statusData" => "Registered on WhatsApp",
                                    "name" => "No name available",
                                    "mobile" => "9610505499"
                                ]
                            ],
                            "Data" => "",
                            "APIName" => "Whatsapp"
                        ]
                    ]
                ],
                "fetch_telegram" => [
                    "status" => "success",
                    "data" => [
                        [
                            "Status" => "is exist",
                            "PayloadData" => [
                                [
                                    "status" => true,
                                    "name" => "Not found",
                                    "mobile" => "+************"
                                ]
                            ],
                            "Data" => "",
                            "APIName" => "Telegram"
                        ]
                    ]
                ]
            ]
        ];

        return $data;
    }

if(!function_exists('saveUser')) {
    function saveUser($name, $mobile, $telegram_id) {
		
        $ci = & get_instance();

        $ci->load->model('TelegramModel');
        $ci->load->library('TelegramBot');

        $user = $ci->TelegramModel->findOne(['telegramId' => $telegram_id]);

        if (!empty((array)$user)) {
            return true;
        }

        $data = [
            'name' => $name,
            'mobile' => $mobile,
            'telegramId' => $telegram_id,
            'status' => 'active',
            'accountType' => 'fresh',
        ];

        $ci->TelegramModel->attach($data);
    }
}