<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Session extends CI_Session {

    function __construct() {
        parent::__construct();
    }

    function push($sessionDataArrayOrObject, $sessionName = '') {
        $sessionDataArray = (is_object($sessionDataArrayOrObject)) ? (array) $sessionDataArrayOrObject : $sessionDataArrayOrObject;
        if (isArray($sessionDataArray)) {
            foreach ($sessionDataArray as $key => $value) {
                $this->set_userdata($key, $value);
            }
        }
        $this->set_userdata($sessionName, $sessionDataArray);
        return true;
    }

    function get($sessionName) {
        return $this->has_userdata($sessionName) ? (is_array($this->userdata($sessionName)) ? (object) $this->userdata($sessionName) : $this->userdata($sessionName)) : false;
    }

    function has($sessionName) {
        return $this->has_userdata($sessionName) ? true : false;
    }

    function put($sessionDataArrayOrObject, $sessionName) {
        $this->push($sessionDataArrayOrObject, $sessionName);
        return true;
    }

    function flush($sessionName) {
        $this->unset_userdata($sessionName);
        return true;
    }

    function flushAll() {
        $sessArray = $this->all_userdata();
        foreach ($sessArray as $key => $val) {
            if ($key != 'session_id' && $key != 'last_activity' && $key != 'ip_address' && $key != 'user_agent' && $key != 'RESERVER_KEY_HERE')
                $this->unset_userdata($key);
        }
        unset($sessArray);
        return true;
    }

}
