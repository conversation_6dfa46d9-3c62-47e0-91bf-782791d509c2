<?php

class Truecaller
{

    private $ci;
    private $responseError;

    function __construct()
    {
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
    }

    function truecallerAuthToken($uname,$pwd) {
        $ch = curl_init();
        $param = array(
            'UserName' => $uname,
            'Password' => $pwd
        );
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result);
        $token = (array_key_exists('token',$result)) ? $result->token : '';
        return $token;
    }

    function account($mobile){
        $ch = curl_init();
        $param = array(
            'Moblie' => $mobile,
        );
        $pwd = $this->ci->db->select('value')->get_where('tb_setting', ['name'=>'truecaller_token_password'])->row()->value;
        $authToken = $this->truecallerAuthToken('**********',$pwd);
		if($authToken=='') {
			return array("status" => false,"msg" => 'Unauthorized');
		}
		
        $param = json_encode($param);
        curl_setopt($ch, CURLOPT_URL, 'https://mobileapi.lisw.in/api/Tac/GetTrucallerAPI');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        $headers = array();
        $headers[] = 'Authorization: Bearer '.$authToken;
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
        $truecaller_response = json_decode($result,true);
        $truecaller_data = $truecaller_response['data'];
        $success = $truecaller_response['success'];
        $message = $truecaller_response['message'];
        
        if($success==false ){
            return array("status" => false,"msg" => $message);
        }else{
            $resData[] = json_decode($truecaller_data);
            $returnData =  array(
                "status" => 200,
                "mobile" => $mobile,
                "name" => (property_exists($resData[0], 'name') ? $resData[0]->name : null),
                "gender" => (property_exists($resData[0], 'gender') ? $resData[0]->gender : null),
                "image" => (property_exists($resData[0], 'image') ? $resData[0]->image : null),
                "altName" => (property_exists($resData[0], 'altName') ? $resData[0]->altName : null),
                "about" => (property_exists($resData[0], 'about') ? $resData[0]->about : null),
                "phones" => (property_exists($resData[0], 'phones') ? $resData[0]->phones : []),
                "addresses" => (property_exists($resData[0], 'addresses') ? $resData[0]->addresses : []),
                "internetAddresses" => (property_exists($resData[0], 'internetAddresses') ? $resData[0]->internetAddresses : []),
                "spam_description" => (property_exists($resData[0], 'spamInfo') ? $resData[0]->spamInfo : [])
            );
            return $returnData;
        }
       
    }

}