<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class MY_Pagination extends CI_Pagination {

    var $config = [
        "num_links" => 0,
        "per_page" => 50,
        "total_rows" => 100,
        "first_link" => '&lsaquo;&lsaquo; First',
        "last_link" => 'Last &rsaquo; &rsaquo;',
        "uri_segment" => 3,
        "reuse_query_string" => true,
        "full_tag_open" => '<nav aria-label="Page navigation"> <ul class="pagination">',
        "full_tag_close" => "</ul></nav>",
        "first_tag_open" => '<li class="page-item"><span class="page-link">',
        "first_tag_close" => "</span></li>",
        "last_tag_open" => '<li class="page-item"><span class="page-link">',
        "last_tag_close" => "</span></li>",
        "next_tag_open" => '<li class="page-item"><span class="page-link">',
        "next_tag_close" => "</span></li>",
        'next_diabled_tag_open' => '<li class="page-item disabled"><span class="page-link">',
        'next_diabled_tag_close' => '</span><li>',
        "prev_tag_open" => '<li class="page-item"><span class="page-link">',
        "prev_tag_close" => "</span></li>",
        'prev_diabled_tag_open' => '<li class="page-item disabled"><span class="page-link">',
        'prev_diabled_tag_close' => '</span><li>',
        "num_tag_open" => '<li class="page-item"><span class="page-link">',
        "num_tag_close" => "</span></li>",
        "cur_tag_open" => '<li class="page-item active"><a  class="page-link" href="javascript:void(0)">',
        "cur_tag_close" => '<span class="sr-only">(current)</span></a></li>',
        "next_link" => '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
        "prev_link" => '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
        'first_link' => false,
        'last_link' => false,
        'attributes' => ['class' => "page-link"],
//        'cur_tag_open' => "<li class='hidden'><a href='javascript:void(0)'>",
    ];

    // --------------------------------------------------------------------
    function __construct() {

        parent::__construct($this->config);
    }

    function get($offset, $perPage, $totalRows) {
        $showingUpto = $offset + $perPage;
        $showingUpto = ($totalRows >= $showingUpto) ? $showingUpto : $totalRows;
        $pagination = $this->create_links();
        if (!$pagination) {
            //return '<ul class="pagination"><li class="disabled" disabled="disabled"><a href="javascript:void(0);"><i class="fa fa-chevron-left" aria-hidden="true"></i></a></li><li class="disabled" disabled="disabled"><a href="javascript:void(0);"><i class="fa fa-chevron-right" aria-hidden="true"></i></a></li></ul>';
            return '';
        }
        return "<span class='pagination-showing'>" . ($offset + 1) . " - " . $showingUpto . "<span style='font-weight:300'> of </span>" . $totalRows . "</span> " . $pagination;
    }

}

// END Pagination Class
