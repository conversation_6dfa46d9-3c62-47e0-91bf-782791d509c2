<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class ExcelDataModel extends MY_Model {

    protected $primaryTable = 'tb_excel_data';
    protected $primaryTablePK = 'id';
	protected $primaryTableFK = 'category_id';
	protected $secondaryTable = 'tb_excel_categories';
    protected $secondaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	function getAll($where = '', $select=null, $extra=NULL) {
        $this->db->select('t1.*')
                ->select('t2.title')
                ->from($this->primaryTable . ' t1')
                ->join($this->secondaryTable . ' t2', 't1.' . $this->primaryTableFK . '=t2.' . $this->secondaryTablePK, 'left');
        if ($where != '')
            $this->db->where($where);
        $this->db->order_by('t1.id', 'desc');
        if (!is_null($extra) && isArray($extra)) {
            if (array_key_exists('orderBy', $extra) && isArray($extra['orderBy'])) {
                if (isArrayOfArray($extra['orderBy'])) {
                    foreach ($extra['orderBy'] as $key => $value) {
                        $this->db->order_by($value[0], $value[1]);
                    }
                } else {
                    $this->db->order_by($extra['orderBy'][0], $extra['orderBy'][1]);
                }
            }
            if (array_key_exists('limit', $extra) && isArray($extra['limit'])) {
                $this->db->limit($extra['limit']['perPage'], $extra['limit']['offset']);
            }
        }
        $this->db->group_by('t1.id');
        return $this->getResult($this->db->get());
    }

}
