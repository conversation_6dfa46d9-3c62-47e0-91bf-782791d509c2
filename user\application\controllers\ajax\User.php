<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class User extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
    }
    function requestForDemo()
    {
        $post = $this->input->post();
        $userSession = $this->auth->user();
        $post['userId'] = $userSession->id;
        $this->load->library('curl');
        $returnData = $this->curl->post(apiURL("user/requestForDemo"), $post);
        return $this->exitOnly($returnData);
    }

    function smsCredit()
    {
        $post = $this->input->post();
        $userSession = $this->auth->user();
        $post['userId'] = $userSession->id;
        $this->load->library('curl');
        $returnData = $this->curl->post(apiURL("user/sms_credit"), $post);
        return $this->exitOnly($returnData);
    }
}
