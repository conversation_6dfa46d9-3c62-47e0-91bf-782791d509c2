<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Payment extends SecureController
{
    private $user;
    function __construct()
    {
        parent::__construct();
        // $this->user = $this->auth->user();
    }
    function response()
    {
        $this->load->library('paytm');
        $responseData = $this->paytm->getPaymentResponse();
        if (isArray($responseData)) {
            if (!array_key_exists("RESPCODE", $responseData)) {
                return redirectTo('me/dashboard/');
            }
            $paymentStatus = "failed";
            if ($responseData["RESPCODE"] == 1) {
                $paymentStatus = "success";
            } else if ($responseData["RESPCODE"] == 400 || $responseData["RESPCODE"] == 402) {
                $paymentStatus = "pending";
            }
            $userId = $this->viewDataBag->userSession->id;
            $inserData = [
                'paymentId' => $responseData['TXNID'],
                'orderNumber' => $responseData['ORDERID'],
                'paymentCreatedOn' => currentDateTime(),
                'paymentIsStatus' => $paymentStatus,
                'paymentAmount' => $responseData['TXNAMOUNT'],
                'responseMessage' => $responseData['RESPMSG'],
                'paymentGateway' => 'paytm',
                'userId' => $userId,
            ];
            $this->load->model('paymentDetailsModel');
            $this->paymentDetailsModel->attach($inserData);
            if ($paymentStatus == "success") {
                $this->load->model('userModel');
                $this->userModel->modify(['registrationType' => 'paid', 'paymentMode' => 'online', 'registrationRequest' => 'new'], ['id' => $userId]);
                return redirectTo('me/dashboard/');
            } else {
                $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
                $this->viewDataBag->responseData = (object)$responseData;
                $this->loadView('payment/failedView', $this->viewDataBag);
            }
        } else {
            show_404();
        }
    }
    function init()
    {
        $userId = $this->viewDataBag->userSession->id;
        $this->load->model('paymentModel');
        $paybleAmount = $this->paymentModel->findOne([], '*');
        $this->load->model('paymentDetailsModel');
        if ($this->paymentDetailsModel->has(['userId' => $userId, 'paymentIsStatus' => 'success'])) {
            $amount = $paybleAmount->renewPrice;
        } else {
            $amount = $paybleAmount->proVersionPrice;
        }
        if ($userId) {
            $this->load->library('paytm');
            $this->paytm->makePayment((object)[
                'amount' => $amount,
                'orderId' => 'MAOD' . time() . random_string('numeric', 10),
                'customerId' => $userId,
            ]);
        } else {
            show_404();
        }
    }
    function action()
    {
        dd($_REQUEST);
    }
    function verify(){
        header("Pragma: no-cache");
        header("Cache-Control: no-cache");
        header("Expires: 0");

       // following files need to be included
       // require_once("./lib/config_paytm.php");
       // require_once("./lib/encdec_paytm.php");

        require_once( 'third_party/paytmLib/encdec_paytm.php');

        $ORDER_ID = "";
        $requestParamList = array();
        $responseParamList = array();

        $requestParamList = array("MID" => "XXXXX" , "ORDERID" => "xxxxx");  //Put MID

        $checkSum = getChecksumFromArray($requestParamList,"XXXXX");          // Put Merchant key
        $requestParamList['CHECKSUMHASH'] = urlencode($checkSum);

        $data_string = "JsonData=".json_encode($requestParamList);
        echo $data_string;


        $ch = curl_init();  // initiate curl
        //$url = PAYTM_STATUS_QUERY_URL; //Live server where you want to post data
        $url = "https://securegw-stage.paytm.in/merchant-status/getTxnStatus?"; //staging server
       //$url = "https://securegw.paytm.in/merchant-status/getTxnStatus?"; // Live server

        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_POST, true);  // tell curl you want to post something
        curl_setopt($ch, CURLOPT_POSTFIELDS,$data_string); // define what you want to post
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // return the output in string format
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $output = curl_exec($ch); // execute
        $info = curl_getinfo($ch);

        //echo "kkk".$output;
        $data = json_decode($output, true);
        echo "<pre>";
        print_r($data);
        echo "</pre>";
    }
}
