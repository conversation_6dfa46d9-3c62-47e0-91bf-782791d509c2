<?php

defined('BASEPATH') OR exit('No direct script access allowed');
set_time_limit(0);
class Whatsap extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$user_id = $this->viewDataBag->userSession->id;
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}

    }

    function search(){
		$emailData = [];
		$this->viewDataBag->mobiles = $emailData;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'whatsap/jsView';
        $this->loadView('whatsap/searchView', $this->viewDataBag);
    }
	function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $user_id = $this->viewDataBag->userSession->id;
       /*$where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.mobile like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }*/
		
		#new  changes multi mobile
		$post = [
			"from"=> $from,
			"to"=> $to,
			"id"=>$user_id,
			"offset"=>$offset,
			"searchItem"=>$searchItem
		];
		$param = json_encode($post);
		log_message('error', 'whatsapHistory$post'.$param);
		$authToken = $this->viewDataBag->userSession->auth_token;
		$curl = curl_init();
		$url = "https://eitem.in/vehicle/history-whatsap.php";					

		curl_setopt_array($curl, array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_POSTFIELDS =>$param,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
			),
		));
		$response = curl_exec($curl);
		log_message('error', 'whatsapHistory$response'.$response);
		$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

		//$responses = json_decode($response);
		curl_close($curl);
		
		$responses = json_decode($response);			

		if(isset($responses->status) && $responses->status!=''){
			$config['total_rows'] 	= $totalRows = $responses->totalRows;
			$config['per_page'] 	= $perPage = $responses->perPage;
			$this->viewDataBag->users = $responses->data;
		}else{
			$config['total_rows'] 	= $totalRows = 0;
			$config['per_page'] 	= $perPage = 0;
			$this->viewDataBag->users = [];
		}
        
        
        //$perPage = 50;
        //$this->load->model('whatsapModel');
        //$totalRows = $this->whatsapModel->totalCount($where);
        //$offset = ($offset > $totalRows) ? 0 : $offset;
        /*$this->viewDataBag->users = $this->whatsapModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);*/
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
       // $config['per_page'] = $perPage;
       // $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        $this->viewDataBag->jsView = 'whatsap/jsView';
        $this->loadView('whatsap/listView', $this->viewDataBag);
    }
	function detail($slug)
    {
        $id = slugId();
        if ($id) {
            $this->load->model('whatsapModel');
            $post = [				
				"id"=>$id
			];
			$param = json_encode($post);
			$curl = curl_init();
			$url = "https://eitem.in/vehicle/detail-whatsap.php";					

			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS =>$param,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));
			$response = curl_exec($curl);
			log_message('error', 'whatsapDetails$response'.$response);
			$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

			//$responses = json_decode($response);
			curl_close($curl);

			$responses = json_decode($response);
            if(isset($responses->status) && $responses->status!=''){
				$result = $result_vi = $result_id = '';					
								
				$data = [];
				foreach(json_decode($responses->data) as $value){
					// Array to hold extracted data
					$statusDetails = isset($value->statusData) ? $value->statusData : 'N/A';
					$details = isset($value->details) ? $value->details : 'N/A';
					$name = isset($value->name) ? $value->name : 'N/A';
					$url = isset($value->url) ? $value->url : 'N/A';
					$aadhaar = isset($value->mobile) ? $value->mobile : 'N/A';
					$result = "<p></p><h3>Mobile - $aadhaar</h3><p>";		


					// Define regex patterns for each field
					$patterns = [
						'Name' => $name,
						'Details' => $details,
						'Status' => $statusDetails,
						//'Mobile' => $aadhaar,
						'Profile Image' => $url
					];
						
						
					$result = $this->getDetails($patterns,$value->details,$result);
					$result .= "</p>";
					$data[] = $result;
				}
				//$this->viewDataBag->response = $responses->response;
				$this->viewDataBag->pi = $data;
				$this->viewDataBag->res_status = $responses->status;
				$this->viewDataBag->mobiles = $responses->mobile;
                $this->viewDataBag->jsView = 'whatsap/jsView';
                $this->loadView('whatsap/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if($key == 'Profile Image'){
				$pattern = "<img src='".$pattern."' />";
			}
			$result .= "<b>".$key."</b>: ".$pattern."<br>";
		}
		return $result;
	}
}