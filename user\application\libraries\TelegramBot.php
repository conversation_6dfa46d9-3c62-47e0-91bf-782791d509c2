<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TelegramBot {
    protected $CI;
    protected $token;
    protected $api_url;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->config('telegram');
        $this->token = $this->CI->config->item('telegram_bot_token');
        $this->api_url = $this->CI->config->item('telegram_api_url') . $this->token . '/';
    }

    public function sendMessage($chat_id, $text, $parse_mode = 'Markdown', $buttons = []) {
		$data = [
			'chat_id' => $chat_id,
			'text' => $text,
			'parse_mode' => $parse_mode
		];

		if (!empty($buttons)) {
			$data['reply_markup'] = json_encode([
				'inline_keyboard' => $buttons
			]);
		}

		return $this->sendRequest('sendMessage', $data);
	}
	
	public function sendMessageWithKeyboard($chat_id, $text, $keyboard, $parse_mode = 'Markdown') {
		return $this->sendRequest('sendMessage', [
			'chat_id' => $chat_id,
			'text' => $text,
			'parse_mode' => $parse_mode,
			'reply_markup' => json_encode($keyboard)
		]);
	}

    public function sendRequest($method, $params = []) {
        $url = $this->api_url . $method;
        $options = [
            'http' => [
                'header'  => "Content-Type:application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($params),
            ]
        ];
        $context = stream_context_create($options);
        return json_decode(file_get_contents($url, false, $context), true);
    }
	
	public function sendPhoto($chat_id, $image_input, $caption = '')
    {
        $token = $this->token;
        
        $url = "https://api.telegram.org/bot{$token}/sendPhoto";

        $tmpFile = null; // Track temp file for cleanup
        $post_fields = [
            'chat_id' => $chat_id,
            'caption' => $caption,
            'parse_mode' => 'Markdown',
        ];

        // Case 1: data:image/...;base64,...
        if (preg_match('/^data:image\/(\w+);base64,/', $image_input, $type)) {
            $data = base64_decode(substr($image_input, strpos($image_input, ',') + 1));
            if ($data === false) {
                throw new \Exception('Base64 decoding failed');
            }
            $ext = $type[1]; // e.g., png, jpeg
            $tmpFile = tempnam(sys_get_temp_dir(), 'tgimg_') . '.' . $ext;
            file_put_contents($tmpFile, $data);
            $post_fields['photo'] = new CURLFile($tmpFile, "image/$ext", "image.$ext");
        }

        // Case 2: Valid image URL (Telegram accepts direct URLs)
        elseif (filter_var($image_input, FILTER_VALIDATE_URL)) {
            $post_fields['photo'] = $image_input;
        }

        // Case 3: Local file path
        elseif (file_exists($image_input)) {
            $mime_type = mime_content_type($image_input);
            $ext = pathinfo($image_input, PATHINFO_EXTENSION);
            $post_fields['photo'] = new CURLFile($image_input, $mime_type, "image.$ext");
        }

        else {
            throw new \Exception('Invalid image input format');
        }

        // Send via CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        $response = curl_exec($ch);
        curl_close($ch);

        // Clean up temp file
        if ($tmpFile && file_exists($tmpFile)) {
            unlink($tmpFile);
        }

        return $response;
    }
	
	function sendDocument($chat_id, $file_path) {

		$bot_token = $this->token;

		$url = "https://api.telegram.org/bot$bot_token/sendDocument";

		$post_fields = [
			'chat_id'   => $chat_id,
			'document'  => new CURLFile(realpath($file_path))
		];

		$ch = curl_init(); 
		curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type:multipart/form-data"]);
		curl_setopt($ch, CURLOPT_URL, $url); 
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); 
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields); 
		$output = curl_exec($ch);
		curl_close($ch);

		return $output;
	}


}
