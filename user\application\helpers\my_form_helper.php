<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

if (!function_exists('myDropdown')) {

    function myDropdown($name, $options, $setDefault = null, $disabled = [], $extra = null) {
        $myOptions = [];
        $myDisabled = [];
        foreach ($options as $key => $value) {
            if ($key != '') {
                if (in_array($key, $disabled)) {
                    array_push($myDisabled, $key . '-:|#|:-' . $value);
                }
                $myOptions[$key . '-:|#|:-' . $value] = $value;
            } else {
                $myOptions[$key] = $value;
            }
            if ($setDefault != null && $key == $setDefault) {
                $setDefault = $key . '-:|#|:-' . $value;
            }
        }
        unset($options);
        unset($disabled);
        $ci = &get_instance();
        $inputName = '';
        if (isArray($name)) {
            $inputName = $name['name'];
        } else {
            $inputName = $name;
        }

        $setDefault = ($ci->input->method() === 'post') ? $ci->input->post($inputName) : $setDefault;
        return form_dropdown($name, $myOptions, set_value($inputName, $setDefault), $myDisabled, $extra);
    }

}
if (!function_exists('myDropdownSelectedValue')) {

    function myDropdownSelectedValue($name) {
        $ci = &get_instance();
        $value = ($ci->input->method() === 'post') ? $ci->input->post($name) : (($ci->input->method() === 'get') ? $ci->input->get($name) : []);
        return jsDropdownSelectedValue($value);
    }

}

if (!function_exists('myDropdownSelectedName')) {

    function myDropdownSelectedName($name) {
        $ci = &get_instance();
        $value = ($ci->input->method() === 'post') ? $ci->input->post($name) : (($ci->input->method() === 'get') ? $ci->input->get($name) : []);
        return jsDropdownSelectedName($value);
    }

}


if (!function_exists('myDropdownOptions')) {

    function myDropdownOptions($optionsData, $placeHolder = '', $id = '', $name = '') {
        $options = [];
        if ($placeHolder != '') {
            $options [''] = $placeHolder;
        }
        if (isArray($optionsData)) {
            $id = ( $id != '') ? $id : 'id';
            $name = ( $name != '') ? $name : 'name';
            foreach ($optionsData as $key => $column) {
                $options[$column->$id] = $column->$name;
            }
        }return $options;
    }

}
if (!function_exists('jsDropdownOptions')) {

    function jsDropdownOptions($options) {
        $myOptions = '';
        foreach ($options as $key => $value) {
            if ($key != '') {
                $myOptions .= '<option value="' . $key . '-:|#|:-' . $value . '">' . $value . '</option>';
            } else {
                $myOptions .= '<option value="' . $key . '">' . $value . '</option>';
            }
        }
        return $myOptions;
    }

}
if (!function_exists('jsDropdownSelectedName')) {

    function jsDropdownSelectedName($value) {
        $selectedValue = explode('-:|#|:-', $value);
        return end($selectedValue);
    }

}
if (!function_exists('jsDropdownSelectedValue')) {

    function jsDropdownSelectedValue($value) {
        $selectedValue = explode('-:|#|:-', $value);
        return $selectedValue[0];
    }

}
if (!function_exists('dbSetRadio')) {

    function dbSetRadio($name, $defaultValue, $dbValue) {
        return set_radio($name, $defaultValue, (($dbValue == $defaultValue) ? TRUE : FALSE));
    }

}

if (!function_exists('setWithDBValue')) {

    function setWithDBValue($inputName, $dbValue) {
        $ci = &get_instance();
        return set_value($inputName, ($ci->input->post()) ? $ci->input->post($inputName) : $dbValue);
    }

}
if (!function_exists('makePasswordHash')) {

    function makePasswordHash($inputValue) {
        $options = ['cost' => 8,
            'salt' => mcrypt_create_iv(22, MCRYPT_DEV_URANDOM)];
        return password_hash($inputValue, PASSWORD_BCRYPT, $options);
    }

}
if (!function_exists('verifyPasswordHash')) {

    function verifyPasswordHash($inputValue, $hash) {
        return password_verify($inputValue, $hash) ? true : false;
    }

}
if (!function_exists('validationClass')) {

    function validationClass($field) {
        return (validation_errors()) ? (form_error($field) ? ' has-error ' : ' has-success ') : 'has-info';
    }

}
if (!function_exists('getErrorMessage')) {

    function getErrorMessage() {
        return "We're sorry! An unexpected error has occurred. Our technical staff has been automatically notified and will be looking into this with utmost urgency.";
    }

}
if (!function_exists('csrfInput')) {

    function csrfInput() {
        $ci = &get_instance();
        return '<input type="hidden" name="' . $ci->security->get_csrf_token_name() . '" value="' . $ci->security->get_csrf_hash() . '" />';
    }

}
if (!function_exists('csrfJsonObject')) {

    function csrfJsonObject() {
        $ci = &get_instance();
        return json_encode([$ci->security->get_csrf_token_name() => $ci->security->get_csrf_hash()]);
    }

}
if (!function_exists('csrfArray')) {

    function csrfArray() {
        $ci = &get_instance();
        return [$ci->security->get_csrf_token_name() => $ci->security->get_csrf_hash()];
    }

}
if (!function_exists('csrfKey')) {

    function csrfKey() {
        $ci = &get_instance();
        return $ci->security->get_csrf_token_name();
    }

}
if (!function_exists('csrfValue')) {

    function csrfValue() {
        $ci = &get_instance();
        return $ci->security->get_csrf_hash();
    }

}

if (!function_exists('makeCurlFile')) {

    function makeCurlFile($file) {
        return new CurlFile($_FILES[$file]["tmp_name"], $_FILES[$file]['type'], $_FILES[$file]["name"]);
    }

}