<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class CellIdExcelModel extends MY_Model { 

    protected $primaryTable = 'tbl_cell_id_excel';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	public function select_sum($where){
		return $truecallerDataSum = $this->db->select_sum('search_records')
			->where($where)
			->get($this->primaryTable)
			->row()
			->search_records;
	}
	
	public function fetchMapData($where){
		$query =  $this->db->select('*')
			->where($where)
			->get('tb_cell_data');
		return $query->result();
	}
	
	public function generateUrl(){
		$name = $this->generateUniqueAlphabetString(7);
		$exists = $this->checkIfExists($name);
		if ($exists) {
			$name = $this->generateUniqueAlphabetString(15);
			$exists = $this->checkIfExists($name);
			if ($exists) {
				$name = $this->generateUniqueAlphabetString(30);
				$exists = $this->checkIfExists($name);
				if ($exists) {
					$name = $this->generateUniqueAlphabetString(50);
					$exists = $this->checkIfExists($name);
				}
			}
		}
		return $name;
	}
	public function insertCellData($res,$name='',$commonUrl=0,$userId){
		
		if($name == ''){
			$name = $this->generateUrl();
			//$common_name = $this->generateUrl();
			//$common_name = $name;
		}
		//else{
			//$common_name = $name;
			//$name = $this->generateUrl();
		//}
		$common_name = $name;
		
		if($commonUrl == 1){
			$cname = $common_name;
		}else{
			$cname = '';
		}
		
		
		if(!isset($res->latitude) || !isset($res->longitude)){
			return ["ERROR"];
		}
		$data = [
			'userId' =>	$userId,
			'cellIDCode' => $res->cellIDCode,
			'city' => $res->city,
			'type1' => $res->type1,
			'cellIDCodeWLAC' => $res->cellIDCodeWLAC,
			'eAzimuth' => $res->eAzimuth,
			'sAzimuth' => $res->sAzimuth,
			'mcc' => $res->mcc,
			'mnc' => $res->mnc,
			'lac' => $res->lac,
			'uniqueID' => $res->uniqueID,
			'dateUpload' => $res->dateUpload,
			'circle' => $res->circle,
			'operator' => $res->operator,
			'tmRemark' => $res->tmRemark,
            'lat' => $res->latitude,
            'lon' => $res->longitude,
			'radius' => isset($res->radius) ? $res->radius : '500',
            'azimooth' => $res->azimooth,
            'address' => $res->siteAddress,
			'name' => $name,
			'common_name' => $cname,
			'cgi' => $res->cgi,
			'lbsLatitude' => isset($res->lbsLatitude) ? $res->lbsLatitude : null,
			'lbsLongitude' => isset($res->lbsLongitude) ? $res->lbsLongitude : null,
			'lbsRadius' => isset($res->lbsRadius) ? $res->lbsRadius : 0,
			'lbsColor' => isset($res->lbsColor) ? $res->lbsColor : 0,
        ];

        $this->db->insert('tb_cell_data', $data);
		return [$common_name,$name,$this->db->insert_id()];
	
	}
	
	public function checkIfExists($name) {
        $this->db->where('name', $name);
        $query = $this->db->get('tb_cell_data');
        return $query->num_rows() > 0;
    }	
	
	public function generateUniqueAlphabetString($length = 10) {
		$alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$shuffledAlphabet = str_shuffle($alphabet); 
		return substr($shuffledAlphabet, 0, $length);
	}
	
	function export($type,$where = '',$limit='') { 
		$this->db->select("cgi,cellIDCode,lat,lon,address,city,tmRemark,operator,circle,dateUpload,uniqueID,mcc,mnc,lac,sAzimuth,eAzimuth,cellIDCodeWLAC,type1,azimooth, CASE 
        WHEN common_name = '' 
        THEN CONCAT('https://msg.ccas.in/user/map/view/', name) 
        ELSE CONCAT('https://msg.ccas.in/user/map/viewsingle/', common_name) 
    END as name")
			->from('tb_cell_data' . ' t1');

		$this->db->order_by('id', 'asc');
		if ($where != '') {
			$this->db->where($where);
		}
		if ($limit != '') {
			$this->db->limit($limit);
		}
		if($type=='excel') {
			$this->db->group_by('cgi');
		}
		$res = $this->getResult($this->db->get());
		return $res;
    }

}
