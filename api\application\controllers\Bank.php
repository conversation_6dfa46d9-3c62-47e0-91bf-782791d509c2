<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Bank extends ApiController
{
	private $client_token;
    function __construct()
    {
        parent::__construct();
		$this->client_token = $this->token->validateToken();
    }
	
    function search_post()
    {
        if(!$this->client_token){
            return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
        }
        
        $this->load->model('bankModel');
        $this->load->model('VerificationTokenModel');

        $this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
        $this->form_validation->set_rules('bankacc', 'bankacc', 'trim|required|xss_clean');
        $this->form_validation->set_rules('ifsc', 'ifsc', 'trim|required|xss_clean');
        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $ifsc = (string) $this->input->post("ifsc");
            $bankacc = (string) $this->input->post("bankacc");
            $user_id = $this->input->post("userId");
            $date = new DateTime("now");
            $check_date = $date->format('Y-m-d');
            $curr_date = $date->format('Y-m-d h:i:s');
            $this->load->model('userModel');
            if ($user_id) {
                $returnData = $this->userModel->findOne(['id' => $user_id]);
                if (!isObject($returnData)) {
                    unset($mobile);
                    unset($returnData);
                    return $this->set_response($this->exitDanger('Invalid User'), REST_Controller::HTTP_OK);
                }
            }
                            
            $users = $this->token->userlimit($user_id, 'bank');
			
            if(!$users || empty($users)){
                return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
            }else{
                if($users['status']){
                    $userlimit = $users['error'];
                }else{
                    return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
                }
            }
            
            $truecallerDataCount = $this->bankModel->count(['userId' => $user_id,'DATE(created_at)'=>$check_date]);
        
            if($truecallerDataCount >=$userlimit){
                return $this->set_response($this->exitDanger('Daily limit over.Please try tomorrow.'), REST_Controller::HTTP_OK);die;
            }

            // --- NEW BANK API LOGIC STARTS HERE ---
            // Get active token for bank service
            $tokenRow = $this->VerificationTokenModel->getActiveToken('bank');
            if (!$tokenRow) {
                return $this->set_response($this->exitDanger("No active API token available."), REST_Controller::HTTP_OK);
            }
            $apiToken = $tokenRow->token;

            // First API: Get request_id
            $firstApiPayload = json_encode([
                "task_id" => "123",
                "group_id" => "1234",
                "data" => [
                    "bank_account_no" => $bankacc,
                    "bank_ifsc_code" => $ifsc
                ]
            ]);

            log_message('error', 'Bank First API Payload: ' . $firstApiPayload);

            $curl1 = curl_init();
            curl_setopt_array($curl1, array(
                CURLOPT_URL => 'https://indian-bank-account-verification.p.rapidapi.com/v3/tasks/async/verify_with_source/validate_bank_account',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $firstApiPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'x-rapidapi-host: indian-bank-account-verification.p.rapidapi.com',
                    'x-rapidapi-key: ' . $apiToken
                ),
            ));
            $firstApiResponse = curl_exec($curl1);
            curl_close($curl1);

            $firstApiData = json_decode($firstApiResponse, true);
            $request_id = isset($firstApiData['request_id']) ? $firstApiData['request_id'] : null;

            if (!$request_id) {
                $this->VerificationTokenModel->setTokenStatus($tokenRow->id, 'inactive');
                return $this->set_response($this->exitDanger("Unable to get request_id from Bank API."), REST_Controller::HTTP_OK);
            }

            $this->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

            // Wait before polling second API
            sleep(10);

            // Second API: Get bank data using request_id
            $curl2 = curl_init();
            curl_setopt_array($curl2, array(
                CURLOPT_URL => 'https://indian-bank-account-verification.p.rapidapi.com/v3/tasks?request_id=' . $request_id,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'x-rapidapi-host: indian-bank-account-verification.p.rapidapi.com',
                    'x-rapidapi-key: ' . $apiToken
                ),
            ));
            $secondApiResponse = curl_exec($curl2);
            curl_close($curl2);

            $this->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

            $responses = json_decode($secondApiResponse, true);

            // Extract the result object
            $dataInsert = '';
            $status = 'FAIL';
            $extraction = null;
            // Updated extraction logic to match actual API response
            if (is_array($responses) && isset($responses[0]['result'])) {
                $extraction = $responses[0]['result'];
                $dataInsert = json_encode($extraction);
                $status = isset($responses[0]['status']) && $responses[0]['status'] == 'completed' ? 1 : $responses[0]['status'];
            }

            $inserData = [
                'userId' => $user_id,
                'response' => $dataInsert,
                'status' => $status,
                'bank' => $bankacc,
                'ifsc' => $ifsc,
                //'created_at' => $curr_date
            ];

            $this->bankModel->attach($inserData);

            // Prepare output using the new structure
            if ($extraction) {
                $result = "<p></p><h3>Bank Account Information</h3><p>";
                $result .= "<b>Name</b>: " . (isset($extraction['name_at_bank']) && $extraction['name_at_bank'] ? $extraction['name_at_bank'] : 'N/A') . "<br>";
                $result .= "<b>Bank A/c No</b>: " . (isset($extraction['bank_account_number']) && $extraction['bank_account_number'] ? $extraction['bank_account_number'] : 'N/A') . "<br>";
                $result .= "<b>IFSC Code</b>: " . (isset($extraction['ifsc_code']) && $extraction['ifsc_code'] ? $extraction['ifsc_code'] : 'N/A') . "<br>";
                $result .= "<b>A/c exists</b>: " . (isset($extraction['account_exists']) && $extraction['account_exists'] ? $extraction['account_exists'] : 'N/A') . "<br>";
                $result .= "<b>Amount Deposited</b>: " . (isset($extraction['amount_deposited']) && $extraction['amount_deposited'] ? $extraction['amount_deposited'] : 'N/A') . "<br>";
                $result .= "<b>Status</b>: " . (isset($extraction['status']) && $extraction['status'] ? $extraction['status'] : 'N/A') . "<br>";
                $result .= "</p>";

                $data = [ "pi" => $result ];
                return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
            } else {
                log_message('error', 'Bank API extraction failed. $responses: ' . print_r($firstApiData, true) . ' $secondApiResponse: ' . print_r($responses, true));
                return $this->set_response($this->exitDanger("Some issue occurred. Please contact admin"), REST_Controller::HTTP_OK);
            }
        }
    }

    public function getDetails($patterns,$response,$result){
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                $result .= "<b>".$key."</b>: ".$matches[1]."<br>";
            } else {
                $result .= "<b>".$key."</b>: ".'N/A'."<br>";
            }
        }
        return $result;
    }
    
    function history_post()
    {
		if(!$this->client_token){
			return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
		$this->form_validation->set_rules('searchItem', 'searchItem', 'trim|xss_clean');
		$this->form_validation->set_rules('to', 'to', 'trim|xss_clean');
		$this->form_validation->set_rules('from', 'from', 'trim|xss_clean');
		$this->form_validation->set_rules('offset', 'offset', 'trim|xss_clean');

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        }
		
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->post('searchItem');
        $user_id =  $this->input->post('userId');
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.bank like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;$offset=0;
		$offset=$this->input->post('offset');
        $this->load->model('bankModel');
        $totalRows = $this->bankModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $data = $this->bankModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
		
		// Get daily search count for today's date
        $today = date('Y-m-d');
        $dailySearchCount = $this->bankModel->count(['userId' => $user_id, 'DATE(created_at)' => $today]);

        // Get daily credit using helper function
        $dailyCredit = getDailyCredit($user_id, 'bank');
		
        $config['total_rows'] = $totalRows;
		$config['per_page'] = $perPage;
 		$config['data'] = $data;
		$config['daily_search'] = $dailySearchCount;
 		$config['daily_credit'] = $dailyCredit;
       	return $this->set_response($this->exitSuccessWithMultiple($config), REST_Controller::HTTP_OK);
				
    }
	function detail_get($id)
    {
        if(!$this->client_token){
            return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
        }
        if ($id) {
            $this->load->model('bankModel');
            $value = $this->bankModel->findOne(['id' => $id], '*');
            if (isObject($value)) {
                $result = '';
                if(isset($value->response) && $value->status){
                    // Try to decode JSON extraction (new API structure)
                    $extraction = json_decode($value->response, true);

                    if (is_array($extraction)) {
                        $result = "<p></p><h3>Bank Account Information</h3><p>";
                        $result .= "<b>Name</b>: " . (isset($extraction['name_at_bank']) && $extraction['name_at_bank'] ? $extraction['name_at_bank'] : 'N/A') . "<br>";
                        $result .= "<b>Bank A/c No</b>: " . (isset($extraction['bank_account_number']) && $extraction['bank_account_number'] ? $extraction['bank_account_number'] : 'N/A') . "<br>";
                        $result .= "<b>IFSC Code</b>: " . (isset($extraction['ifsc_code']) && $extraction['ifsc_code'] ? $extraction['ifsc_code'] : 'N/A') . "<br>";
                        $result .= "<b>A/c exists</b>: " . (isset($extraction['account_exists']) && $extraction['account_exists'] ? $extraction['account_exists'] : 'N/A') . "<br>";
                        $result .= "<b>Amount Deposited</b>: " . (isset($extraction['amount_deposited']) && $extraction['amount_deposited'] ? $extraction['amount_deposited'] : 'N/A') . "<br>";
                        $result .= "<b>Status</b>: " . (isset($extraction['status']) && $extraction['status'] ? $extraction['status'] : 'N/A') . "<br>";
                        $result .= "</p>";
                        $data = [ "pi" => $result ];
                        return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
                    } else {
                        // Fallback: try regex extraction (old structure)
                        $data = [];
                        $patterns = [
                            'Name' => '/Name:\s*([^\n]+)/',
                            'Bank A/c No' => '/Bank A\/c No:\s*([^\n]+)/',
                            'IFSC Code' => '/IFSC Code:\s*([^\n]+)/',
                            'A/c exists' => '/A\/c exists:\s*([^\n]+)/'
                        ];
                        $result = "<p></p><h3>Bank Account Information</h3><p>";
                        $result = $this->getDetails($patterns, $value->response, $result);
                        $result .= "</p>";
                        $data = [ "pi" => $result ];
                        return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
                    }
                }else{
                    return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);	
                }
            } else {
               return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);	
            }
        } else {
               return $this->set_response($this->exitDanger("Please check request"), REST_Controller::HTTP_OK);	
        }
    }

}
