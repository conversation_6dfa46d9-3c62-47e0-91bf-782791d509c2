
<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class OsintModel extends MY_Model {

    protected $primaryTable = 'tb_osint';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }

    function getAll($where = '', $limit = []) {
        $this->db->select('t1.id,t1.username')
                ->select('t2.verified')
                ->select('t3.profileId, t3.teamName, t3.followers, t3.following, t3.fullName, t3.state')
				->select('t4.verified as amazon_verified')
				->select('t5.name, t5.gender, t5.image, t5.altName, t5.about, t5.phone, t5.address,t5.spam_description,t5.internetAddress,t5.mobile as tc_mobile')
                ->from($this->primaryTable . ' t1')
                ->join('tb_flipkart_account t2', 't1.' . $this->primaryTablePK . '=t2.osintId', 'left')
                ->join('tb_dream_eleven_account t3', 't1.' . $this->primaryTablePK . '=t3.osintId', 'left')
				->join('tb_amazon_account t4', 't1.' . $this->primaryTablePK . '=t4.osintId', 'left')
				->join('tb_truecaller_account t5', 't1.' . $this->primaryTablePK . '=t5.osintId', 'left');
        if ($where != '')
            $this->db->where($where);
        $this->db->order_by('t1.id', 'desc');
        if (isArray($limit)) {
            $this->db->limit($limit[1], $limit[0]);
        }
        return $this->getResult($this->db->get());
    }

}