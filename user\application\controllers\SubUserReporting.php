<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class SubUserReporting extends SecureController {

    function __construct() {
        parent::__construct();
		//die;
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
        $this->load->model('SubUserReportingModel','SubUserReporting');
    }

    public function all($offset = 0){
		
		$this->load->model('SubUserModel');
		$subuserId = $this->input->get('subuserId');
		$user_id = $this->viewDataBag->userSession->id;
		$subusers = $this->SubUserModel->find(['userId' => $user_id],'id,mobile as name');
		$this->viewDataBag->subusers = $subusers;
		$this->viewDataBag->response = '';
		$this->viewDataBag->pagination = '';
		if($subuserId!='') {
			$subuserId = jsDropdownSelectedValue($subuserId);
			$name = $this->input->get('name');
        	//$where = 't2.userId='.$user_id;
			$where = 't1.subuserId = '.$subuserId;
        if ($name != '') {
            $where .= ' and ( t1.app_name like "' . $name . '%" )';
        }		
        $perPage = 50;       
        $totalRows = $this->SubUserReporting->totalCount($where); 
        $offset = ($offset > $totalRows) ? 0 : $offset;  
        $this->viewDataBag->response =  $this->SubUserReporting->getAll($where, '*' , ['orderBy' => ['id', "DESC"], "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
		}
        $this->viewDataBag->jsView = 'subUserReporting/jsView';
        $this->loadView('subUserReporting/listView', $this->viewDataBag);
    }
      
}