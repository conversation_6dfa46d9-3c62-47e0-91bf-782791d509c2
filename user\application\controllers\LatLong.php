<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class LatLong extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
        if (strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime()) || empty($res) ) {
           return redirectTo("me/dashboard");
        }
    }
    function find()
    {
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/latlong/search.js")];
        $this->viewDataBag->jsView = 'latlong/jsView';
        $this->loadView('latlong/searchView', $this->viewDataBag);
    }
}
