<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Subuser extends AjaxSecureController
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('SubUserModel');
    }

    function takeAction()
    {
        $status = $this->input->post('status');
        $execute = false;
        if ($status == "delete") {
            $image = $this->SubUserModel->findOne(['id' => $this->input->post('id')]);
            $this->SubUserModel->detach(['id' => $this->input->post('id')]);
            $this->db->where('subuserId', $this->input->post('id'));
            $this->db->delete('tb_subuser_reporting');
            $execute = true;
        } else {
            $execute = false;
        }
        if ($execute) {
            unset($where);
            unset($updateData);
            $this->exitSuccess();
        } else {
            unset($where);
            unset($updateData);
            $this->exitDanger();
        }
    }

    function search()
    {
        $searchItem = $this->input->post('searchItem');
        $isStatus = jsDropdownSelectedValue($this->input->post('isStatus'));

        $where = "";
        if ($searchItem != '' && $searchItem != null) {
            $where .= ' mobile like "%' . $searchItem . '%" || email like "%' . $searchItem . '%" || name like "%' . $searchItem . '%" ';
        }

        if ($isStatus != '' || $isStatus != null && $searchItem != '' && $searchItem != null) {
            $where .= " And isStatus = '" . $isStatus . "'";
        }
        if ($isStatus != '' && $isStatus != null && $searchItem == '' && $searchItem == null) {
            $where = "  isStatus = '" . $isStatus . "'";
        }

        if ($where != '') {
            $this->load->model('SubUserModel');
            $users = $this->SubUserModel->find($where, "id,name,mobile,email,isStatus,createdOn");
            $this->exitWithSuccess($this->load->view('subuserb/ajax/listView', ["users" => $users, "pagination" => ''], true));
        } else {
            $this->exitWithDanger();
        }
    }

}
