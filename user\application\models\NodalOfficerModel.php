
<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class NodalOfficerModel extends MY_Model {

    protected $primaryTable = 'tb_nodal';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }


    public function getAllNodal() { 
            $query = $this->db->get('tb_nodal');  
            return $query->result();


    }

}

