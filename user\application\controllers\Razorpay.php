<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON>y extends SecureController {

    private $user;
    private $ci;
    function __construct()
    {
        parent::__construct();
        $this->ci = &get_instance();
        $this->ci->config->load('env', true);
        define('RAZORPAY_KEY_ID', $this->ci->config->item('RAZORPAY_KEY_ID', 'env')); // key id
        define('RAZORPAY_KEY_SECRET', $this->ci->config->item('RAZORPAY_KEY_SECRET', 'env')); // key secret
    }

	public function index() {
		$this->checkout();
	}

	public function checkout() {
        $data['title']              = 'Checkout payment | Infovistar';  
        $data['callback_url']       = base_url().'razorpay/callback';
        $data['surl']               = base_url().'razorpay/success';;
        $data['furl']               = base_url().'razorpay/failed';;
        $data['currency_code']      = 'INR';
		$data['razorpay_keyid']					= RAZORPAY_KEY_ID;
        $this->load->view('razorpay/checkout', $data);
    }

    // initialized cURL Request
    private function curl_handler($payment_id, $amount)  {
        $url            = 'https://api.razorpay.com/v1/payments/'.$payment_id.'/capture';
        $key_id         = RAZORPAY_KEY_ID;
        $key_secret     = RAZORPAY_KEY_SECRET;
        $fields_string  = "amount=$amount";
        //cURL Request
        $ch = curl_init();
        //set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERPWD, $key_id.':'.$key_secret);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        return $ch;
    }   
        
    // callback method
    public function callback() {   
        //print_r($this->input->post());     
        if (!empty($this->input->post('razorpay_payment_id')) && !empty($this->input->post('merchant_order_id'))) {
            $razorpay_payment_id = $this->input->post('razorpay_payment_id');
            $merchant_order_id = $this->input->post('merchant_order_id');
            
            $this->session->set_flashdata('razorpay_payment_id', $this->input->post('razorpay_payment_id'));
            $this->session->set_flashdata('merchant_order_id', $this->input->post('merchant_order_id'));
            $currency_code = 'INR';
            $amount = $this->input->post('merchant_total');
            $success = false;
            $error = '';
            try {                
                $ch = $this->curl_handler($razorpay_payment_id, $amount);
                //execute post
                $result = curl_exec($ch);

                $userId = $this->viewDataBag->userSession->id;
                $resultArray = json_decode($result, true);
                $inserData = [
                    'paymentId' => $resultArray['id'],
                    'orderNumber' => $this->input->post('merchant_order_id'),
                    'paymentCreatedOn' => currentDateTime(),
                    'paymentIsStatus' => ($resultArray['status']=='captured') ? 'success' : 'pending' ,
                    'paymentAmount' => ($resultArray['amount']/100),
                    'responseMessage' => $resultArray['status'],
                    'paymentGateway' => 'razorpay',
                    'userId' => $userId,
                ];
                $this->load->model('paymentDetailsModel');
                $this->paymentDetailsModel->attach($inserData);

                $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                if ($result === false) {
                    $success = false;
                    $error = 'Curl error: '.curl_error($ch);
                } else {
                    $response_array = json_decode($result, true);
                        //Check success response
                        if ($http_status === 200 and isset($response_array['error']) === false) {
                            $success = true;
                            $this->load->model('userModel');
                            $this->userModel->modify(['registrationType' => 'paid', 'paymentMode' => 'online', 'registrationRequest' => 'new'], ['id' => $userId]);
                            return redirectTo('me/dashboard/');
                        } else {
                            $success = false;
                            if (!empty($response_array['error']['code'])) {
                                $error = $response_array['error']['code'].':'.$response_array['error']['description'];
                            } else {
                                $error = 'RAZORPAY_ERROR:Invalid Response <br/>'.$result;
                            }
                        }
                }
                //close curl connection
                curl_close($ch);
            } catch (Exception $e) {
				echo $e->getMessage();die;
                $success = false;
                $error = 'Request to Razorpay Failed';
            }
            
            if ($success === true) {
                if(!empty($this->session->userdata('ci_subscription_keys'))) {
                    $this->session->unset_userdata('ci_subscription_keys');
                }
                /*if (!$order_info['order_status_id']) {
                    redirect($this->input->post('merchant_surl_id'));
                } else {
                    redirect($this->input->post('merchant_surl_id'));
                }*/
                $this->load->model('userModel');
                $this->userModel->modify(['registrationType' => 'paid', 'paymentMode' => 'online', 'registrationRequest' => 'new'], ['id' => $userId]);
                return redirectTo('me/dashboard/');

            } else {
                $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
                $this->viewDataBag->responseData = (object)$responseData;
                $this->loadView('payment/failedView', $this->viewDataBag);
                /*redirect($this->input->post('merchant_furl_id'));*/
            }
        } else {
            echo 'An error occured. Contact site administrator, please!';
        }
    } 
    public function success() {
        $data['title'] = 'Razorpay Success | TechArise';
        echo "<h4>Your transaction is successful</h4>";  
        echo "<br/>";
        echo "Transaction ID: ".$this->session->flashdata('razorpay_payment_id');
        echo "<br/>";
        echo "Order ID: ".$this->session->flashdata('merchant_order_id');
    }  
    public function failed() {
        $data['title'] = 'Razorpay Failed | TechArise';  
        echo "<h4>Your transaction got Failed</h4>";            
        echo "<br/>";
        echo "Transaction ID: ".$this->session->flashdata('razorpay_payment_id');
        echo "<br/>";
        echo "Order ID: ".$this->session->flashdata('merchant_order_id');
    }

}