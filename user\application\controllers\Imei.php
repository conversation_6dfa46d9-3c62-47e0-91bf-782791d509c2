<?php

defined('BASEPATH') OR exit('No direct script access allowed');

    class imei extends SecureController {

        function __construct() {
            parent::__construct();
			$res = accessModule('ip_grabber');
			if (empty($res) ) {
				return redirectTo("me/dashboard");
			}
            $this->load->model('ImeiModel','imei');
        }

        public function all($offset = 0){
            $name = $this->input->get('name');
            $imeiNumber = substr($name,0,8);
            $imeiData = companyInfo($imeiNumber);
            $this->viewDataBag->response = $imeiData->Data;
            $this->viewDataBag->jsView = 'imei/jsView';
            $this->loadView('imei/listView', $this->viewDataBag);
        }

        function search(){
            $response = "";
            $primaryImei = "";
            $companyName = "";

            if ($this->input->method() === 'post'){
                $this->form_validation->set_rules('imeiNumber', 'imeiNumber', 'trim|required|xss_clean|regex_match[/^[0-9]{14}$/]');
                if ($this->form_validation->run() === true) {
                    $imeiNumber = $this->input->post("imeiNumber");
                    $firstOption = $imeiNumber+1;
                    $secondOption = $imeiNumber-1;
                    $str1 = substr($imeiNumber, 0, 6);
                    $str2 = substr($imeiNumber, 6);
                    $thirdOption = ($str1 + 1).''.$str2;
                    $fourthOption = ($str1 - 1).''.$str2;
                
                    $primaryImei = $imeiNumber.''.imeiFifteenthDigit($imeiNumber);
                    $firstOption = $firstOption.''.imeiFifteenthDigit($firstOption);
                    $secondOption = $secondOption.''.imeiFifteenthDigit($secondOption);
                    $thirdOption = $thirdOption.''.imeiFifteenthDigit($thirdOption);
                    $fourthOption = $fourthOption.''.imeiFifteenthDigit($fourthOption);
                    $response = [];
                    
                    $response[] = $firstOption;
                    $response[] = $secondOption;
                    $response[] = $thirdOption;
                    $response[] = $fourthOption;
                    
                    $imeiNumber = substr($primaryImei,0,8);
                    $imeiData = companyInfo($imeiNumber);
					if($imeiData->StatusCode==1) {
                    	$data = $imeiData->Data[0];
                    	$companyName = $data->CompanyName;
					}                    
                }
            }
            $this->viewDataBag->companyName = $companyName;
            $this->viewDataBag->primaryImei = $primaryImei;
            $this->viewDataBag->response = $response;
            $this->viewDataBag->jsView = 'imei/jsView';
            $this->loadView('imei/searchView', $this->viewDataBag);
        }

   }