<?php
defined('BASEPATH') OR exit('No direct script access allowed');
class Isp extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
    }

    function search()
    {
        $this->form_validation->set_rules('ip', 'ip', 'trim|required');
        if ($this->form_validation->run() === true):
		    $authToken = $this->viewDataBag->userSession->auth_token;
		
		    $ip = $this->input->post("ip");
            $post['ip'] = $ip;
			$post['authToken'] = $authToken;
        	$this->load->library('curl');
        	$response = $this->curl->post(apiURL("isp/search"), $post);
		
            return $this->exitSuccessWithMultiple(['response' => $response]);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
}
