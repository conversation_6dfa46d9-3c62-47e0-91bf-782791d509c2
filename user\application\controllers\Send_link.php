<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Send_link extends SecureController
{

    function __construct()
    {
        parent::__construct();
        if (strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime())) {
           return redirectTo("me/dashboard");
        }
    }
    function history($offset = 0)
    {

        $searchItem = $this->input->get('searchItem');
        $where = ' ( userId = "' . $this->viewDataBag->userSession->id . '")';
        if ($searchItem != '') {
            $where .= 'AND ( mobile like "%' . $searchItem . '%" )';
        }
        $perPage = 50;
        $this->load->model('smsRecordModel');
        $totalRows = $this->smsRecordModel->count($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;

        $this->viewDataBag->response = $this->smsRecordModel->find($where, '*', ['orderBy' => ['id', "DESC"],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        
        $this->load->model("userModel");
        $smsCredit = $this->userModel->findOne(['id' => $this->viewDataBag->userSession->id], "smsCredit");
        $this->viewDataBag->jsView = 'sendLink/jsView';
        $this->viewDataBag->smsCredit =  $smsCredit->smsCredit;
        $this->loadView('sendLink/listView', $this->viewDataBag);
    }
}
