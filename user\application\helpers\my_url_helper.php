<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

if (!function_exists('redirectToLoginPanel')) {

    function redirectToLoginPanel($url, $method = 'auto', $code = '') {
        return ($method == 'location') ? redirect($url, $method) : redirect($url, $method, $code);
    }

}

if (!function_exists('redirectTo')) {

    function redirectTo($url, $method = 'auto', $code = '') {
        return ($method == 'location') ? redirect(url($url), $method) : redirect(url($url), $method, $code);
    }

}
if (!function_exists('redirectToCurrent')) {

    function redirectToCurrent() {
        return redirect(current_url());
    }

}
if (!function_exists('redirectToItSelf')) {

    function redirectToItSelf($url) {
        $ci = & get_instance();
        return redirect($ci->router->directory . $ci->router->fetch_class() . '/' . $url);
    }

}
if (!function_exists('redirectToLatest')) {

    function redirectToLatest($currentSlugName, $currentSlugId) {
        $ci = & get_instance();
        return redirect(makeSlugURL(url() . $ci->router->fetch_class() . '/' . $ci->router->fetch_method(), $currentSlugName, $currentSlugId));
    }

}

if (!function_exists('redirectToHome')) {

    function redirectToHome() {
        return redirect(url());
    }

}
if (!function_exists('redirectToBack')) {

    function redirectToBack() {
        return redirect(backURL());
    }

}
if (!function_exists('getDomain')) {

    function getDomain() {
        return preg_replace("/^[\w]{2,6}:\/\/([\w\d\.\-]+).*$/", "$1", base_url());
    }

}
if (!function_exists('getWebsite')) {

    function getWebsite() {
        $parsed_url = parse_url(base_url());
        return(isset($parsed_url['scheme']) ? $parsed_url['scheme'] . '://' : '') .
                (isset($parsed_url['host']) ? $parsed_url['host'] : '');
    }

}
if (!function_exists('currentUrlString')) {

    function currentUrlString() {
        $ci = & get_instance();
        return $ci->config->site_url($ci->router->fetch_class() . "/" . $ci->router->fetch_method());
    }

}
if (!function_exists('returnURL')) {

    function returnURL($urlCurrentOrNext = '') {
        $returnURL = '';
        if (isset($_COOKIE['returnURL'])) {
            $returnURL = $_COOKIE['returnURL'];
            unset($_COOKIE['returnURL']);
            setcookie('returnURL', '', 0, '/');
        }
        if ($returnURL == ''):
            $returnURL = $urlCurrentOrNext;
        endif;
        return $returnURL;
    }

}

if (!function_exists('backURL')) {

    function backURL() {
        $ci = & get_instance();
        $ci->load->library('user_agent');
        if ($ci->agent->is_referral()) {
            return $ci->agent->referrer();
        } else {
            return url();
        }
    }

}

if (!function_exists('url')) {

    function url($path = NULL) {
        $url = '';
        if ($path != NULL) {
            $url .= $path;
        }
        return base_url($url);
    }

}

if (!function_exists('backendURL')) {

    function backendURL($path = NULL) {
        $ci = & get_instance();
        return $ci->config->item('backendURL') . $path;
    }

}

if (!function_exists('frontendURL')) {

    function frontendURL($path = NULL) {
        $ci = & get_instance();
        return $ci->config->item('frontendURL') . $path;
    }

}
if (!function_exists('apiURL')) {

    function apiURL($path = NULL) {
        $ci = & get_instance();
        return $ci->config->item('apiURL') . $path;
    }

}
if (!function_exists('makeSlugName')) {

    function makeSlugName($slugName) {
        $specialChars = [' - ', ' -', '- ', '.', ' , ', ', ', ' ,', ',', ' / ', ' /', '/ ', '/'];
        foreach ($specialChars as $key => $specialChar) {
            $slugName = str_replace($specialChar, '-', $slugName);
        }
        return strtolower(str_replace(' ', '-', $slugName));
    }

}
if (!function_exists('makeSlugURL')) {

    function makeSlugURL($url, $name, $id) {
        $slugName = $slugId = '';
        if (isArray($name)) {
            foreach ($name as $k => $v) {
                $slugName .= ($slugName) ? '-' . trim($v) : trim($v);
            }
        } else {
            $slugName = trim($name);
        }
        if (isArray($id)) {
            foreach ($id as $k2 => $v2) {
                $slugId .= ($slugId) ? '-' . trim($v2) : trim($v2);
            }
        } else {
            $slugId = trim($id);
        }
        unset($name);
        unset($id);
        return $url . '/' . makeSlugName($slugName) . '---' . $slugId;
    }

}

if (!function_exists('slugId')) {

    function slugId($n = null) {
        $ci = & get_instance();
        $n = ($n) ? $n : $ci->uri->total_segments();
        $slugId = end((explode('---', $ci->uri->segment($n))));
        return $slugId;
    }

}
if (!function_exists('slugName')) {

    function slugName($n = null) {
        $ci = & get_instance();
        $n = ($n) ? $n : $ci->uri->total_segments();
        $slugName = (explode('---', $ci->uri->segment($n)));
        return $slugName[0];
    }

}



if (!function_exists('makeHashUrl')) {

    function makeHashUrl($url, $dataArrayOrObject) {
        if (isObject($dataArrayOrObject)) {
            $dataArrayOrObject = (array) $dataArrayOrObject;
        }
        $ci = & get_instance();
        $dataArrayOrObject = (string) base64_encode(json_encode($dataArrayOrObject));
        return $url . '/' . urlencode($dataArrayOrObject);
    }

}

if (!function_exists('verifyHashUrl')) {

    function verifyHashUrl($verficationHash) {
        $ci = & get_instance();
        return (array) json_decode(base64_decode(urldecode($verficationHash)));
    }

}


if (!function_exists('css')) {

    function css($url) {
        $scheme = parse_url($url, PHP_URL_SCHEME);
        $ci = & get_instance();
        $extention = (end((explode('.', $url))) == 'css') ? '' : '.css';
        if ($scheme == 'http' || $scheme == 'https') {
            return '<link href="' . $url . '" rel="stylesheet"></link>';
        }
        return '<link href="' . $ci->config->item('base_url') . 'public/assets/' . $url . $extention . '" rel="stylesheet" />';
    }

}

if (!function_exists('js')) {

    function js($url) {
        $scheme = parse_url($url, PHP_URL_SCHEME);
        $ci = & get_instance();
        $extention = (end((explode('.', $url))) == 'js') ? '' : '.js';
        if ($scheme == 'http' || $scheme == 'https') {
            return '<script src="' . $url . '"></script>';
        }
        return '<script src="' . $ci->config->item('base_url') . 'public/assets/' . $url . $extention . '"></script>';
    }

}

if (!function_exists('assets')) {

    function assets($url = NULL) {
        $ci = & get_instance();
        return (trim($url) == NULL) ? $ci->config->item('base_url') . 'public/assets/' : $ci->config->item('base_url') . 'public/assets/' . $url;
    }

}
if (!function_exists('upload')) {

    function upload($url = NULL) {
        $ci = & get_instance();
        return (trim($url) == NULL) ? $ci->config->item('base_url') : $ci->config->item('base_url') . $url;
    }

}

if (!function_exists('favicon')) {

    function favicon($url) {
        $ci = & get_instance();
        return '<link rel="shortcut icon" type="image/x-icon" href="' . $ci->config->item('base_url') . 'public/assets/' . $url . '"/>';
    }

}

if (!function_exists('image')) {

    function image($relativePath, $alt = "", $extra = []) {
        $ci = & get_instance();
        $properties = "";
        if (isArray($extra)) {
            foreach ($extra as $k => $v) {
                $properties .= '$k="' . $v . '" ';
            }
        }
        return '<img ' . (($alt != "" && $alt != NULL ) ? 'alt="' . $alt . '"' : "") . ' src="' . $ci->config->item('base_url') . 'public/assets/' . $relativePath . '" ' . $properties . '/>';
    }

}
if (!function_exists('serverImage')) {

    function serverImage($relativePath, $alt = "", $extra = []) {
        $ci = & get_instance();
        $properties = "";
        if (isArray($extra)) {
            foreach ($extra as $k => $v) {
                $properties .= '$k="' . $v . '" ';
            }
        }
        return '<img ' . (($alt != "" && $alt != NULL ) ? 'alt="' . $alt . '"' : "") . ' src="' . $ci->config->item('base_url') . 'public/assets/' . $relativePath . '" ' . $properties . '/>';
    }

}
if (!function_exists('title')) {

    function title($title) {
        return '<title>' . $title . '</title>';
    }

}

if (!function_exists('currentClass')) {

    function currentClass() {
        $ci = & get_instance();
        return underscoreToDash(strtolower($ci->router->directory . $ci->router->fetch_class()));
    }

}
if (!function_exists('currentMethod')) {

    function currentMethod() {
        $ci = & get_instance();
        return underscoreToDash(strtolower($ci->router->fetch_method()));
    }

}

if (!function_exists('currentClassAndMethod')) {


    function currentClassAndMethod() {
        $ci = & get_instance();
        return underscoreToDash(strtolower($ci->router->directory . $ci->router->fetch_class() . "/" . $ci->router->fetch_method()));
    }

}
if (!function_exists('currentActiveParentNode')) {

    function currentActiveParentNode($className) {
        return (currentClass() == $className) ? "show" : "";
    }

}
if (!function_exists('currentActiveParentChildNode')) {

    function currentActiveParentChildNode($ParentAndChildName) {
        return (currentClassAndMethod() == $ParentAndChildName) ? " sub-m-active active" : "";
    }

}
if (!function_exists('currentActiveStaticNode')) {

    function currentActiveStaticNode($ParentAndChildName) {
        return (uri_string() == $ParentAndChildName) ? " active" : "";
    }

}

if (!function_exists('ipaddress')) {

    function ipaddress() {
        $ci = & get_instance();
        return $ci->input->ip_address();
    }

}

if (!function_exists('user_ipaddress')) {

    function user_ipaddress() {
        $ipaddresses = ['::1','**************'];
        return $ipaddresses;
    }

}