<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class GoogletokenModel extends MY_Model {

    protected $primaryTable = 'tb_google_token';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
	
	public function get_all_data() {
        return $this->db->get($this->primaryTable)->result();  // Replace 'your_table_name' with your actual table name
    }
	
	public function update_token( $new_token,$refresh_token) {
        $data = array(
            'token' => $new_token,
			'refresh_token' => $refresh_token,
            'created_at' => date('Y-m-d H:i:s') 
        );

        return $this->db->update($this->primaryTable, $data);
    }
}
