<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class TruecallerModel extends MY_Model {

    protected $primaryTable = 'tb_truecaller_data';
    protected $primaryTablePK = 'id';

    function __construct() {
        parent::__construct($this->primaryTable, $this->primaryTablePK);
    }
    
    function export($type,$where = '',$limit='') { 
		if($type=='excel') {
            $this->db->select("
			    t1.mobile,
				t1.name,
				t1.gender,
				t1.altName,
				t1.about,
				JSON_EXTRACT(t1.internetAddress, '$[0].id[0]') as email,
				JSON_EXTRACT(t1.address, '$[0].city[0]') as state,
				JSON_EXTRACT(t1.address, '$[0].countryCode[0]') as country,
				JSON_EXTRACT(t1.address, '$[0].address[0]') as address,
				CASE WHEN t1.spam_description = '[]' THEN 'No' ELSE 'Yes' END AS Spam")
                ->from($this->primaryTable . ' t1');

			if ($where != '') {
				$this->db->where($where);
			}

            $this->db->order_by('id', 'desc');

			if ($limit != '') {
                $this->db->limit($limit);
            }

			$res = $this->getResult($this->db->get());

			//$res = $query->result_array(); // Get result as an array
		}else{

			$this->db->select("t1.mobile,
				t1.name,
				t1.gender,
				t1.altName,
				t1.about,
				JSON_EXTRACT(t1.internetAddress, '$[0].id[0]') as email,
				JSON_EXTRACT(t1.address, '$[0].city[0]') as state,
				JSON_EXTRACT(t1.address, '$[0].countryCode[0]') as country,
				JSON_EXTRACT(t1.address, '$[0].address[0]') as address,
				CASE WHEN t1.spam_description = '[]' THEN 'No' ELSE 'Yes' END AS Spam")->from($this->primaryTable . ' t1');
			if ($where != '') {
				$this->db->where($where);
			}
			$this->db->order_by('id', 'desc');
			if ($limit != '') {
                $this->db->limit($limit);
            }
			$res = $this->getResult($this->db->get());
		}
        return $res;        
    }

}
