<?php

class Send_notifications extends InSecureController
{
    function __construct()
    {
        parent::__construct();
    }
    function broadcast()
    {
        $this->load->model('broadcastMsgModel');
        $cdt = currentDateTime();
        $where = ['startDateTime <' => $cdt, 'endDateTime >' => $cdt, 'isStatus' => 'active'];
        $broadcastMsg = $this->broadcastMsgModel->find($where, 'title,message,url,image');
        $this->load->model('userModel');
        $usersDeviceIds = $this->userModel->find(['deviceId !=' => NULL], 'deviceId');
        $this->load->library("googlefcm");
        foreach($broadcastMsg as $key => $broadcast){
            foreach($usersDeviceIds as $deviceId){
             $this->googlefcm->send($deviceId->deviceId,$broadcast->title, $broadcast->message, "Broadcasr-msg", NULL, NULL,$broadcast->url,IMAGE_BASE_URL.$broadcast->image);
            }
        }
    }
    function user_account_expired()
    {
        $cdt = currentDateTime();
        $where = ['expiryDate <' => $cdt,'isStatus' => 'active','deviceId !=' => NULL];
        $this->load->model('userModel');
        $usersDeviceIds = $this->userModel->find($where, 'deviceId');
        dd($usersDeviceIds);
        $this->load->library("googlefcm");
        $message = "Your Ip Tracking Account Expaired. Please Renew To Continue...!";
            foreach($usersDeviceIds as $deviceId){
                $this->googlefcm->send($deviceId->deviceId, $message, "User Account Expaired", NULL, NULL,NULL,NULL);
        }
    }
}
