<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class ReleaseNotes extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('releaseNotesModel');
    }

    function takeAction()
    {
        $status = $this->input->post('status');
        $execute = false;
        if ($status == "delete") {
            $this->releaseNotesModel->detach(['id' => $this->input->post('id')]);
            $execute = true;
        } else {
            $this->releaseNotesModel->modify(['isStatus' => $status], ['id' => $this->input->post('id')]);
            $execute = true;
        }
        if ($execute) {
            unset($where);
            unset($updateData);
            $this->exitSuccess();
        } else {
            unset($where);
            unset($updateData);
            $this->exitDanger();
        }
    }

    function search()
    {
        $searchItem = $this->input->post('searchItem');
        $where = "";
        if ($searchItem != '' && $searchItem != null) {
            $where .= ' version like "%' . $searchItem . '%" || message like "%' . $searchItem . '%" ';
        }
        if ($where != '') {
            $this->load->model('releaseNotesModel');
            $users = $this->releaseNotesModel->find($where, "id,version,message");
            $this->exitWithSuccess($this->load->view('release_notes/ajax/listView', ["users" => $users, "pagination" => ''], true));
        } else {
            $this->exitWithDanger();
        }
    }

}
