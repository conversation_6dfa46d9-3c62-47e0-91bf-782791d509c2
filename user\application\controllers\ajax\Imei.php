<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class imei extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
    }

    function companyName()
    {
        $this->form_validation->set_rules('ImeiNumber', 'ImeiNumber', 'trim|required|xss_clean');
        if ($this->form_validation->run() === true):
        	$name = $this->input->post('ImeiNumber');
			$data = [];
			$company = 'N/A';
        	//$curlPost = "IMEI=".$name;
			$curlPost = json_encode(["IMEI"=>$name]);
            $authToken = $this->viewDataBag->userSession->auth_token;
            $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetCompanyName');
            curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
            curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
            $headers = array();
            $headers[] = 'Authorization: Bearer '.$authToken;
            $headers[] = 'Cache-Control: no-cache';
			$headers[] = 'Content-Type: application/json';
            curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($cURLConnection);
            if (curl_errno($cURLConnection)) {
                echo 'Error:' . curl_error($cURLConnection);
            }
            curl_close($cURLConnection);
            $imeiData = json_decode($result);
			$status = $imeiData->StatusCode;
			if($status==1) {
				$data = $imeiData->Data[0];
				$company = $data->CompanyName;
			}
            return $this->exitSuccessWithOne(['status'=>$status,'data' => $data,'company'=>$company]);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
}
