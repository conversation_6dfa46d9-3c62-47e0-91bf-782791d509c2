<?php

defined('BASEPATH') OR exit('No direct script access allowed');
set_time_limit(0);
class Gst extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$user_id = $this->viewDataBag->userSession->id;
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
		
    }

    function search(){
		$emailData = [];
		$this->viewDataBag->gsts = $emailData;
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js")];
        $this->viewDataBag->jsView = 'gst/jsView';
        $this->loadView('gst/searchView', $this->viewDataBag);
    }
	function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $user_id = $this->viewDataBag->userSession->id;
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.gst like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;
        $this->load->model('gstModel');
        $totalRows = $this->gstModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $this->viewDataBag->users = $this->gstModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        // $this->viewDataBag->pagination = $this->pagination->create_links();
        $this->viewDataBag->jsView = 'gst/jsView';
        $this->loadView('gst/listView', $this->viewDataBag);
    }
	function detail($slug)
    {
        $id = slugId();
        if ($id) {
            $this->load->model('gstModel');
            $value = $this->gstModel->findOne(['id' => $id], '*');
            if (isObject($value)) {
				$result = $result_vi = $result_id = '';
				if(isset($value->response) && $value->status){
					// Array to hold extracted data
					$data = [];

					// Define regex patterns for each field
					$patterns = [
						'GSTIN Status' => '/GSTIN Status:\s*([^\n]+)/',
						'Legal Name Of Business' => '/Legal Name Of Business:\s*([^\n]+)/',
						'Constitution Of Business' => '/Constitution Of Business:\s*([^\n]+)/',
						'Date Of Registration' => '/Date Of Registration:\s*([^\n]+)/',
						'Nature Of Business Acitivty' => '/Nature Of Business Acitivty:\s*([^\n]+)/',
						'Place Of Business' => '/Place Of Business:\s*([^\n]+)/',
						'Trade Name' => '/Trade Name:\s*([^\n]+)/',
						'Taxpayer Type' => '/Taxpayer Type:\s*([^\n]+)/',
						'Administrative Office' => '/Administrative Office:\s*([^\n]+)/',
						'Centre Jurisdiction' => '/Centre Jurisdiction:\s*([^\n]+)/',
						'Date of Cancellation' => '/Date of Cancellation\s*([^\n]+)/',
						'GSTIN' => '/GSTIN:\s*([^\n]+)/',
						'Source' => '/Source:\s*([^\n]+)/',
						'State Jurisdiction Code' => '/State Jurisdiction Code:\s*([^\n]+)/'
					];
					
					$result = "<p></p><h3>GST Number Information</h3><p>";						
					$result = $this->getDetails($patterns,$value->response,$result);
					$result .= "</p>";
				}else{
					$this->viewDataBag->response = $value->response;
				}
				 
				$this->viewDataBag->pi = $result;
				$this->viewDataBag->res_status = $value->status;
				$this->viewDataBag->gsts = $value->gst;
                $this->viewDataBag->jsView = 'gst/jsView';
                $this->loadView('gst/detailView', $this->viewDataBag);
            } else {
                return show_404();
            }
        } else {
            return show_404();
        }
    }
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
}