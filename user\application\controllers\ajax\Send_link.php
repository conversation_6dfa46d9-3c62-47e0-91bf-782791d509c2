<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Send_link extends AjaxSecureController
{
    function __construct()
    {
        parent::__construct();
    }

    function getSenderIds()
    {
        $this->load->library('curl');
        $response = (array)$this->curl->get(apiURL('send_link/sender-ids'));
        return $this->exitOnly($response);
    }

    function by_sms()
    {
        $this->form_validation->set_rules('senderId', 'sender id', 'trim|required|xss_clean');
        $this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');
        $this->form_validation->set_rules('customizedLink', 'customized link', 'trim|required|xss_clean');
        if ($this->form_validation->run() === true):
            $post = $this->input->post();
            $userSession = $this->auth->user();
            $post['userId'] = $userSession->id;
            $this->load->library('curl');
            $returnData = $this->curl->post(apiURL('send_link/by-sms'), $post);
            return $this->exitOnly($returnData);
        else:
            return $this->exitDangerWithValidation($this->form_validation->errorArray());
        endif;
    }
}
