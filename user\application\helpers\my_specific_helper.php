<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

if (!function_exists('userAgent')) {

    /* history for update */

    function userAgent($oldHistory, $userId, $when) {
        $ci = &get_instance();
        $ci->load->library('user_agent');

        $history = [];
        if ($oldHistory != "" && $oldHistory != NULL) {
            $history = (array) json_decode($oldHistory);
        }
        if (count($history) >= MAXIMUM_LOGIN_HISTORY) {
            array_shift($history);
        }
        $userAgent = (object) $oldHistory;
        array_push($history, (object) [
                    "ip" => $ci->input->ip_address(),
                    "browser" => $ci->agent->browser(),
                    "version" => $ci->agent->version(),
                    "platform" => $ci->agent->platform(),
                    "id" => $userId,
                    "when" => $when,
                    "dateTime" => currentDateTime()
        ]);
        return (string) json_encode($history);
    }

}

if (!function_exists('userInfo')) {

    function userInfo() {
        $ci = &get_instance();
        $ci->load->library('user_agent');

        $history = [
            "ip" => $ci->input->ip_address(),
            "browser" => $ci->agent->browser(),
            "version" => $ci->agent->version(),
            "platform" => $ci->agent->platform(),
            "when" => 'login',
        ];
        return (string) json_encode($history);
    }

}

if (!function_exists('getSetProfileImage')) {

    function getSetProfileImage($imagePath = '') {
        $ci = & get_instance();
        return (isset($imagePath) && $imagePath != '' && (!is_null($imagePath))) ? IMAGE_BASE_URL . $imagePath : assets('images/user/male.png');
    }

}
if (!function_exists('lastLogin')) {

    function lastLogin($lastLogin = '') {
        $lastLogin = json_decode($lastLogin);
        return (isset($lastLogin) && $lastLogin != '' && (!is_null($lastLogin))) ? end($lastLogin) : 'Data Not Found';
    }

}

if (!function_exists('getHeader')) {
    function getHeader($url)
    {
        $returnData = ["favicon"=>"","description"=>"","title"=>""];
        $content = @file_get_contents($url);

        if(!empty($content)){
            $doc = new DOMDocument();
            $doc->strictErrorChecking = FALSE;
			$content = mb_convert_encoding($content, 'HTML-ENTITIES', "UTF-8");
            @$doc->loadHTML($content);
            $title = $doc->getElementsByTagName('title');
            $returnData["title"]= $title->item(0)->nodeValue;
            $metaNodeList = $doc->getElementsByTagName('meta');
            $description="";
            foreach($metaNodeList as $metaNode){
                if($metaNode->getAttribute('name')=="description"){
                    $description=$metaNode->getAttribute('content');
                    break;
                }
            }
            $returnData["description"]=$description;
            $linkNodeList = $doc->getElementsByTagName('link');
            $favicon="";
            foreach($linkNodeList as $linkNode){
                if($linkNode->getAttribute('rel')=="image_src"){
                     $favicon=$linkNode->getAttribute('href');
                    break;
                }else if($linkNode->getAttribute('rel')=="shortcut icon"){
                    $favicon=$linkNode->getAttribute('href');
                    break;
                }else if($linkNode->getAttribute('rel')=="icon"){
                    $favicon=$linkNode->getAttribute('href');
                    break;
                }
            }
            if(!empty($favicon) && empty(parse_url($favicon, PHP_URL_HOST))){
                $urlList=parse_url($url);
                $favicon=$urlList["scheme"]."://".$urlList["host"].$favicon;
            }

            $returnData["favicon"]=$favicon;      
        }
        return $returnData;
    }
}
