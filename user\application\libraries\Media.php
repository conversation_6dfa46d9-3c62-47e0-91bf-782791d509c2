<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Media {

    private $ci;

    function __construct() {
        $this->ci = & get_instance();
    }

    function uploadImage($fileName, $uploadDirectory, $configSetting = []) {
        /* #start file uploading */
        $returnDataArray = ['status' => true,
            'error' => '',
            'filePath' => ''];
        if (isset($_FILES[$fileName]) && !empty($_FILES[$fileName]['name'])) {
            $uploadDirectory = spaceToDash(strtolower($uploadDirectory));
            $targetDirectory = './' . $uploadDirectory;
            /* #create file uploading directory */
            makeDirectory($targetDirectory);
            /* #seting for uploading file */
            $config = array(
                'upload_path' => $targetDirectory,
                'allowed_types' => "gif|jpg|png|jpeg",
                'overwrite' => false,
                'max_size' => 2048, // Can be set to particular file size
                'encrypt_name' => true,
                'remove_spaces' => true,
            );
            if (isArray($configSetting)) {
                foreach ($configSetting as $key => $value) {
                    $config[$key] = $value;
                }
                unset($configSetting);
            }

            $this->ci->upload->initialize($config);

            if ($this->ci->upload->do_upload($fileName)) {
                $uploadData = $this->ci->upload->data();
                $returnDataArray['filePath'] = ltrim($uploadDirectory . $uploadData['file_name'], '/');
//                $returnDataArray['fileName'] = $uploadData['orig_name'];
//                $returnDataArray['fileDirectory'] = $uploadData['file_path'];
            } else {
                $returnDataArray['status'] = false;
                $returnDataArray['error'] = $this->ci->upload->display_errors();
            }
        }
        return $returnDataArray;
        /* #end file uploading */
    }

    function resizeImage($imageSource, $width, $height, $imageSourceNewDestination = '') {
        $this->ci->load->library('image_lib');
        $config['image_library'] = 'gd2';
        $config['source_image'] = $imageSource;
        $config['maintain_ratio'] = TRUE;
        $config['width'] = $width;
        $config['height'] = $height;
        $config['file_permissions'] = 0755;
        $config['quality'] = "100%";

        if ($imageSourceNewDestination != '') {
            $imageSourceNewDestination = spaceToDash(strtolower($imageSourceNewDestination));
            makeDirectory('./' . $imageSourceNewDestination);
            $imageSourceNewDestination = ltrim($imageSourceNewDestination, '/') . random_string('alnum', 8) . @end((explode('/', $imageSource)));
            $config['new_image'] = $imageSourceNewDestination;
        } else {
            $imageSourceNewDestination = $imageSource;
        }
        $this->ci->image_lib->clear();
        $this->ci->image_lib->initialize($config);
        if (!$this->ci->image_lib->resize()) {

            return ['status' => false,
                'error' => $this->ci->image_lib->display_errors(),
                'filePath' => $imageSourceNewDestination];
        } else {
            return ['status' => true,
                'error' => 'OK',
                'filePath' => $imageSourceNewDestination];
        }
    }

}

?>