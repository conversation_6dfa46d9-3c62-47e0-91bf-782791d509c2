<?php

defined('BASEPATH') OR exit('No direct script access allowed');

    class TollFree extends SecureController {

        function __construct() {
            parent::__construct();
			$res = accessModule('ip_grabber');
			if (empty($res) ) {
				return redirectTo("me/dashboard");
			}
        }

        public function all($offset = 0){
            $tollFreeNum = $this->input->get('tollfreenum');
            //$imeiNumber = substr($name,0,8);
            //$imeiData = companyInfo($imeiNumber);
            //$curlPost = "TallFreeNumber=".$tollFreeNum;
			 $curlPost = json_encode(["TallFreeNumber"=>$tollFreeNum]);
            $authToken = $this->viewDataBag->userSession->auth_token;
            $cURLConnection = curl_init('https://mobileapi.lisw.in/api/Tac/GetTollFreeCodeInfoByNumber');
            curl_setopt($cURLConnection, CURLOPT_POSTFIELDS, $curlPost);
            curl_setopt($cURLConnection, CURLOPT_RETURNTRANSFER, true);
            $headers = array();
            $headers[] = 'Authorization: Bearer '.$authToken;
            $headers[] = 'Cache-Control: no-cache';
			$headers[] = 'Content-Type: application/json';
            curl_setopt($cURLConnection, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($cURLConnection);
            if (curl_errno($cURLConnection)) {
                echo 'Error:' . curl_error($cURLConnection);
            }
            curl_close($cURLConnection);
            $tollFreedata = json_decode($result);
            $this->viewDataBag->response = $tollFreedata->Data;
            $this->viewDataBag->jsView = 'tollFree/jsView';
            $this->loadView('tollFree/listView', $this->viewDataBag);
        }

   }