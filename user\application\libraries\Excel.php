<?php

set_include_path(APPPATH . "third_party/Excel2PHP/");
include_once 'PHPExcel/IOFactory.php';
/** PHPExcel */
include_once 'PHPExcel.php';
/** PHPExcel_Writer_Excel2007 */
include_once 'PHPExcel/Writer/Excel2007.php';

class Excel {

    private $ci;
    private $objPHPExcel;

    function __construct() {
        $this->ci = &get_instance();
        $this->objPHPExcel = new PHPExcel();
    }

    function readAndGetResultArray($uplopadFileName, $noOfColumn, $preDefinedHeaderArray) {
        if (isset($_FILES[$uplopadFileName]) && $_FILES[$uplopadFileName]["size"] > 0) {
            if (pathinfo($_FILES[$uplopadFileName]["name"], PATHINFO_EXTENSION) != "xlsx") {
                return ['status' => false,
                    'error' => 'Only xlsx files are allowed.',
                    'data' => ''];
            }
            // Check file size
            if ($_FILES[$uplopadFileName]["size"] > 5000000) {
                $this->alert->danger('your file is too large. Uploaded excel file max size is 5MB');
                return ['status' => false,
                    'error' => 'your file is too large. Uploaded excel file max size is 5MB.',
                    'data' => ''];
            }

            $inputFileName = $_FILES[$uplopadFileName]["tmp_name"];
            $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
            $objReader = PHPExcel_IOFactory::createReader($inputFileType);
            $objReader->getIncludeCharts(true);
            $this->objPHPExcel = $objReader->load($inputFileName);
            $objWorksheet = $this->objPHPExcel->getActiveSheet();
            $colSize = PHPExcel_Cell::columnIndexFromString($this->objPHPExcel->setActiveSheetIndex(0)->getHighestColumn());
            if ($colSize < $noOfColumn) {
                return ['status' => false,
                    'error' => 'Uploaded excel file is corrupted. Required columns are missing.',
                    'data' => ''];
            } else if ($colSize > $noOfColumn) {
                return ['status' => false,
                    'error' => 'Uploaded excel file is invalid. Contaning unknown columns.',
                    'data' => ''];
            }
            $i = 0;
            $sheetData = [];

            foreach ($objWorksheet->getRowIterator() as $row) {
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false);
                $j = 0;
                $sheetTempData = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    /// $value = htmlentities($value);
                    //$value = nl2br($value);
                    if ($cell->hasHyperlink()) {
                        $value = '<a href="' . $cell->getHyperlink()->getUrl() . '" target="_blank">' . nl2br(htmlentities($cell->getValue())) . '</a>';
                    }
                    if ($i == 0 && !in_array($value, $preDefinedHeaderArray)) {
//                        return ['status' => false,
//                            'error' => 'Uploaded excel file contaning invalid column (' . $value . ').',
//                            'data' => ''];
                    } elseif ($i > 0) {
                        $sheetTempData[$preDefinedHeaderArray[$j]] = $value;
                        $j++;
                    }
                }
                if ($i > 0) {
                    $sheetData[$i - 1] = $sheetTempData;
                }
                $i++;
            }
            return ['status' => true,
                'error' => 'OK',
                'data' => $sheetData];
        }
    }

    function writeAndDownload($contents, $fileName = '') {
        error_reporting(E_ALL & ~E_NOTICE);
        PHPExcel_Settings::setZipClass(PHPExcel_Settings::PCLZIP);
        $this->objPHPExcel->setActiveSheetIndex(0);

        if (isArray($contents)) {
            foreach ($contents as $key => $content) {
                $char = 65;
                if ($key == 0) {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), 'S.No');
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
                    $char++;
                    foreach ($content as $k => $v) {
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), strtoupper(camelCaseToString($k)));
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
                        $char++;
                    }
                } else {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
                    $char++;
                    foreach ($content as $k => $v) {
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
                        $char++;
                    }
                }
            }
        }

        // Rename sheet
        $this->objPHPExcel->getActiveSheet()->setTitle($fileName);

        $objWriter = new PHPExcel_Writer_Excel2007($this->objPHPExcel);

        ob_end_clean();
        header('Content-type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $fileName . "-" . time() . '.xlsx"');
        header('Cache-Control: max-age=0'); //no cache
        $objWriter = PHPExcel_IOFactory::createWriter($this->objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
        return;
    }

    function customDownload($contents, $fileName = '') {
        error_reporting(E_ALL & ~E_NOTICE);
        PHPExcel_Settings::setZipClass(PHPExcel_Settings::PCLZIP);
        $this->objPHPExcel->setActiveSheetIndex(0);

        // Setting font to Arial Black
        $this->objPHPExcel->getDefaultStyle()->getFont()->setName('Arial');
        
        // Setting font size to 10
        $this->objPHPExcel->getDefaultStyle()->getFont()->setSize(10);

        $this->objPHPExcel->getActiveSheet()->SetCellValue('A1', 'S.No');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Mobile');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Name');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Gender');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('E1', 'Alt Name');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('F1', 'About');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('G1', 'Email');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('H1', 'State');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Country');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('J1', 'Address');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('K1', 'Spam');

        $styleArray = array(
            'font'  => array(
                'bold'  => true,
                'color' => array('rgb' => 'FFFFFF'),
                'size'  => 10,
                'name'  => 'Verdana'
            )
        );
        $this->objPHPExcel->getActiveSheet()->getStyle("B1:K1")->applyFromArray($styleArray);
        //$this->objPHPExcel->getActiveSheet()->getStyle("A1:G1")->getFont()->setBold(true);
        //$this->objPHPExcel->getActiveSheet()->getStyle("A1:G1")->getFont()->setColor(PHPExcel_Style_Color::COLOR_WHITE);
        $this->objPHPExcel->getActiveSheet()->getStyle("B1:K1")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_PATTERN_LIGHTUP)->getStartColor()->setRGB('0000FF');

        $this->objPHPExcel->getActiveSheet()->getColumnDimension('A')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('B')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('C')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('D')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('E')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('F')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('G')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('H')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('I')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('J')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('K')->setAutoSize(true);
		
        if (isArray($contents)) {
            foreach ($contents as $key => $content) {
                $char = 65;
                if ($key == 0) {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), 'S.No');
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
                    $char++;
                    foreach ($content as $k => $v) {
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), strtoupper(camelCaseToString($k)));
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
                        $char++;
                    }
                } else {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
                    $char++;
                    foreach ($content as $k => $v) {
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
                        $char++;
                    }
                }
            }
			$this->objPHPExcel->getActiveSheet()->setTitle($fileName);

			$objWriter = new PHPExcel_Writer_Excel2007($this->objPHPExcel);

			ob_end_clean();
			header('Content-type: application/vnd.ms-excel');
			header('Content-Disposition: attachment; filename="' . $fileName . "-" . time() . '.xlsx"');
			header('Cache-Control: max-age=0'); //no cache
			$objWriter = PHPExcel_IOFactory::createWriter($this->objPHPExcel, 'Excel2007');
			$objWriter->save('php://output');
        }
		
		//dd($this->objPHPExcel->getActiveSheet());
        // Rename sheet
        exit;
    }
	
	function customCellIdDownload($contents, $fileName = '') {
        error_reporting(E_ALL & ~E_NOTICE);
        PHPExcel_Settings::setZipClass(PHPExcel_Settings::PCLZIP);
        $this->objPHPExcel->setActiveSheetIndex(0);

        // Setting font to Arial Black
        $this->objPHPExcel->getDefaultStyle()->getFont()->setName('Arial');
        
        // Setting font size to 10
        $this->objPHPExcel->getDefaultStyle()->getFont()->setSize(10);

        $this->objPHPExcel->getActiveSheet()->SetCellValue('A1', 'S.No');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('B1', 'CGI');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('C1', 'CellId Code');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Latitude');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('E1', 'Longitude');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('F1', 'Site Address');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('G1', 'City');
        $this->objPHPExcel->getActiveSheet()->SetCellValue('H1', 'TM Remark');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Operator');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('J1', 'Circle');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('K1', 'Upload Date');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('L1', 'Unique Id');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('M1', 'MCC');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('N1', 'MNC');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('O1', 'LAC');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('P1', 'SAzimuth');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('Q1', 'EAzimuth');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('R1', 'CellID Code WLAC');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('S1', 'Type1');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('T1', 'Azimooth');
		$this->objPHPExcel->getActiveSheet()->SetCellValue('U1', 'Show Map');

        $styleArray = array(
            'font'  => array(
                'bold'  => true,
                'color' => array('rgb' => '000000'),
                'size'  => 10,
                'name'  => 'Verdana'
            )
        );
       $this->objPHPExcel->getActiveSheet()->getStyle("A1:U1")->applyFromArray($styleArray);
        $this->objPHPExcel->getActiveSheet()->getStyle("A1:U1")->getFont()->setBold(true);
        //$this->objPHPExcel->getActiveSheet()->getStyle("A1:T1")->getFont()->setColor(PHPExcel_Style_Color::COLOR_WHITE);
        //$this->objPHPExcel->getActiveSheet()->getStyle("A1:T1")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_PATTERN_LIGHTUP)->getStartColor();

        $this->objPHPExcel->getActiveSheet()->getColumnDimension('A')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('B')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('C')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('D')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('E')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('F')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('G')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('H')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('I')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('J')->setAutoSize(true);
        $this->objPHPExcel->getActiveSheet()->getColumnDimension('K')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('L')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('M')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('N')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('O')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('P')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('Q')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('R')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('S')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('T')->setAutoSize(true);
		$this->objPHPExcel->getActiveSheet()->getColumnDimension('U')->setAutoSize(true);
		
        if (isArray($contents)) {
            foreach ($contents as $key => $content) {
                $char = 65;
                if ($key == 0) {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), 'S.No');
                   	$this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
                    $char++;
                    foreach ($content as $k => $v) {
                        //$this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 1), strtoupper(camelCaseToString($k)));
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
						$lastcell = chr($char) . ($key + 2);
						$lastval = $v;						
                        $char++;
                    }
					if(isset($lastcell)){
						$this->objPHPExcel->getActiveSheet()->getCell($lastcell)->setValue('Open Map');
						$this->objPHPExcel->getActiveSheet()->getCell($lastcell)->getHyperlink()->setUrl($lastval);
					}
                } else {
                    $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $key + 1);
					$lastcell = chr($char) . ($key + 2);
					$lastval =  $key + 1;
                    $char++;
                    foreach ($content as $k => $v) {
                        $this->objPHPExcel->getActiveSheet()->SetCellValue(chr($char) . ($key + 2), $v);
						$lastcell = chr($char) . ($key + 2);
						$lastval = $v;	
                        $char++;
                    }
					if(isset($lastcell)){
						$this->objPHPExcel->getActiveSheet()->getCell($lastcell)->setValue('Open Map');
						$this->objPHPExcel->getActiveSheet()->getCell($lastcell)->getHyperlink()->setUrl($lastval);
					}

                }
            }
			$this->objPHPExcel->getActiveSheet()->setTitle($fileName);

			$objWriter = new PHPExcel_Writer_Excel2007($this->objPHPExcel);

			ob_end_clean();
			header('Content-type: application/vnd.ms-excel');
			header('Content-Disposition: attachment; filename="' . $fileName . "-" . time() . '.xlsx"');
			header('Cache-Control: max-age=0'); //no cache
			$objWriter = PHPExcel_IOFactory::createWriter($this->objPHPExcel, 'Excel2007');
			$objWriter->save('php://output');
        }
		
		//dd($this->objPHPExcel->getActiveSheet());
        // Rename sheet
        exit;
    }

    function getData($uplopadFileName, $noOfColumn, $preDefinedHeaderArray) {

        $inputFileName = $uplopadFileName;
        $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
        $objReader = PHPExcel_IOFactory::createReader($inputFileType);
        $objReader->getIncludeCharts(true);
        $this->objPHPExcel = $objReader->load($inputFileName);
        $objWorksheet = $this->objPHPExcel->getActiveSheet();
        $colSize = PHPExcel_Cell::columnIndexFromString($this->objPHPExcel->setActiveSheetIndex(0)->getHighestColumn());

        if ($colSize < $noOfColumn) {
            return [
                'status' => false,
                'error' => 'Uploaded excel file is corrupted. Required columns are missing.',
                'data' => ''
            ];
        } else if ($colSize > $noOfColumn) {
            return [
                'status' => false,
                'error' => 'Uploaded excel file is invalid. Contaning unknown columns.',
                'data' => ''
            ];
        }
        $i = 0;
        $sheetData = [];

        foreach ($objWorksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            $j = 0;
            $sheetTempData = [];
            foreach ($cellIterator as $key => $cell) {
                
                $value = $cell->getValue();
                if ($cell->hasHyperlink()) {
                    $value = '<a href="' . $cell->getHyperlink()->getUrl() . '" target="_blank">' . nl2br(htmlentities($cell->getValue())) . '</a>';
                }
                $sheetTempData = $value;
                //dd($sheetTempData);
                $j++;
                /*if ($i == 0 && !in_array($value, $preDefinedHeaderArray)) {

                } elseif ($i > 0) {
                    
                }*/
            }
            //dd($sheetTempData);
            if ($i > 0) {
                $sheetData[$i - 1] = $sheetTempData;
            }
            $i++;
        }
        return [
            'status' => true,
            'error' => 'OK',
            'data' => $sheetData
        ];
    }

    function customLink($contents, $fileName = '') {
        error_reporting(E_ALL & ~E_NOTICE);
        PHPExcel_Settings::setZipClass(PHPExcel_Settings::PCLZIP);
        $this->objPHPExcel->setActiveSheetIndex(0);
		$sheet = $this->objPHPExcel->getActiveSheet();
		
		// Set a tracking URL in a cell
		$trackingUrl = 'https://msg.ccas.in/api/token/53jje59203wrwrfds';
		//$this->objPHPExcel->getActiveSheet()->setCellValue('A1', $trackingUrl);
		// Set a hyperlink in a cell
        //$sheet->setCellValue('A1', 'Click here for tracking');
        //$sheet->getCell('A1')->getHyperlink()->setUrl($trackingUrl);
        //$sheet->getCell('A1')->getHyperlink()->setTooltip('Click to track the event');
		
		$sheet->getCell("P1")->setValue('=Hyperlink("https://msg.ccas.in/api/token/53jje59203wrwrfds","Sample")');

		// Insert an image into the spreadsheet
		
		/*$drawing = new PHPExcel_Worksheet_Drawing();
		$drawing->setName('Tracking Image');
		$drawing->setDescription('An image that serves as a tracking beacon');
		$drawing->setPath('https://msg.ccas.in/api/token/53jje59203wrwrfds'); // Path to your image file
		$drawing->setHeight(100); // Adjust the image height as needed
		$drawing->setCoordinates('B1'); // Cell where the image should be placed
		$drawing->setWorksheet($this->objPHPExcel->getActiveSheet());*/
        
        $this->objPHPExcel->getActiveSheet()->setTitle($fileName);

        $objWriter = new PHPExcel_Writer_Excel2007($this->objPHPExcel);

        ob_end_clean();
        header('Content-type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $fileName . "-" . time() . '.xlsx"');
        header('Cache-Control: max-age=0'); //no cache
        $objWriter = PHPExcel_IOFactory::createWriter($this->objPHPExcel, 'Excel2007');
		$objWriter->setIncludeCharts(TRUE);
        $objWriter->save('php://output');
        
        return ;
    }

}

?>