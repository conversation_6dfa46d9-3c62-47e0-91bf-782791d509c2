<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Releasenotes extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
    }
    
    function all($offset = 0)
    {
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->get('searchItem');
        $where = '';
        if ($searchItem != '') {
            $where .= '( version like "%' . $searchItem . '%" || message like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(release_date) >=  "' . $from . '" && DATE(release_date) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(release_date) >=  "' . $from . '" && DATE(release_date) <=  "' . $to . '")';
            endif;
        }

        if ($searchItem == '' && $from == '' && $to == '') {
            $where = [];
        }

        $perPage = 50;
        $this->load->model('releaseNotesModel');
        $totalRows = $this->releaseNotesModel->count($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;

        $this->viewDataBag->response = $this->releaseNotesModel->find($where, '*', ['orderBy' => ['id', "DESC"],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $this->load->library('pagination');
        $config['base_url'] = currentUrlString();
        $config['per_page'] = $perPage;
        $config['total_rows'] = $totalRows;
        $this->pagination->initialize($config);
        $this->viewDataBag->pagination = $this->pagination->get($offset, $perPage, $totalRows);
        $this->viewDataBag->jsView = 'release_notes/jsView';
        $this->loadView('release_notes/listView', $this->viewDataBag);
    }

}
