<?php
set_time_limit(0);
defined('BASEPATH') OR exit('No direct script access allowed');
class Gst extends AjaxSecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
		if (empty($res) ) {
            return redirectTo("me/dashboard");
        }
		$user_id = $this->viewDataBag->userSession->id;
		$users = $this->auth->userlimit($user_id);
		if(!$users || empty($users)){
			 return redirectTo("me/dashboard");
		}else{
			if($users['status']){
				$userlimit = $users['error'];
			}else{
				 return redirectTo("me/dashboard");
			}
		}
    }

    function search(){
		header('Content-Type: application/json');
		$user_id = $this->viewDataBag->userSession->id;
		$emailData = [];

        if ($this->input->method() === 'post'){
            $param = [];
                     
			$this->form_validation->set_rules('vehicle', 'vehicle', 'trim|required');
			$vehicle = $this->input->post("vehicle");
			
            if ($this->form_validation->run() === true) {
                
                try {
					$date = new DateTime("now");
                	$curr_date = $date->format('Y-m-d h:i:s');
					$check_date = $date->format('Y-m-d');
					$this->load->model('gstModel');
					$users = $this->auth->userlimit($user_id);
					if(!$users || empty($users)){
						return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
					}else{
						if($users['status']){
							$userlimit = $users['error'];
						}else{
							return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
						}
					}
						
					$truecallerDataCount = $this->gstModel->count(['userId' => $this->viewDataBag->userSession->id,'DATE(created_at)'=>$check_date]);
					
					if($truecallerDataCount >=$userlimit){
						return $this->exitDangerWithValidation("Daily limit over.Please try tomorrow");
					}
					
					$post = [
						"gst"=> $vehicle,
						"id"=>$user_id
					];
					$param = json_encode($post);
					log_message('error', 'gst$post'.$param);
					$authToken = $this->viewDataBag->userSession->auth_token;
					$curl = curl_init();
					$url = "https://eitem.in/vehicle/fetch-gst.php";					
					
					curl_setopt_array($curl, array(
						CURLOPT_URL => $url,
						CURLOPT_RETURNTRANSFER => true,
						CURLOPT_ENCODING => '',
						CURLOPT_MAXREDIRS => 10,
						CURLOPT_TIMEOUT => 0,
						CURLOPT_FOLLOWLOCATION => true,
						CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
						CURLOPT_CUSTOMREQUEST => 'POST',
						CURLOPT_SSL_VERIFYPEER => false,
						CURLOPT_SSL_VERIFYHOST => false,
					  	CURLOPT_POSTFIELDS =>$param,
					  	CURLOPT_HTTPHEADER => array(
							'Content-Type: application/json',
							'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
					  	),
					));

					$response = curl_exec($curl);
					log_message('error', 'gst$response'.$response);
					$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
					$responses = json_decode($response);
					curl_close($curl);
					if(isset($responses->response)){
						$dataInsert = $responses->response;
					}else{
						$dataInsert = '';
					}
					if(isset($responses->status)){
						$status = $responses->status;
					}else{
						$status = 'FAIL';
					}
					$inserData = [];
					$inserData = [
						'userId' => $this->viewDataBag->userSession->id,
						'response' => $dataInsert,
						'status' => $status,
						'gst' => $vehicle,
						'created_at' => $curr_date
					];

					$this->gstModel->attach($inserData);
					if(isset($responses->response) && $responses->status){
						// Array to hold extracted data
						$data = [];

						// Define regex patterns for each field
						$patterns = [
							'GSTIN Status' => '/GSTIN Status:\s*([^\n]+)/',
							'Legal Name Of Business' => '/Legal Name Of Business:\s*([^\n]+)/',
							'Constitution Of Business' => '/Constitution Of Business:\s*([^\n]+)/',
							'Date Of Registration' => '/Date Of Registration:\s*([^\n]+)/',
							'Nature Of Business Acitivty' => '/Nature Of Business Acitivty:\s*([^\n]+)/',
							'Place Of Business' => '/Place Of Business:\s*([^\n]+)/',
							'Trade Name' => '/Trade Name:\s*([^\n]+)/',
							'Taxpayer Type' => '/Taxpayer Type:\s*([^\n]+)/',
							'Administrative Office' => '/Administrative Office:\s*([^\n]+)/',
							'Centre Jurisdiction' => '/Centre Jurisdiction:\s*([^\n]+)/',
							'Date of Cancellation' => '/Date of Cancellation\s*([^\n]+)/',
							'GSTIN' => '/GSTIN:\s*([^\n]+)/',
							'Source' => '/Source:\s*([^\n]+)/',
							'State Jurisdiction Code' => '/State Jurisdiction Code:\s*([^\n]+)/'
						];
						
						$result = "<p></p><h3>GST Number Information</h3><p>";						
						$result = $this->getDetails($patterns,$responses->response,$result);
						$result .= "</p>";
						$data = [ "pi" =>$result];
						
						echo $this->exitSuccessWithMultiple($data);
					}else{
						if(isset($responses->response)){
							return $this->exitDangerWithValidation($responses->response);
						}else{
							return $this->exitDangerWithValidation("Some issue occured. Please contact admin");
						}						
					}
					
                    
                } catch (Exception $e) {
					return $this->exitDangerWithValidation($e->getMessage());
                }
            }else{
				 return $this->exitDangerWithValidation($this->form_validation->errorArray());
			}
    	}else{
			 return $this->exitDangerWithValidation("Unauthorized Access!");
		}
	}
	
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
}