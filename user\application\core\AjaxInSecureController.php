
<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class AjaxInSecureController extends MY_Controller {

    public function __construct() {
        parent::__construct();
        if ($this->auth->has() === TRUE) {
            $this->viewDataBag->userSession = $this->auth->user();
        } else {
            $this->viewDataBag->userSession = (object) ["id" => 0];
        }
        if (!$this->input->is_ajax_request()) {
            if (getWebsite() != $_SERVER['HTTP_ORIGIN']) {
                echo json_encode([
                    'status' => (bool) false,
                    'code' => (string) "SAE",
                    'error' => 'The user might not have the necessary permissions for a resource.',
                ]);
                exit;
            } else {
                echo json_encode([
                    'status' => (bool) false,
                    'code' => (string) "SAE",
                    'error' => 'The action you have requested is not allowed.',
                ]);
                exit;
            } return;
        }
    }

    protected function exitSuccessWithMultiple($data = []) {
        echo json_encode([
            'status' => (bool) true,
            'data' => (array) $data,
        ]);
        exit;
    }

    protected function exitSuccessWithOne($data = []) {
        if (is_object($data)) {
            $data = (array) $data;
        }
        $data['status'] = (bool) true;
        $data['csrf'] = (array) csrfArray();
        echo json_encode($data);
        exit;
    }

    protected function exitSuccess($message = null) {
        echo json_encode([
            'status' => (bool) true,
            'message' => (string) (($message != '') ? $message : 'OK'),
        ]);
        exit;
    }

    protected function exitDanger($error = null) {
        echo json_encode([
            'status' => (bool) false,
            'code' => (string) NULL,
            'error' => (string) (($error != '') ? $error : 'Oh snap! Something went wrong.'),
        ]);
        exit;
    }

    protected function exitDangerWithOne($error = []) {
        echo json_encode([
            'status' => (bool) false,
            'code' => (string) "UVE",
            'error' => (array) $error,
        ]);
        exit;
    }


    protected function exitDangerWithValidation($error = [])
    {
        echo json_encode([
            'status' => (bool)false,
            'errorCode' => (string)"UVE",
            'error' => (array)$error,
        ]);
        exit;
    }
    protected function exitOnly($response) {
      echo json_encode($response);
        exit;
    }
}
