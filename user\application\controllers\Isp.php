<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Isp extends SecureController
{

    function __construct()
    {
        parent::__construct();
		$res = accessModule('ip_grabber');
        if (strtotime($this->viewDataBag->user->expiryDate) < strtotime(currentDateTime()) || empty($res) ) {
           return redirectTo("me/dashboard");
        }
    }
    function details()
    {
        $this->viewDataBag->scriptLink = [base_url("public/assets/js/plugin/lib.js"),
            base_url("public/assets/js/plugin/isp/search.js")];
        $this->viewDataBag->jsView = 'isp/jsView';
        $this->loadView('isp/detailView', $this->viewDataBag);
    }
}
